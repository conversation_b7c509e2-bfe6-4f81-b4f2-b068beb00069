package com.xiaomi.dayu.service;

import com.xiaomi.dayu.DayuApplication;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.constants.ApproveTypeEnum;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.approval.ApprovalVO;
import com.xiaomi.dayu.mybatis.entity.Approval;
import com.xiaomi.dayu.mybatis.example.ApprovalExample;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Type ApprovalServiceImplTest.java
 * @Desc
 * @date 2024/11/7 14:02
 */
@Slf4j
@SpringBootTest(classes = DayuApplication.class)
    /**
     * ApprovalServiceImplTest类用于测试ApprovalService的各项功能。
     *
     * 该类包含多个测试方法，用于验证ApprovalService的不同操作是否正常工作。
     * 测试方法包括分页查询、更新记录、通过主键查询、根据类型和关联键查询、创建审批记录以及更新状态等。
     *
     * 通过这些测试方法，可以确保ApprovalService的功能在各种情况下都能正常运行。
     *
     * 使用了SpringBootTest注解来加载Spring应用上下文，并使用@Resource注解注入ApprovalService实例。
     *
     * 测试方法使用了JUnit的@Test注解，并包含了各种断言来验证方法的正确性。
     */
public class ApprovalServiceImplTest {

	@Resource
	private ApprovalService approvalService;


    /**
     * 测试分页查询审批记录的方法
     *
     * 测试通过调用 approvalService 的 queryByPage 方法，传入应用名称、审批类型、状态、页码和每页大小，
     * 并断言返回结果不为空。
     */
	@Test
    public void testQueryByPage() {
        // 设置应用名称
        String appName = "testApp";
        // 设置审批类型
        Integer approveType = 1;
        // 设置审批状态
        Integer status = 1;
        // 设置页码
        Integer pageNum = 1;
        // 设置每页大小
        Integer pageSize = 10;
        // 调用approvalService的queryByPage方法进行分页查询，并获取返回结果
        PageResult<ApprovalVO> result = approvalService.queryByPage(appName, approveType, status, pageNum, pageSize);
        // 断言返回结果不为空，表示查询成功
        assertNotNull(result);

    }


    /**
     * 测试ApprovalService的update方法
     *
     * 创建一个Approval对象和一个ApprovalExample对象，并调用approvalService的update方法进行更新操作。
     * 最后，断言更新操作的结果为true。
     */
	@Test
    public void testUpdate() {
        // 创建一个 Approval 对象，用于更新数据库中的记录
        Approval approval = new Approval();
        // 创建一个 ApprovalExample 对象，用于构建更新条件
        ApprovalExample example = new ApprovalExample();
        // 调用 approvalService 的 update 方法，传入 approval 对象和 example 对象
        boolean result = approvalService.update(approval, example);
        // 断言更新操作的结果为 true，表示更新成功
        assertTrue(result);

    }



	/**
     * 测试通过主键查询审批记录的方法
     *
     * 测试内容包括：
     * 1. 通过主键ID查询审批记录
     * 2. 验证查询结果不为空
     * 3. 验证查询结果的ID与预期一致
     */
	@Test
    public void testSelectByPrimaryKey() {
        // 设置审批记录的ID
        Integer id = 1;
        // 根据ID查询审批记录
        Approval approval = approvalService.selectByPrimaryKey(id);
        // 断言查询结果不为空
        assertNotNull(approval);
        // 断言查询结果的ID与预期一致
        assertEquals(id, approval.getId());

    }


    /**
     * 测试根据审批类型和关联键查询审批记录的方法
     *
     * 该方法测试了通过审批类型和关联键查询审批记录的功能。
     * 它首先设置了审批类型和关联键，然后调用 `approvalService.queryByTypeAndRelateKey` 方法进行查询，
     * 最后通过断言检查查询结果不为空且包含至少一个审批记录。
     */
	@Test
    public void testQueryByTypeAndRelateKey() {
                // 设置审批类型为 NACOS_CONFIG
        ApproveTypeEnum approveType = ApproveTypeEnum.NACOS_CONFIG; // Replace with actual enum value
        // 设置关联键为 someRelateKey
        String relateKey = "someRelateKey";
        // 根据审批类型和关联键查询审批记录
        List<Approval> approvals = approvalService.queryByTypeAndRelateKey(approveType, relateKey);
        // 断言查询结果不为空
        assertNotNull(approvals);
        // 断言查询结果数量大于 0
        assertTrue(approvals.size() > 0);

    }



	/**
     * 测试创建审批的功能
     *
     * 该方法创建一个PublishConfigReq对象和一个操作类型为CREATE的枚举值，
     * 然后调用approvalService的createApproval方法进行审批创建。
     */
	@Test
    public void testCreateApproval() {
        // 创建一个 PublishConfigReq 对象，用于封装发布配置的请求参数
        PublishConfigReq requestBody = new PublishConfigReq();
        // 创建一个 Enums.OperateType 枚举对象，值为 CREATE，表示操作类型为创建
        Enums.OperateType operateType = Enums.OperateType.CREATE; // Replace with actual enum value
        // 调用 approvalService 的 createApproval 方法，传入请求体和操作类型，创建一个审批
        approvalService.createApproval(requestBody, operateType);

        // Add assertions as needed
    }


    /**
     * 测试更新状态的方法
     *
     * 测试通过调用 approvalService 的 updateStatus 方法，验证其返回结果是否为 true。
     */
	@Test
    public void testUpdateStatus() {
        // 设置审批记录的ID
        Integer id = 1;
        // 设置应用名称
        String appName = "testApp";
        // 设置审批状态
        Integer status = 2;
        // 设置操作备注
        String operateRemark = "Test Remark";
        // 调用approvalService的updateStatus方法更新审批状态，并获取返回结果
        Boolean result = approvalService.updateStatus(id, appName, status, operateRemark);
        // 断言返回结果为true，表示更新成功
        assertTrue(result);

    }













}
