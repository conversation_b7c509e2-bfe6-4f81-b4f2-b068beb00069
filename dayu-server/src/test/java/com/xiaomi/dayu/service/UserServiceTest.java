package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.dayu.common.HttpClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

//@ActiveProfiles("test")
//@SpringBootTest(classes = DubboAdminApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@RunWith(SpringJUnit4ClassRunner.class)
public class UserServiceTest {
    @Autowired
    private UserService userService;
    @Test
    public void queryFullDeptByUid(){

        //1b2c830385324e68baa6a98448109b18  中国区
        //5aa96844f7534c0dac937e52e88a83f2  有品
        String s = userService.queryFullDeptByUid("5aa96844f7534c0dac937e52e88a83f2","lck");
        System.out.println(s);
    }

    @Test
    public void test(){
        String result = HttpClient.queryFullDeptByUid("https://api.id.mioffice.cn", "dayu", "f0ff4822affe4a8585a1f9d7a6d6833f", "1b2c830385324e68baa6a98448109b18");
        if(StringUtils.isNotBlank(result)){
            JSONArray array = (JSONArray)JSON.parseObject(result).get("data");
            if(CollectionUtils.isNotEmpty(array)){
                for(int i=array.size()-1; i>=0 ;i--){
                    int level = ((JSONObject) array.get(i)).getIntValue("level");
                    if(1 == level || 2 == level){
                        String deptName = ((JSONObject) array.get(i)).getString("deptName");
                        if(StringUtils.isNotBlank(deptName)){
                            if("中国区".equals(deptName)){
                                System.out.println("2");
                                return ;
                            }else if(deptName.indexOf("有品")>-1){
                                System.out.println("3");
                                return ;
                            }

                        }
                    }
                }
            }
        }
    }
}
