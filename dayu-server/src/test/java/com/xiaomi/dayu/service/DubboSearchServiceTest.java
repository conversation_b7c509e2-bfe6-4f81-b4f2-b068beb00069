package com.xiaomi.dayu.service;

import com.google.gson.Gson;
import com.xiaomi.dayu.DayuApplication;
import com.xiaomi.dayu.api.bo.DubboSearchReq;
import com.xiaomi.dayu.api.bo.DubboServiceInfoRes;
import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.constants.SideEnum;
import com.xiaomi.dayu.api.service.DubboSearchService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 *
 * @description
 * @version 1.0
 * <AUTHOR>
 * @date 2024/4/15 14:35
 *
 */
@Slf4j
@SpringBootTest(classes = DayuApplication.class)
public class DubboSearchServiceTest {

    @Autowired
    private DubboSearchService dubboSearchService;

    private Gson gson = new Gson();

    @Test
    public void searchServiceTest() {
        DubboSearchReq dubboSearchReq = new DubboSearchReq();
        dubboSearchReq.setSide(SideEnum.provider);
        dubboSearchReq.setServiceName("com.xiaomi.nr.eiam.admin.service.LogAdminService");
        Result<PageResult<DubboServiceInfoRes>> pageResultResult = dubboSearchService.searchService(dubboSearchReq);
        log.info("result:{}", gson.toJson(pageResultResult));
    }
}
