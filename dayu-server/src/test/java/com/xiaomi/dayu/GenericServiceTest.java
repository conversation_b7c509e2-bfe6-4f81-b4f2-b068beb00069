package com.xiaomi.dayu;

import com.alibaba.fastjson.JSON;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.rpc.service.GenericService;
import org.assertj.core.util.Lists;

import java.util.HashMap;
import java.util.concurrent.CountDownLatch;

public class GenericServiceTest {
    public static void main(String[] args) {

        RegistryConfig registryConfig = new RegistryConfig();
        registryConfig.setAddress("nacos://nacos.test.b2c.srv:80");

       // String address="dubbo://************:20880";
        String interfaceName ="com.xiaomi.dayu.api.service.NacosConfigService";
        String methodName="getOrderInfoByVid";
        String group="dev_lck";
        String version="1.0";
        String[] paramTypes={"java.lang.String"};
        HashMap<Object, Object> map = new HashMap<>();
//        map.put("id",1);
//        map.put("aaa","aaa");
        Object[] paramValues = { "XIAQW6R8FN0GKU379"};
        ApplicationConfig applicationConfig = new ApplicationConfig();
        applicationConfig.setName("test1");
        ReferenceConfig<GenericService> reference = new ReferenceConfig<>();
        //providers:com.xiaomi.cnzone.proretail.atlas.api.provider.DemoProvider::staging
        reference.setGeneric("true");
        reference.setApplication(applicationConfig);
        reference.setInterface(interfaceName);
        reference.setGroup(group);
        reference.setVersion(version);
      //  reference.setUrl(address);
        reference.setTimeout(3000);
        reference.setRegistries(Lists.newArrayList(registryConfig));

        GenericService genericService = reference.get();
        Object o = genericService.$invoke(methodName, paramTypes, paramValues);

        reference.destroy();

        CountDownLatch countDownLatch = new CountDownLatch(1);

        System.err.println(JSON.toJSONString(o));
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }
}
