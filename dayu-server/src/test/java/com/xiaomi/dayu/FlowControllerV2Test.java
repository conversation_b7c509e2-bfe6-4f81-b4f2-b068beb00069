package com.xiaomi.dayu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.controller.sentinel.FlowControllerV2;
import com.xiaomi.dayu.model.sentinel.FlowRuleEntity;
import com.xiaomi.dayu.model.sentinel.FlowRuleInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

import static org.jasig.cas.client.util.CommonUtils.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @Type FlowControllerV2Test.java
 * @Desc
 * @date 2024/12/3 19:22
 */
public class FlowControllerV2Test {
	@Resource
	private FlowControllerV2 flowControllerV2;


	/**
	 * 测试apiQueryMachineRules方法
	 * <p>
	 * 模拟一个HttpServletRequest请求，并设置一个应用名称"testApp"。
	 * 使用mock的HttpClient.sentinelProxyHttp方法返回一个包含测试数据的ResultResponse对象。
	 * 调用flowControllerV2的apiQueryMachineRules方法，并对返回的ResultResponse对象进行一系列断言：
	 * - 确认返回的ResultResponse对象不为空
	 * - 确认返回的ResultResponse对象的isSuccess方法返回true
	 * - 确认返回的ResultResponse对象的数据不为空
	 * - 确认返回的数据转换为List<FlowRuleInfo>对象后，列表大小为1
	 * - 确认列表中的第一个FlowRuleInfo对象的resource属性为"testResource"
	 * - 确认列表中的第一个FlowRuleInfo对象的method属性为"GET"
	 */
	@Test
	public void testApiQueryMachineRules() {
		HttpServletRequest request = mock(HttpServletRequest.class);
		String app = "testApp";
		ResultResponse<Object> mockResponse = new ResultResponse<>();
		mockResponse.setData("[{\"resource\":\"testResource\",\"method\":\"GET\"}]");

		when(HttpClient.sentinelProxyHttp(request)).thenReturn(mockResponse);

		ResultResponse<Object> response = flowControllerV2.apiQueryMachineRules(request, app);

		assertNotNull(response, "");
		assertTrue(response.isSuccess());
		assertNotNull(response.getData(), "");
		List<FlowRuleInfo> flowRuleInfos = (List<FlowRuleInfo>) response.getData();
		assertEquals(1, flowRuleInfos.size());
		assertEquals("testResource", flowRuleInfos.get(0).getResource());
		assertEquals("GET", flowRuleInfos.get(0).getMethod());
	}


	/**
	 * 测试apiAddFlowRule方法
	 * <p>
	 * 模拟一个HttpServletRequest对象，并设置相关参数和返回值，调用flowControllerV2的apiAddFlowRule方法，
	 * 验证当方法已存在时是否抛出ParamValidationException异常并检查异常信息。
	 */
	@Test
	public void testApiAddFlowRule() {
		HttpServletRequest request = mock(HttpServletRequest.class);
		String app = "testApp";
		String service = "testService";
		List<String> methodList = new ArrayList<>();
		methodList.add("testMethod");
		FlowRuleEntity flowRuleEntity = new FlowRuleEntity();

		ResultResponse<Object> mockResponse = new ResultResponse<>();
		mockResponse.setData("[{\"resource\":\"testService:testMethod\"}]");

		when(HttpClient.sentinelProxyHttp(request, "v2/flow/rules", "GET", null, new HashMap<String, String>() {{
			put("app", app);
		}})).thenReturn(mockResponse);

		try {
			flowControllerV2.apiAddFlowRule(request, app, service, methodList, null, null, flowRuleEntity);
		} catch (ParamValidationException e) {
			assertEquals("testMethod已经存在不允许新增！", e.getMessage());
		}
	}


	@Test
	public void testRules() {
		String data ="[{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10317,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":60.0,\"gmtCreate\":1742472312493,\"gmtModified\":1744033601309,\"grade\":1,\"id\":10317,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarClueProvider:getClueAgg\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10318,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":60.0,\"gmtCreate\":1742472449953,\"gmtModified\":1744033602976,\"grade\":1,\"id\":10318,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarClueProvider:getClueFunnelAgg\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10319,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":140.0,\"gmtCreate\":1742472567089,\"gmtModified\":1744033604967,\"grade\":1,\"id\":10319,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarClueProvider:getClueStaffAgg\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10328,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":60.0,\"gmtCreate\":1743679927010,\"gmtModified\":1743679927010,\"grade\":1,\"id\":10328,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarAftermarketProvider:getIncomeByServiceAgg\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10329,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":100.0,\"gmtCreate\":1743679975929,\"gmtModified\":1744033608768,\"grade\":1,\"id\":10329,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarAftermarketProvider:getIncomeAgg\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10331,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":60.0,\"gmtCreate\":1743680048641,\"gmtModified\":1743680048641,\"grade\":1,\"id\":10331,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarSaleOrdProvider:getSaleOrdAgg\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10332,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":60.0,\"gmtCreate\":1743680083766,\"gmtModified\":1743680083766,\"grade\":1,\"id\":10332,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarSaleOrdProvider:getSaleOrdStaffAgg\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10333,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":18.0,\"gmtCreate\":1744012221025,\"gmtModified\":1744033611653,\"grade\":1,\"id\":10333,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carMonthSaleManage\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10342,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744015276221,\"gmtModified\":1744030969133,\"grade\":1,\"id\":10342,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarDeliveryProvider:orderManagement\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10343,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744015399709,\"gmtModified\":1744030977501,\"grade\":0,\"id\":10343,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarDeliveryProvider:receptionAchievement\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10344,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744015541051,\"gmtModified\":1744030980888,\"grade\":1,\"id\":10344,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarDeliveryProvider:stock\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10345,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744015631283,\"gmtModified\":1744030955913,\"grade\":1,\"id\":10345,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarDeliveryProvider:carManagementV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10346,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744015716560,\"gmtModified\":1744030964026,\"grade\":1,\"id\":10346,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarDeliveryProvider:guaranteeAchievement\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10283,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":30.0,\"gmtCreate\":1735890382920,\"gmtModified\":1744033614633,\"grade\":1,\"id\":10283,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarClueProvider:downClue\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10347,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":22.0,\"gmtCreate\":1744015981412,\"gmtModified\":1744028855335,\"grade\":1,\"id\":10347,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarSalesProvider:managerTeamSearchKey\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10284,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":46.0,\"gmtCreate\":1736248064398,\"gmtModified\":1744033616504,\"grade\":1,\"id\":10284,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarSalesProvider:assistantOverview\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10348,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744016141120,\"gmtModified\":1744030973292,\"grade\":1,\"id\":10348,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarDeliveryProvider:overview\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10285,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":38.0,\"gmtCreate\":1736248157211,\"gmtModified\":1744033618595,\"grade\":1,\"id\":10285,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carClueTransferV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10349,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744016334137,\"gmtModified\":1744030297595,\"grade\":1,\"id\":10349,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarLtcProvider:positionBuild\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10286,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":26.0,\"gmtCreate\":1736248205002,\"gmtModified\":1744033621000,\"grade\":1,\"id\":10286,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carModelOverview\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10288,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":30.0,\"gmtCreate\":1736248297475,\"gmtModified\":1744030315990,\"grade\":1,\"id\":10288,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarPadServiceProvider:carPadQuotaDetailV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10289,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":28.0,\"gmtCreate\":1736248349281,\"gmtModified\":1744033624070,\"grade\":1,\"id\":10289,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carSaleShopOverviewV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10290,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":30.0,\"gmtCreate\":1736248376045,\"gmtModified\":1744033627554,\"grade\":1,\"id\":10290,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarClueProvider:crm\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10291,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":30.0,\"gmtCreate\":1736248402765,\"gmtModified\":1744033631810,\"grade\":1,\"id\":10291,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarSalesProvider:managerTeamV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10355,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":40.0,\"gmtCreate\":1744026251016,\"gmtModified\":1744033634206,\"grade\":1,\"id\":10355,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarSalesProvider:monthlyAchievementCard\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10292,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":30.0,\"gmtCreate\":1736248438529,\"gmtModified\":1744033635863,\"grade\":1,\"id\":10292,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarAftermarketProvider:serviceOverviewPadV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10356,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":30.0,\"gmtCreate\":1744026632281,\"gmtModified\":1744026632281,\"grade\":1,\"id\":10356,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarQuotaDetailV2Provider:carQuotaDetailV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10357,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":100.0,\"gmtCreate\":1744028444417,\"gmtModified\":1744028444417,\"grade\":1,\"id\":10357,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.service.pds.api.provider.CarAftermarketProvider:getOrderCount\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10359,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744029355262,\"gmtModified\":1744029355262,\"grade\":1,\"id\":10359,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carDataBriefing\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10361,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744029731696,\"gmtModified\":1744029731696,\"grade\":1,\"id\":10361,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carDeliveryOverview\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10362,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744029862185,\"gmtModified\":1744029862185,\"grade\":1,\"id\":10362,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carAfterSaleOverviewV2\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10363,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744030006989,\"gmtModified\":1744030046700,\"grade\":1,\"id\":10363,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carManageOverview\",\"strategy\":0,\"warmUpPeriodSec\":0},{\"app\":\"proretail-bi\",\"clusterConfig\":{\"acquireRefuseStrategy\":0,\"clientOfflineTime\":2000,\"fallbackToLocalWhenFail\":true,\"flowId\":10364,\"resourceTimeout\":2000,\"resourceTimeoutStrategy\":0,\"sampleCount\":10,\"strategy\":0,\"thresholdType\":1,\"windowIntervalMs\":1000},\"clusterMode\":true,\"controlBehavior\":0,\"count\":10.0,\"gmtCreate\":1744030169389,\"gmtModified\":1744030195788,\"grade\":1,\"id\":10364,\"isClose\":1,\"limitApp\":\"default\",\"maxQueueingTimeMs\":0,\"resource\":\"com.xiaomi.cnzone.proretail.bi.api.provider.car.CarCountryProvider:carSaleOverview\",\"strategy\":0,\"warmUpPeriodSec\":0}]";
		List<FlowRuleEntity> flowRuleEntities = JSON.parseObject(data, new TypeReference<List<FlowRuleEntity>>() {
		});
		if(CollectionUtils.isNotEmpty(flowRuleEntities)){

			List<FlowRuleInfo> flowRuleInfos = new ArrayList<>(flowRuleEntities.size());
			flowRuleEntities.forEach(entity->{
				FlowRuleInfo flowRuleInfo = buildFlowRuleInfo(entity);
				flowRuleInfos.add(flowRuleInfo);
			});
			flowRuleInfos.sort(new Comparator<FlowRuleInfo>() {
				@Override
				public int compare(FlowRuleInfo o1, FlowRuleInfo o2) {

					if (o1.equals(o2)) return 0;

					// 2. 比较 service
					int serviceCompare = o1.getService().compareTo(o2.getService());
					if (serviceCompare != 0) {
						return serviceCompare; // 按 service 升序
					}

					// 3. 比较 method（处理空值）
					if (StringUtils.isBlank(o1.getMethod())) {
						return StringUtils.isBlank(o2.getMethod()) ? 0 : -1; // o1.method 空时排前
					}
					if (StringUtils.isBlank(o2.getMethod())) {
						return 1; // o2.method 空时排后
					}

					// 4. 按 method 升序
					return o1.getMethod().compareTo(o2.getMethod());
				}
			});
			System.out.println(flowRuleInfos);
		}
	}
	private FlowRuleInfo buildFlowRuleInfo(FlowRuleEntity flowRuleEntity) {
		FlowRuleInfo flowRuleInfo = new FlowRuleInfo();
		BeanUtils.copyProperties(flowRuleEntity,flowRuleInfo);

		fillEntity(flowRuleInfo);
		if(StringUtils.isNotBlank(flowRuleEntity.getDefaultFallbackMethod())){
			String[] split1 = flowRuleEntity.getDefaultFallbackMethod().split(Constants.COLON);
			flowRuleInfo.setFallbackClass(split1[0]);
			flowRuleInfo.setFallbackMethod(split1[1]);
		}
		return flowRuleInfo;
	}
	public static void fillEntity(FlowRuleInfo entity) {
		String service =null;
		String method =null;
		String dubboGroup =null;
		String dubboVersion =null;
		String tmp =null;
		String[] splitWell = entity.getResource().split(Constants.WELL);
		if(splitWell.length>1){
			dubboGroup = splitWell[0];
			tmp = splitWell[1];
		}else{
			tmp = splitWell[0];
		}
		String[] splitColon = tmp.split(Constants.COLON);
		if(splitColon.length>1) {
			service = splitColon[0];
			tmp = splitColon[1];
		}else{
			tmp = splitColon[0];
		}
		String[] splitEit = tmp.split(Constants.EIT);
		if(splitEit.length>1) {
			dubboVersion = splitEit[1];
		}
		if(service==null){
			service = splitEit[0];
		}else{
			method = splitEit[0];
		}
		entity.setService(service);
		entity.setMethod(method);
		entity.setDubboGroup(dubboGroup);
		entity.setDubboVersion(dubboVersion);
	}


}
