package com.xiaomi.dayu.rpc;

import com.xiaomi.dayu.AbstractSpringIntegrationTest;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class AccountServiceRpcTest extends AbstractSpringIntegrationTest {
    @Autowired
    private AccountServiceRpc accountServiceRpc;
    @Test
    public void queryApplicationNamesByUsername(){
        accountServiceRpc.queryApplicationNamesByUsername("liuchuankang");
    }
    @Test
    public void queryUsersByAppName(){
        List<UserInfoResult> userInfoResults = accountServiceRpc.queryUsersByAppName("dayu");
        System.err.println(userInfoResults);

    }
}
