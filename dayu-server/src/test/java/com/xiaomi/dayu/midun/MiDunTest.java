package com.xiaomi.dayu.midun;


import com.xiaomi.aegis.utils.AegisSignUtil;
import com.xiaomi.aegis.utils.CommonUtil;

import java.security.SignatureException;

public class MiDunTest {
    public static void main(String[] args) throws SignatureException {
        String signData="cb7tIc4uosk3A4J5+sokWqJm6orNd71dwk0UHgvIXeIRAlaEWOLYKrZbfM6VMRwjbWvqCuaklHakg+wmSvZGuJTrWeZOMkybO149MY8XG87G/7EfDiyLiHVAt2NvAwIO0caUvTlS9jIlAEy5WpQP1Neurew3E1fQZ+XQgNvtMCo=#MTY3OTU1NDk0Ni4yMTM=";
        String[] keys= new String[]{"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC6NlYAKz1wprDJo0F+zW17vbnNWm40W33c6Gp3EgIoG+T9Kaz3yl7BXyd8qQ2jNPEMTZvteitQLVsiQ1r/D/5GhbKRn1k2O8tUmgZ9QqRGHhvsihSPXwwXjZuOlfRai7OxSrz26oU2WAyvEhm6oWoVzNewuMf076JWJFw9Fjh2IQIDAQAB",
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDsaAeU9gFLGVyAg6uzRl3lGv9XTrSxlkNoxT9olYjwEpICuvKw5iPFeKMb6ZUk7FuQ50tHCi1vQ59Um/o9WjgQ/JNCBOwCRflDHCuxPydU/4ozMXhuzrNoI0KIKZQ0wLN/4XHLJyv46PFXdURL4X9kbsNrqPtka1DLcAQkwaZ8kQIDAQAB",
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDfpdbKf/LMTg1RWYfHqxd5ApNp/I2Qk2mrHDNlFuzgRC9zEKiOhPqX9kxtowo7hZMHSPssQUPmUmIymPoTpfYPrEWIeDqCOUDUrpL9cGf/4U4g0CA8LyIFGGtL/mNwBvz0rARKa3G4+qvbedEKfoVNck+T/0nh2Bok97cuzfyhjQIDAQAB"};

        String currentUsePublicKey= null;
        String verifyIdentityData= null;
        for (int i=0;i++<keys.length;i++) {
            verifyIdentityData = AegisSignUtil.verifySignGetInfo(signData, keys[i]);
            if (CommonUtil.isNotEmpty(verifyIdentityData)) {
                System.err.println(i);
                break;
            }
        }
    }
}