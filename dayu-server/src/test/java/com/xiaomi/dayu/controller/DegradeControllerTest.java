package com.xiaomi.dayu.controller;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.model.sentinel.DegradeRuleEntity;
import org.junit.Test;

import static com.xiaomi.dayu.controller.sentinel.DegradeController.fillEntity;

/**
 * <AUTHOR>
 * @Type DegradeControllerTest.java
 * @Desc
 * @date 2025/2/18 16:59
 */
public class DegradeControllerTest {
	@Test
	public  void test() {
		DegradeRuleEntity degradeRuleEntity1 = new DegradeRuleEntity();
		degradeRuleEntity1.setResource("china_dayu#swimlane.service.TagRulesInfoService:getTagRuleInfoList@1.0");
		fillEntity(degradeRuleEntity1);
		System.out.println(JSON.toJSONString(degradeRuleEntity1));
		DegradeRuleEntity degradeRuleEntity2 = new DegradeRuleEntity();
		degradeRuleEntity2.setResource("china_dayu#swimlane.service.TagRulesInfoService@1.0");
		fillEntity(degradeRuleEntity2);
		System.out.println(JSON.toJSONString(degradeRuleEntity2));
		DegradeRuleEntity degradeRuleEntity3 = new DegradeRuleEntity();
		degradeRuleEntity3.setResource("swimlane.service.TagRulesInfoService:getTagRuleInfoList@1.0");
		fillEntity(degradeRuleEntity3);
		System.out.println(JSON.toJSONString(degradeRuleEntity3));
		DegradeRuleEntity degradeRuleEntity4 = new DegradeRuleEntity();
		degradeRuleEntity4.setResource("china_dayu#swimlane.service.TagRulesInfoService:getTagRuleInfoList");
		fillEntity(degradeRuleEntity4);
		System.out.println(JSON.toJSONString(degradeRuleEntity4));
		DegradeRuleEntity degradeRuleEntity5 = new DegradeRuleEntity();
		degradeRuleEntity5.setResource("swimlane.service.TagRulesInfoService:getTagRuleInfoList");
		fillEntity(degradeRuleEntity5);
		System.out.println(JSON.toJSONString(degradeRuleEntity5));
	}
}
