package com.xiaomi.dayu.common.util;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.interfaces.Success;
import com.xiaomi.mone.current.utils.HashUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;

import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;

import static com.xiaomi.dayu.common.util.RetryUtil.callWithRetries;

/**
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @since 2022/1/14
 */
@Ignore
@Slf4j
class RetryUtilTest {

    @Test
    void callWithRetriesTest() {
        AtomicInteger ai = new AtomicInteger();
        ResultResponse<Object> resp = new ResultResponse<>();
        resp.setSuccess(false);
        int maxTries = 10;

        Callable<Success> callable = () -> {
            if (ai.incrementAndGet() == 4) {
                resp.setSuccess(true);
            }
            return resp;
        };
        callWithRetries(callable, maxTries, log, "RetryUtilTest callWithRetriesTest ", 2000L);
    }

    @Test
    void callWithRetriesTest2() {
        System.out.println(Math.abs(HashUtil.apHash("10.220.143.103")) % 500 + 1);
    }
}
