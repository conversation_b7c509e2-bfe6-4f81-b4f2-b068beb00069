package com.xiaomi.dayu;

import com.xiaomi.dayu.service.NamingInstanceService;

import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;

public class DemoApplication {
 
 public static void main(String[] args) {


  try {
   URL[] array= {new URL("file:/D:/workspace/dayu/dayu-server/target/dayu-server-0.3.0-SNAPSHOT.jar")};
   URLClassLoader urlClassLoader = new URLClassLoader(array);
    Class<?> clazz = urlClassLoader.loadClass("com.xiaomi.dayu.service.NamingInstanceService");
   NamingInstanceService o = (NamingInstanceService)clazz.newInstance();
   Boolean isQueryNaming4DB = o.getIsQueryNaming4DB();

   System.out.println(urlClassLoader);
  } catch (MalformedURLException e) {
   System.out.println();
  }catch (ClassNotFoundException e) {
   e.printStackTrace();
  }catch (InstantiationException e) {
   e.printStackTrace();
  } catch (IllegalAccessException e) {
   e.printStackTrace();
  }

  for (int i = 0; i < Integer.MAX_VALUE; i++) {
   System.out.println("times:" + i + " , result:" + testExceptionTrunc());
  }
 }
 
 public static boolean testExceptionTrunc()  {
  try {
   // 人工构造异常抛出的场景
   ((Object)null).getClass();
  } catch (Exception e) {
   if (e.getStackTrace().length == 0) {
    try {
     // 堆栈消失的时候当前线程休眠5秒，便于观察
     Thread.sleep(5000);
    } catch (InterruptedException interruptedException) {
     // do nothing
    }
    return true;
   }
  }
  return false;
 }
}