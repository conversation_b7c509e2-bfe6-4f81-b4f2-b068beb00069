package com.xiaomi.dayu.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Optional;
import java.util.regex.Pattern;

import static com.xiaomi.dayu.common.util.SerializeUtils.getText;

/**
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @since 2022/1/7
 */
@Ignore
public class SerializeUtilTest {

    @Test
    public void testTextNull() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonStr = "{\"appName\": null}";
        JsonNode jsonObject = objectMapper.readTree(jsonStr);
        Optional<String> appName = getText(jsonObject, "appName");
        System.out.println(appName.orElse("empty"));

        Optional<String> test = getText(jsonObject, "test");
        System.out.println(test.orElse("empty"));
    }
    @Test
    public void test2(){
            String str=":*:*";
            String act="rw";
            String permissionResource = str.replaceAll("\\*", ".*");
            String permissionAction = act;
        boolean matches = Pattern.matches(permissionResource, str);
        System.out.println(matches);
    }
}
