package com.xiaomi.dayu.mybatis;

import com.xiaomi.dayu.dao.ApprovalMapper;
import com.xiaomi.dayu.mybatis.entity.Approval;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;

import java.io.IOException;
import java.io.InputStream;

public class SqlSessionFactoryUtil {
    public static void main(String[] args) {
        InputStream resourceAsStream;
        try {
             resourceAsStream = Resources.getResourceAsStream("mybatis/mybatis-config-test.xml");
            SqlSessionFactoryBuilder sqlSessionFactoryBuilder = new SqlSessionFactoryBuilder();
            SqlSessionFactory sqlSessionFactory = sqlSessionFactoryBuilder.build(resourceAsStream);
            SqlSession sqlSession = sqlSessionFactory.openSession();
            ApprovalMapper mapper = sqlSession.getMapper(ApprovalMapper.class);
            Approval approval = mapper.selectByPrimaryKey(100);
            sqlSession.commit();
        } catch (IOException e) {
            e.printStackTrace();
        }


    }
}
