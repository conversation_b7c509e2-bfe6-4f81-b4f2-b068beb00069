
import com.alibaba.nacos.common.utils.HttpMethod;
import com.google.common.collect.Lists;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.util.AESEncryptUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IauthTest {
    @Test
    public void test1(){
        String url ="http://10.38.164.228:30082/api/service/list";
        List<String> headers = new ArrayList<>();
        headers.add("Authorization");
        headers.add("cloud-manager/1.0 {\"identifier\":{\"name\":\"liuyaoli\",\"id\":1344,\"type\":\"user\"},\"roles\":[]}");
        headers.add("Content-Type");
        headers.add("application/json; charset=utf-8");
        Map<String, String> paramValues= new HashMap<String,String>();
        paramValues.put("platform","fusion-iauth");
        String body= null;
        String encoding= HttpClient.UTF_8;
        String method= HttpMethod.GET;
        HttpClient.HttpResult httpResult = HttpClient.request(url, headers, paramValues, body, encoding, method);
        System.out.println(httpResult);
    }
    @Test
    public void iath(){
        ArrayList<String> list = Lists.newArrayList(
                "120229-counting",
                "90567-yp-dy",
                "300243-my-test-project",
                "120518-planet-invitation-code",
                "330244-test-web",
                "120508-tag-center",
                "90617-mi-home-api");
        list.sort(null);
        System.out.println(list);

        String str="Authorization";
        String encryp = AESEncryptUtils.encrypt(str);
        System.out.println(AESEncryptUtils.decrypt(encryp));
        String encrypIauth = AESEncryptUtils.encryptIauth(str+"test");
        System.out.println(AESEncryptUtils.decryptIauth(encrypIauth));
    }
}
