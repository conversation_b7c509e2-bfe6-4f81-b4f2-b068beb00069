package com.xiaomi.dayu.dao;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xiaomi.dayu.AbstractSpringIntegrationTest;
import com.xiaomi.dayu.common.ConfigTypeEnum;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ConfigInfoMapperTest extends AbstractSpringIntegrationTest {
    @Autowired
    private ConfigInfoMapper configInfoMapper;
    @Test
    public void testFuzzySearchConfig() {
        List<String> appNameList = Lists.newArrayList("dubbo_admin");
        String configName= ConfigTypeEnum.Overrides.getConfigName();
        List<ConfigInfo> configInfos = configInfoMapper.fuzzySearchConfig(appNameList,configName);
        System.out.println(JSON.toJSONString(configInfos));
    }
}