package com.xiaomi.dayu.swimlane;

import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.dayu.common.ParameterCheckerUtil;
import swimlane.bo.ConditionPairBo;
import swimlane.bo.ConditionPairCheckBo;

/**
 * <AUTHOR>
 * @Type SwimLaneTest.java
 * @Desc
 * @date 2025/3/17 15:05
 */
public class SwimLaneTest {
	
	@Test
	public void testParameterCheckerUtil() {
		String condition="{\"conditionPairs\":[{\"paramType\":1,\"paramName\":\"orgId\",\"parseExpr\":\"params.toList()[0]{orgId}|String\",\"oriParseExpr\":\"[0]{orgId}|String\",\"op\":2,\"paramValue\":\"JM002|JM0032|MI0102\"}],\"conditionType\":1}";
		String jsonParam="[{\"orgId\":\"JM0020\"}]";
		ConditionPairCheckBo conditionPairCheckBo = new ConditionPairCheckBo();
		JSONObject parse = JSON.parseObject(condition);
		JSONArray jsonArray = (JSONArray) parse.get("conditionPairs");
		ConditionPairBo conditionPairBo = JSON.parseObject(JSON.toJSONString(jsonArray.get(0)), ConditionPairBo.class);
		conditionPairCheckBo.setConditionPairBo(conditionPairBo);
		conditionPairCheckBo.setJsonParam(jsonParam);
		boolean b = ParameterCheckerUtil.judgeParamSatisfy(conditionPairCheckBo);
		System.err.println(b);
    }
}
