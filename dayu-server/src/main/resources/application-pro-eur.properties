server.type=online
env.name=pro-eur
server.port=8080
dubbo.application.name=dayu

registry.address=eur.nacos.systech.b2c.srv:80


server.casServerLoginUrl=https://castest.mioffice.cn/login
server.casServerUrlPrefix=https://castest.mioffice.cn
server.serverName=https://dayu.be.mi.com
server.service=

dayu.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2yJXNzFdovx+OOsm8xNrDrix/pT+EroKFG+ZV/PF3OAQsqAtFdYM94Hl+DUDMTD1Sv1Dd/s3gqoKXtwDWvf8dsGhXjeAgR3PvVlmw/L6LlSyvBWZDIfoT9D92TkvtBV0lBfL4G1eNH9SIP81IVYMOYAkkAUcM4cXWIXbOCc4ofwIDAQAB
dayu.online.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDPt2tBN/G5m3nF/eJQO59eDOcAUXzYpXYIezzs4075gRjnMlIzdl5zpZSWkdlFgm3mfO2NaJ6sBw2vAiLy4i0iV/cSzgH9QkH9C7qv6xt41efruP70LtiR9PCWow5KkvkNFmlj1hLgCtzwVf/V0BqLHiwwTkgQ9z8OyMeSDRnVPwIDAQAB
mone.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC6NlYAKz1wprDJo0F+zW17vbnNWm40W33c6Gp3EgIoG+T9Kaz3yl7BXyd8qQ2jNPEMTZvteitQLVsiQ1r/D/5GhbKRn1k2O8tUmgZ9QqRGHhvsihSPXwwXjZuOlfRai7OxSrz26oU2WAyvEhm6oWoVzNewuMf076JWJFw9Fjh2IQIDAQAB
mone.online.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5RpCdIh6VoPky1xkjboqDaEQeVYdIkFRWarYCX1V4QXCOW38Xp17RZ99ZD46xKs8gyY7jSo+CoTZRcj6hLv69gk2hSyO8jYbPQL9Bjqe8AYJVytwBG8w93QQX9Bl9tebW97stcEUzI4T0NW1N9i1TI9wJbY/Jr/F9iW5VN3N1zwIDAQAB
miline.staging.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDsaAeU9gFLGVyAg6uzRl3lGv9XTrSxlkNoxT9olYjwEpICuvKw5iPFeKMb6ZUk7FuQ50tHCi1vQ59Um/o9WjgQ/JNCBOwCRflDHCuxPydU/4ozMXhuzrNoI0KIKZQ0wLN/4XHLJyv46PFXdURL4X9kbsNrqPtka1DLcAQkwaZ8kQIDAQAB
miline.online.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDfpdbKf/LMTg1RWYfHqxd5ApNp/I2Qk2mrHDNlFuzgRC9zEKiOhPqX9kxtowo7hZMHSPssQUPmUmIymPoTpfYPrEWIeDqCOUDUrpL9cGf/4U4g0CA8LyIFGGtL/mNwBvz0rARKa3G4+qvbedEKfoVNck+T/0nh2Bok97cuzfyhjQIDAQAB


monitor.url.pattern=https://grafana-mione.be.xiaomi.com/d/projectId/ye-wu-jian-kong-projectId?orgId=1&refresh=30s&from=now-6h&to=now&var-application=projectId&var-instance=
hera.url=https://hera.be.mi.com/project-hera-tracing?service=


umami.url=https://umami.be.mi.com/api/collect
umami.website=663ce3d4-75a5-44b6-82a3-d3d0b10d62ee

swimlane.url=http://swimlane.be.mi.com
umami.swimlane.website=d77ca18b-21a6-4704-a4b0-cf94199d1d5b

nacos.config.data-id=dayu_pro_eur


provider.group=eur
provider.statistics.group=

rpc.hermes.AccountService.group=
rpc.gwdash.OperationLogService.group=intranet
rpc.gwdash.IProjectDeploymentService.group=intranet
rpc.gwdash.IProjectService.group=intranet
rpc.mischedule.dubbo.group=online
ref.gwdash.service.group=intranet
ref.gwdash.service.version=
rpc.swimlane.group=eur_dayu
rpc.gwdash.GwdashApiService.group=intranet
rpc.gwdash.OpenApiService.group=intranet
rpc.tesla.K8sProxyService.group=eur
rpc.mimonitor.AlertGorupFacade.group=intranet
rpc.teslaAuth.service.group=xmnonline


rpc.drizzle.service.group=intranet

rpc.miline.MilogProviderService.version=1.0
rpc.miline.MilogProviderService.group=online
rpc.quota.ResourceService.group=
rpc.tpc.projectFacade.group=online
rpc.tpc.userFacade.group=online

ref.tpc.node.version=1.0
ref.tpc.node.group=online

ref.miline.service.group=online
ref.miline.service.version=1.0
ref.faas.service.group=online
ref.faas.service.version=1.0

rpc.moon.project.group=eur
rpc.moon.project.version=1.0


sentinel.url=http://eur-sentinel.be.mi.com
iauth.url=http://iauth-platform-web.n.xiaomi.net
dayu.url=https://dayu.be.mi.com/

dump.project.url=http://************:80/

arthas.url=http://eur-arthas.be.mi.com

# Redis?????
redis.host=cache01.b2c.srv
# Redis???????
redis.port=5100
# Redis?????????????


depart=CHAIN

arthas.app-name=150488-dayu
arthas.tunnel-server=ws://eur-arthas.be.mi.com/ws
arthas.telnetPort=-1
arthas.httpPort=-1

threadpool.config=find:pro-eur