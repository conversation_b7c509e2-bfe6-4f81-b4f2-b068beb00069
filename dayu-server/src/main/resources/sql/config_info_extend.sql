/*
 Navicat Premium Data Transfer

 Source Server         : nacos-staging
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : ksc.test.mysql01.b2c.srv:3206
 Source Schema         : nacos_standalone

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 20/12/2021 14:41:13
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for config_info_extend
-- ----------------------------
DROP TABLE IF EXISTS `config_info_extend`;
CREATE TABLE `config_info_extend`  (
  `id` bigint(64) NOT NULL AUTO_INCREMENT,
  `config_info_id` bigint(255) NOT NULL COMMENT 'config_info表主键',
  `data_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `env_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '环境evn id',
  `env_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '环境名称',
  `config_type` tinyint(2) NULL DEFAULT NULL COMMENT '配置类型',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_idex_configinfoid`(`config_info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
