<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <!--    <classPathEntry location="/Users/<USER>/Documents/Mi/Projects/mysql-connector-java-8.0.25.jar"/>-->
    <context id="Tables" targetRuntime="MyBatis3" defaultModelType="flat">

        <!--<plugin type="com.xiaomi.data.push.common.PagerPlugin"></plugin>-->
        <plugin type="com.itfsw.mybatis.generator.plugins.BatchInsertPlugin"/>
        <plugin type="com.itfsw.mybatis.generator.plugins.ModelColumnPlugin"/>
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <!-- 注释 -->
        <commentGenerator>
            <!-- 是否生成注释代时间戳 -->
            <property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!-- JDBC连接 -->
<!--        <jdbcConnection
                driverClass="com.mysql.jdbc.Driver"
                connectionURL="***********************************************************"
                userId="nacos_standalone_wn"
                password="QNT45WC47FUOurGj57pZS8TyVcz_WJtn">
        </jdbcConnection>-->
        <jdbcConnection
                driverClass="com.mysql.jdbc.Driver"
                connectionURL="***********************************************************"
                userId="root"
                password="root">
        </jdbcConnection>

        <!-- 非必需，类型处理器，在数据库类型和java类型之间的转换控制-->
        <!-- 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer，为 true时把JDBC DECIMAL 和
         NUMERIC 类型解析为java.math.BigDecimal -->
        <javaTypeResolver>
            <!-- 是否使用bigDecimal， false可自动转化以下类型（Long, Integer, Short, etc.） -->
            <property name="forceBigDecimals" value="false"/>
            <property name="forceIntegers" value="true"/>
        </javaTypeResolver>

        <!-- 生成实体类地址 -->
        <javaModelGenerator targetPackage="com.xiaomi.dayu.mybatis.entity" targetProject="src/main/java">
            <!-- 从数据库返回的值被清理前后的空格 -->
            <property name="trimStrings" value="true"/>
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </javaModelGenerator>

        <!-- 生成mapper xml文件 -->
        <sqlMapGenerator targetPackage="mybatis/mapper" targetProject="src/main/resources/">
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <!-- 生成mapper xml对应Client-->
        <javaClientGenerator targetPackage="com.xiaomi.dayu.dao" targetProject="src/main/java"
                             type="XMLMAPPER">
            <!--<javaClientGenerator targetPackage="com.xiaomi.data.push.dao.mapper" targetProject="/data/workspace/java/web-service/user-profile-push/src/main/java" type="XMLMAPPER">-->
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 配置表信息 -->
        <!-- schema即为数据库名 tableName为对应的数据库表 domainObjectName是要生成的实体类 enable*ByExample
            是否生成 example类 -->

        <!--        <table schema="nacos_standalone" tableName="approval" domainObjectName="Approval">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="approve_type" property="approveType" javaType="java.lang.Integer" />-->
        <!--            <columnOverride column="operate_type" property="operateType" javaType="java.lang.Integer" />-->
        <!--            <columnOverride column="status" property="status" javaType="java.lang.Integer" />-->
        <!--            <columnOverride column="del" property="del" javaType="java.lang.Boolean" />-->
        <!--        </table>-->

        <!--                <table schema="nacos_standalone" tableName="tag_rule_info" domainObjectName="TagRuleInfo">-->
        <!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                </table>-->

        <!--        <table schema="nacos_standalone" tableName="swim_lane" domainObjectName="SwimLane">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <table schema="nacos_config" tableName="sla_manage" domainObjectName="SlaManage">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
        <table schema="nacos_config" tableName="sla_manage_his" domainObjectName="SlaManageHis">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
    </context>
</generatorConfiguration>
