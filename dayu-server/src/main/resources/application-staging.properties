server.type=staging
env.name=staging
server.port=8812

dubbo.application.name=dayu

registry.address=nacos.test.b2c.srv:80
dubbo.registry.username=nacos
dubbo.registry.password=nacos!123

admin.check.authority=false

server.casServerLoginUrl=https://castest.mioffice.cn/login
server.casServerUrlPrefix=https://castest.mioffice.cn

server.serverName=http://dayu.test.mi.com
server.service=

nacos.config.data-id=dayu_staging


dayu.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2yJXNzFdovx+OOsm8xNrDrix/pT+EroKFG+ZV/PF3OAQsqAtFdYM94Hl+DUDMTD1Sv1Dd/s3gqoKXtwDWvf8dsGhXjeAgR3PvVlmw/L6LlSyvBWZDIfoT9D92TkvtBV0lBfL4G1eNH9SIP81IVYMOYAkkAUcM4cXWIXbOCc4ofwIDAQAB
dayu.online.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDPt2tBN/G5m3nF/eJQO59eDOcAUXzYpXYIezzs4075gRjnMlIzdl5zpZSWkdlFgm3mfO2NaJ6sBw2vAiLy4i0iV/cSzgH9QkH9C7qv6xt41efruP70LtiR9PCWow5KkvkNFmlj1hLgCtzwVf/V0BqLHiwwTkgQ9z8OyMeSDRnVPwIDAQAB
mone.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC6NlYAKz1wprDJo0F+zW17vbnNWm40W33c6Gp3EgIoG+T9Kaz3yl7BXyd8qQ2jNPEMTZvteitQLVsiQ1r/D/5GhbKRn1k2O8tUmgZ9QqRGHhvsihSPXwwXjZuOlfRai7OxSrz26oU2WAyvEhm6oWoVzNewuMf076JWJFw9Fjh2IQIDAQAB
mone.online.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5RpCdIh6VoPky1xkjboqDaEQeVYdIkFRWarYCX1V4QXCOW38Xp17RZ99ZD46xKs8gyY7jSo+CoTZRcj6hLv69gk2hSyO8jYbPQL9Bjqe8AYJVytwBG8w93QQX9Bl9tebW97stcEUzI4T0NW1N9i1TI9wJbY/Jr/F9iW5VN3N1zwIDAQAB
miline.staging.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDsaAeU9gFLGVyAg6uzRl3lGv9XTrSxlkNoxT9olYjwEpICuvKw5iPFeKMb6ZUk7FuQ50tHCi1vQ59Um/o9WjgQ/JNCBOwCRflDHCuxPydU/4ozMXhuzrNoI0KIKZQ0wLN/4XHLJyv46PFXdURL4X9kbsNrqPtka1DLcAQkwaZ8kQIDAQAB
miline.online.aegis.sdk.public.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDfpdbKf/LMTg1RWYfHqxd5ApNp/I2Qk2mrHDNlFuzgRC9zEKiOhPqX9kxtowo7hZMHSPssQUPmUmIymPoTpfYPrEWIeDqCOUDUrpL9cGf/4U4g0CA8LyIFGGtL/mNwBvz0rARKa3G4+qvbedEKfoVNck+T/0nh2Bok97cuzfyhjQIDAQAB


dubbo.group=staging
provider.group=staging
provider.statistics.group=


rpc.hermes.AccountService.group=staging
rpc.gwdash.OperationLogService.group=
rpc.gwdash.IProjectService.group=
rpc.mischedule.dubbo.group=staging
rpc.gwdash.IProjectDeploymentService.group=
ref.gwdash.service.group=
rpc.gwdash.GwdashApiService.group=
rpc.swimlane.group=china_dayu
rpc.tagrule.group=china_dayu
rpc.gwdash.OpenApiService.group=
rpc.teslaAuth.service.group=staging

rpc.mimonitor.AlertGorupFacade.group=

ref.miline.service.group=staging_dev
ref.miline.service.version=1.0
ref.faas.service.group=staging
ref.faas.service.version=1.0

rpc.moon.project.group=staging
rpc.moon.project.version=1.0

rpc.drizzle.service.group=staging

rpc.miline.MilogProviderService.version=1.0
rpc.miline.MilogProviderService.group=online
rpc.tesla.K8sProxyService.group=test
rpc.quota.ResourceService.group=

rpc.tpc.projectFacade.group=staging
rpc.tpc.userFacade.group=staging
ref.tpc.node.version=1.0
ref.tpc.node.group=staging

sentinel.url=http://sentinel.test.be.mi.com
iauth.url=http://staging.iauth-platform-web.n.xiaomi.net
#iauth.url=http://*************:10000
dump.project.url=https://mone-dump-operator.test.mi.com/

umami.url=http://umami.test.mi.com/api/collect
umami.website=a51bcae4-ab25-4112-b4ac-9e5756add3cd

swimlane.url=http://swimlane.test.mi.com
umami.swimlane.website=4a329689-749f-4bc1-a476-a01955f2e44b

hera.url=http://hera.be.test.mi.com/project-hera-tracing?service=
monitor.url.pattern=https://grafana-mione.test.mi.com/d/projectId/ye-wu-jian-kong-projectId?orgId=1&refresh=30s&from=now-6h&to=now&var-application=projectId&var-instance=
#provider.debug.url.pattern=http://mi-api.be.mi.com/#/api/apidetail?projectID=120555&apiID=169&apiProtocol=1&tab=test
mit.monitor.url=http://hera.be.test.mi.com/project-target-monitor/application/dash-board?id=projectId&name=projectName&iamTreeId=projectId

logging.level.root=warn

arthas.url=http://************:7776

radar.system.url=
# Redisæå¡å¨å°å
redis.host=wcc.cache01.test.b2c.srv
# Redisæå¡å¨è¿æ¥ç«¯å£
redis.port=22122
# Redisæå¡å¨è¿æ¥å¯ç ï¼é»è®¤ä¸ºç©ºï¼

depart=CHAIN

arthas.app-name=90681-dayu
arthas.tunnel-server=ws://************:7777/ws
#arthas.tunnel-server=ws://127.0.0.1:7777/ws
arthas.telnetPort=-1
arthas.httpPort=-1

threadpool.config=find:test


rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.topic=dayu-nacos-exporter-topic
rocketmq.producer.group=dayu-nacos-exporter
