env.name=${env.name}

dubbo.application.name=dayu
dubbo.registry.address=nacos://${registry.address}

# nacos config, add parameters to url like username=nacos&password=nacos
admin.registry.address=nacos://${registry.address}?group=DEFAULT_GROUP&namespace=public
admin.config-center=nacos://${registry.address}?group=dubbo
admin.metadata-report.address=nacos://${registry.address}?group=dubbo&namespace=public
#namespace used by nacos. (Deprecated it is recommended to use URL to add parameters,will be removed in the future)
admin.registry.namespace=public
admin.config-center.namespace=public
admin.metadata-report.namespace=public

spring.aop.proxy-target-class=true

nacos.config.bootstrap.enable=true
nacos.config.type=properties

--éè¦ä¿®æ¹
admin.config-center.username=${nacos.username}
admin.config-center.password=${nacos.password}

dubbo.registry.username=${nacos.username}
dubbo.registry.password=${nacos.password}

#session timeout, default is one hour
admin.check.sessionTimeoutMilli=3600000


#compress
server.compression.enabled=true
server.compression.mime-types=text/css,text/javascript,application/javascript
server.compression.min-response-size=10240


#{sentinelç¸å³éç½®}
spring.application.name=${dubbo.application.name}
# é¡¹ç®æ³¨åè¿senbtinelæ¯å¦æå è½½
spring.cloud.sentinel.eager=true
# sentinel dashboard éç½®sentinel dashboardçè®¿é®å°å
spring.cloud.sentinel.transport.dashboard=${sentinel.url}
# sentinelå®¢æ·ç«¯æ¥å¿è¾åºç®å½
spring.cloud.sentinel.log.dir=/home/<USER>/log/sentinel/log
#spring.cloud.sentinel.log.dir=/tmp/log/sentinel/log

spring.cloud.sentinel.cluster-enable=true

spring.cloud.sentinel.nacos-server-addr=${registry.address}

# sentinel datasource nacos
#æµæ§è§å
spring.cloud.sentinel.datasource.ds1.nacos.server-addr=${registry.address}
spring.cloud.sentinel.datasource.ds1.nacos.dataId=${dubbo.application.name}-flow-rules
spring.cloud.sentinel.datasource.ds1.nacos.groupId=DEFAULT_GROUP
spring.cloud.sentinel.datasource.ds1.nacos.rule-type=FLOW
#éçº§è§å
spring.cloud.sentinel.datasource.ds2.nacos.server-addr=${registry.address}
spring.cloud.sentinel.datasource.ds2.nacos.dataId=${dubbo.application.name}-degrade-rules
spring.cloud.sentinel.datasource.ds2.nacos.groupId=DEFAULT_GROUP
spring.cloud.sentinel.datasource.ds2.nacos.rule-type=DEGRADE


#================== mysqlæ°æ®åº =====================#
spring.datasource.url=${mysql.datasource.url}
spring.datasource.username=${mysql.datasource.username}
spring.datasource.password=${mysql.datasource.password}
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
#================== mybatis =====================#
#æ å°æä»¶è·¯å¾
mybatis.mapper-locations=classpath:mybatis/mapper/*Mapper.xml
#æå®mybatisçæå
mybatis.type-aliases-package=com.xiaomi.dayu.mybatis.entity
#æå®mybatiséç½®æä»¶è·¯å¾
mybatis.config-location=classpath:mybatis/mybatis-config.xml

idm.base.url=https://api.id.mioffice.cn
idm.app.id=dayu
idm.app.key=f0ff4822affe4a8585a1f9d7a6d6833f

nacos.config.server-addr=${registry.address}
nacos.server-addr=${registry.address}

debug=true

rpc.drizzle.service.group=${rpc.drizzle.service.group}

arthas.url=@arthas.url@

# Redisæå¡å¨å°å
redis.host=@redis.host@
# Redisæå¡å¨è¿æ¥ç«¯å£
redis.port=@redis.port@
# Redisæå¡å¨è¿æ¥å¯ç ï¼é»è®¤ä¸ºç©ºï¼

redis.timeout=30000
# è¿æ¥æ± æå¤§è¿æ¥æ°ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼
redis.maxTotal=30
# è¿æ¥æ± ä¸­çæå¤§ç©ºé²è¿æ¥
redis.maxIdle=10
redis.numTestsPerEvictionRun=1024
redis.timeBetweenEvictionRunsMillis=30000
redis.minEvictableIdleTimeMillis=1800000
redis.softMinEvictableIdleTimeMillis=10000
# è¿æ¥æ± æå¤§é»å¡ç­å¾æ¶é´ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼
redis.maxWaitMillis=1500
redis.testOnBorrow=true
redis.testWhileIdle=true
redis.blockWhenExhausted=false
redis.JmxEnabled=true

server.cas.ignoreUrl=/api/heratbeat/threadpool/register

