<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.NamingInstanceMapper">
  <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.NamingInstance">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="instance_id" jdbcType="VARCHAR" property="instanceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="dubbo_service" jdbcType="VARCHAR" property="dubboService" />
    <result column="dubbo_group" jdbcType="VARCHAR" property="dubboGroup" />
    <result column="dubbo_version" jdbcType="VARCHAR" property="dubboVersion" />
    <result column="full_service" jdbcType="VARCHAR" property="fullService" />
    <result column="application" jdbcType="VARCHAR" property="application" />
    <result column="side" jdbcType="VARCHAR" property="side" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="port" jdbcType="INTEGER" property="port" />
    <result column="weight" jdbcType="DOUBLE" property="weight" />
    <result column="healthy" jdbcType="BIT" property="healthy" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="ephemeral" jdbcType="BIT" property="ephemeral" />
    <result column="cluster_name" jdbcType="VARCHAR" property="clusterName" />
    <result column="namespace_id" jdbcType="VARCHAR" property="namespaceId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="metadata" jdbcType="VARCHAR" property="metadata" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="last_beat_time" jdbcType="TIMESTAMP" property="lastBeatTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del" jdbcType="BIT" property="del" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, instance_id, service_name, dubbo_service, dubbo_group, dubbo_version, full_service, 
    application, side, ip, port, weight, healthy, enabled, ephemeral, cluster_name, namespace_id, 
    group_name, metadata, md5, last_beat_time, create_time, update_time, del
  </sql>
  <sql id="Service_Column_List">
    service_name, dubbo_service, dubbo_group, dubbo_version, full_service,
    application, side, ephemeral, cluster_name, namespace_id, group_name
  </sql>
  <sql id="Not_Blob_Column_List">
    id, instance_id, service_name, dubbo_service, dubbo_group, dubbo_version, full_service,
    application, side, ip, port, weight, healthy, enabled, ephemeral, cluster_name, namespace_id,
    group_name, md5, last_beat_time, create_time, update_time, del
  </sql>

  <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleNotWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Not_Blob_Column_List" />
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from naming_instance
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from naming_instance
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample">
    delete from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaomi.dayu.mybatis.entity.NamingInstance">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into naming_instance (instance_id, service_name, dubbo_service, 
      dubbo_group, dubbo_version, full_service, 
      application, side, ip, 
      port, weight, healthy, enabled, 
      ephemeral, cluster_name, namespace_id, 
      group_name, metadata, md5, 
      last_beat_time, create_time, update_time, 
      del)
    values (#{instanceId,jdbcType=VARCHAR}, #{serviceName,jdbcType=VARCHAR}, #{dubboService,jdbcType=VARCHAR}, 
      #{dubboGroup,jdbcType=VARCHAR}, #{dubboVersion,jdbcType=VARCHAR}, #{fullService,jdbcType=VARCHAR}, 
      #{application,jdbcType=VARCHAR}, #{side,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, 
      #{port,jdbcType=INTEGER}, #{weight,jdbcType=DOUBLE}, #{healthy,jdbcType=BIT}, #{enabled,jdbcType=BIT}, 
      #{ephemeral,jdbcType=BIT}, #{clusterName,jdbcType=VARCHAR}, #{namespaceId,jdbcType=VARCHAR}, 
      #{groupName,jdbcType=VARCHAR}, #{metadata,jdbcType=VARCHAR}, #{md5,jdbcType=VARCHAR}, 
      #{lastBeatTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{del,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaomi.dayu.mybatis.entity.NamingInstance">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into naming_instance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="instanceId != null">
        instance_id,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="dubboService != null">
        dubbo_service,
      </if>
      <if test="dubboGroup != null">
        dubbo_group,
      </if>
      <if test="dubboVersion != null">
        dubbo_version,
      </if>
      <if test="fullService != null">
        full_service,
      </if>
      <if test="application != null">
        application,
      </if>
      <if test="side != null">
        side,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="port != null">
        port,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="healthy != null">
        healthy,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="ephemeral != null">
        ephemeral,
      </if>
      <if test="clusterName != null">
        cluster_name,
      </if>
      <if test="namespaceId != null">
        namespace_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="metadata != null">
        metadata,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="lastBeatTime != null">
        last_beat_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="del != null">
        del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="instanceId != null">
        #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="dubboService != null">
        #{dubboService,jdbcType=VARCHAR},
      </if>
      <if test="dubboGroup != null">
        #{dubboGroup,jdbcType=VARCHAR},
      </if>
      <if test="dubboVersion != null">
        #{dubboVersion,jdbcType=VARCHAR},
      </if>
      <if test="fullService != null">
        #{fullService,jdbcType=VARCHAR},
      </if>
      <if test="application != null">
        #{application,jdbcType=VARCHAR},
      </if>
      <if test="side != null">
        #{side,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="port != null">
        #{port,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DOUBLE},
      </if>
      <if test="healthy != null">
        #{healthy,jdbcType=BIT},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="ephemeral != null">
        #{ephemeral,jdbcType=BIT},
      </if>
      <if test="clusterName != null">
        #{clusterName,jdbcType=VARCHAR},
      </if>
      <if test="namespaceId != null">
        #{namespaceId,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="metadata != null">
        #{metadata,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="lastBeatTime != null">
        #{lastBeatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultType="java.lang.Long">
    select count(*) from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update naming_instance
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.instanceId != null">
        instance_id = #{record.instanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceName != null">
        service_name = #{record.serviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.dubboService != null">
        dubbo_service = #{record.dubboService,jdbcType=VARCHAR},
      </if>
      <if test="record.dubboGroup != null">
        dubbo_group = #{record.dubboGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.dubboVersion != null">
        dubbo_version = #{record.dubboVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.fullService != null">
        full_service = #{record.fullService,jdbcType=VARCHAR},
      </if>
      <if test="record.application != null">
        application = #{record.application,jdbcType=VARCHAR},
      </if>
      <if test="record.side != null">
        side = #{record.side,jdbcType=VARCHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.port != null">
        port = #{record.port,jdbcType=INTEGER},
      </if>
      <if test="record.weight != null">
        weight = #{record.weight,jdbcType=DOUBLE},
      </if>
      <if test="record.healthy != null">
        healthy = #{record.healthy,jdbcType=BIT},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.ephemeral != null">
        ephemeral = #{record.ephemeral,jdbcType=BIT},
      </if>
      <if test="record.clusterName != null">
        cluster_name = #{record.clusterName,jdbcType=VARCHAR},
      </if>
      <if test="record.namespaceId != null">
        namespace_id = #{record.namespaceId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.metadata != null">
        metadata = #{record.metadata,jdbcType=VARCHAR},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.lastBeatTime != null">
        last_beat_time = #{record.lastBeatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update naming_instance
    set id = #{record.id,jdbcType=INTEGER},
      instance_id = #{record.instanceId,jdbcType=VARCHAR},
      service_name = #{record.serviceName,jdbcType=VARCHAR},
      dubbo_service = #{record.dubboService,jdbcType=VARCHAR},
      dubbo_group = #{record.dubboGroup,jdbcType=VARCHAR},
      dubbo_version = #{record.dubboVersion,jdbcType=VARCHAR},
      full_service = #{record.fullService,jdbcType=VARCHAR},
      application = #{record.application,jdbcType=VARCHAR},
      side = #{record.side,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      port = #{record.port,jdbcType=INTEGER},
      weight = #{record.weight,jdbcType=DOUBLE},
      healthy = #{record.healthy,jdbcType=BIT},
      enabled = #{record.enabled,jdbcType=BIT},
      ephemeral = #{record.ephemeral,jdbcType=BIT},
      cluster_name = #{record.clusterName,jdbcType=VARCHAR},
      namespace_id = #{record.namespaceId,jdbcType=VARCHAR},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      metadata = #{record.metadata,jdbcType=VARCHAR},
      md5 = #{record.md5,jdbcType=VARCHAR},
      last_beat_time = #{record.lastBeatTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.NamingInstance">
    update naming_instance
    <set>
      <if test="instanceId != null">
        instance_id = #{instanceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="dubboService != null">
        dubbo_service = #{dubboService,jdbcType=VARCHAR},
      </if>
      <if test="dubboGroup != null">
        dubbo_group = #{dubboGroup,jdbcType=VARCHAR},
      </if>
      <if test="dubboVersion != null">
        dubbo_version = #{dubboVersion,jdbcType=VARCHAR},
      </if>
      <if test="fullService != null">
        full_service = #{fullService,jdbcType=VARCHAR},
      </if>
      <if test="application != null">
        application = #{application,jdbcType=VARCHAR},
      </if>
      <if test="side != null">
        side = #{side,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="port != null">
        port = #{port,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DOUBLE},
      </if>
      <if test="healthy != null">
        healthy = #{healthy,jdbcType=BIT},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="ephemeral != null">
        ephemeral = #{ephemeral,jdbcType=BIT},
      </if>
      <if test="clusterName != null">
        cluster_name = #{clusterName,jdbcType=VARCHAR},
      </if>
      <if test="namespaceId != null">
        namespace_id = #{namespaceId,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="metadata != null">
        metadata = #{metadata,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="lastBeatTime != null">
        last_beat_time = #{lastBeatTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.NamingInstance">
    update naming_instance
    set instance_id = #{instanceId,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      dubbo_service = #{dubboService,jdbcType=VARCHAR},
      dubbo_group = #{dubboGroup,jdbcType=VARCHAR},
      dubbo_version = #{dubboVersion,jdbcType=VARCHAR},
      full_service = #{fullService,jdbcType=VARCHAR},
      application = #{application,jdbcType=VARCHAR},
      side = #{side,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      port = #{port,jdbcType=INTEGER},
      weight = #{weight,jdbcType=DOUBLE},
      healthy = #{healthy,jdbcType=BIT},
      enabled = #{enabled,jdbcType=BIT},
      ephemeral = #{ephemeral,jdbcType=BIT},
      cluster_name = #{clusterName,jdbcType=VARCHAR},
      namespace_id = #{namespaceId,jdbcType=VARCHAR},
      group_name = #{groupName,jdbcType=VARCHAR},
      metadata = #{metadata,jdbcType=VARCHAR},
      md5 = #{md5,jdbcType=VARCHAR},
      last_beat_time = #{lastBeatTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByExampleWithDistinctRowbounds" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    full_service,dubbo_service,dubbo_group,dubbo_version ,application
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithDistinctRowboundsNoApp" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    full_service
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithDistinctNORowbounds" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    full_service,dubbo_service,dubbo_group,dubbo_version ,application
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectServiceByExampleWithRowbounds" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultType="com.xiaomi.dayu.api.bo.DubboServiceInfoRes">
    select
    <include refid="Service_Column_List" />
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectServiceByExampleWithNORowbounds" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultType="com.xiaomi.dayu.api.bo.DubboServiceInfoRes">
    select
    <include refid="Service_Column_List" />
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectFullServiceForHera" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultType="java.lang.String">
    select  distinct full_service as fullService
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectDataForHera" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultType="com.xiaomi.dayu.api.bo.DubboServiceInfoRes">
    select distinct side,application,full_service as fullService
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="findAllSimpleInfoWithSideByPage" parameterType="com.xiaomi.dayu.mybatis.example.NamingInstanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    full_service,application
    from naming_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into naming_instance
    (instance_id, service_name, dubbo_service, dubbo_group, dubbo_version, full_service, 
      application, side, ip, port, weight, healthy, enabled, ephemeral, cluster_name, 
      namespace_id, group_name, metadata, md5, last_beat_time, create_time, update_time, 
      del)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.instanceId,jdbcType=VARCHAR}, #{item.serviceName,jdbcType=VARCHAR}, #{item.dubboService,jdbcType=VARCHAR}, 
        #{item.dubboGroup,jdbcType=VARCHAR}, #{item.dubboVersion,jdbcType=VARCHAR}, #{item.fullService,jdbcType=VARCHAR}, 
        #{item.application,jdbcType=VARCHAR}, #{item.side,jdbcType=VARCHAR}, #{item.ip,jdbcType=VARCHAR}, 
        #{item.port,jdbcType=INTEGER}, #{item.weight,jdbcType=DOUBLE}, #{item.healthy,jdbcType=BIT}, 
        #{item.enabled,jdbcType=BIT}, #{item.ephemeral,jdbcType=BIT}, #{item.clusterName,jdbcType=VARCHAR}, 
        #{item.namespaceId,jdbcType=VARCHAR}, #{item.groupName,jdbcType=VARCHAR}, #{item.metadata,jdbcType=VARCHAR}, 
        #{item.md5,jdbcType=VARCHAR}, #{item.lastBeatTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.del,jdbcType=BIT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into naming_instance (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'instance_id'.toString() == column.value">
          #{item.instanceId,jdbcType=VARCHAR}
        </if>
        <if test="'service_name'.toString() == column.value">
          #{item.serviceName,jdbcType=VARCHAR}
        </if>
        <if test="'dubbo_service'.toString() == column.value">
          #{item.dubboService,jdbcType=VARCHAR}
        </if>
        <if test="'dubbo_group'.toString() == column.value">
          #{item.dubboGroup,jdbcType=VARCHAR}
        </if>
        <if test="'dubbo_version'.toString() == column.value">
          #{item.dubboVersion,jdbcType=VARCHAR}
        </if>
        <if test="'full_service'.toString() == column.value">
          #{item.fullService,jdbcType=VARCHAR}
        </if>
        <if test="'application'.toString() == column.value">
          #{item.application,jdbcType=VARCHAR}
        </if>
        <if test="'side'.toString() == column.value">
          #{item.side,jdbcType=VARCHAR}
        </if>
        <if test="'ip'.toString() == column.value">
          #{item.ip,jdbcType=VARCHAR}
        </if>
        <if test="'port'.toString() == column.value">
          #{item.port,jdbcType=INTEGER}
        </if>
        <if test="'weight'.toString() == column.value">
          #{item.weight,jdbcType=DOUBLE}
        </if>
        <if test="'healthy'.toString() == column.value">
          #{item.healthy,jdbcType=BIT}
        </if>
        <if test="'enabled'.toString() == column.value">
          #{item.enabled,jdbcType=BIT}
        </if>
        <if test="'ephemeral'.toString() == column.value">
          #{item.ephemeral,jdbcType=BIT}
        </if>
        <if test="'cluster_name'.toString() == column.value">
          #{item.clusterName,jdbcType=VARCHAR}
        </if>
        <if test="'namespace_id'.toString() == column.value">
          #{item.namespaceId,jdbcType=VARCHAR}
        </if>
        <if test="'group_name'.toString() == column.value">
          #{item.groupName,jdbcType=VARCHAR}
        </if>
        <if test="'metadata'.toString() == column.value">
          #{item.metadata,jdbcType=VARCHAR}
        </if>
        <if test="'md5'.toString() == column.value">
          #{item.md5,jdbcType=VARCHAR}
        </if>
        <if test="'last_beat_time'.toString() == column.value">
          #{item.lastBeatTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'del'.toString() == column.value">
          #{item.del,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <!-- 自定义查询 -->
  <select id="queryApplicationNames" resultType="java.lang.String">
    select distinct application
    from naming_instance
    where side = #{side} order by application
  </select>
  <select id="queryServiceNames" resultType="java.lang.String">
    select  distinct full_service
    from naming_instance
    where side = #{side} and length(full_service)>0 order by full_service
  </select>
  <select id="queryServicesByAddress" resultType="java.lang.String">
    select distinct service_name
    from naming_instance
    where ip = #{ip} order by server_name
  </select>

</mapper>