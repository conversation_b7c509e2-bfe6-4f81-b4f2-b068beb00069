<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.SwimLaneGroupMapper">
  <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.SwimLaneGroup">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="app_list" jdbcType="VARCHAR" property="appList" />
    <result column="descp" jdbcType="VARCHAR" property="descp" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="entrance_app" jdbcType="VARCHAR" property="entranceApp" />
    <result column="prefix_header" jdbcType="VARCHAR" property="prefixHeader" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xiaomi.dayu.mybatis.entity.SwimLaneGroup">
    <result column="app_topic_to_groups" jdbcType="LONGVARCHAR" property="appTopicToGroups" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, app_list, descp, creator, create_time, type, entrance_app, prefix_header
  </sql>
  <sql id="Blob_Column_List">
    app_topic_to_groups
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroupExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swim_lane_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from swim_lane_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swim_lane_group
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from swim_lane_group
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroupExample">
    delete from swim_lane_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroup">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into swim_lane_group (name, app_list, descp, 
      creator, create_time, type, 
      entrance_app, prefix_header, app_topic_to_groups
      )
    values (#{name,jdbcType=VARCHAR}, #{appList,jdbcType=VARCHAR}, #{descp,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{type,jdbcType=INTEGER}, 
      #{entranceApp,jdbcType=VARCHAR}, #{prefixHeader,jdbcType=VARCHAR}, #{appTopicToGroups,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroup">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into swim_lane_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="appList != null">
        app_list,
      </if>
      <if test="descp != null">
        descp,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="entranceApp != null">
        entrance_app,
      </if>
      <if test="prefixHeader != null">
        prefix_header,
      </if>
      <if test="appTopicToGroups != null">
        app_topic_to_groups,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="appList != null">
        #{appList,jdbcType=VARCHAR},
      </if>
      <if test="descp != null">
        #{descp,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="entranceApp != null">
        #{entranceApp,jdbcType=VARCHAR},
      </if>
      <if test="prefixHeader != null">
        #{prefixHeader,jdbcType=VARCHAR},
      </if>
      <if test="appTopicToGroups != null">
        #{appTopicToGroups,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroupExample" resultType="java.lang.Long">
    select count(*) from swim_lane_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update swim_lane_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.appList != null">
        app_list = #{record.appList,jdbcType=VARCHAR},
      </if>
      <if test="record.descp != null">
        descp = #{record.descp,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.entranceApp != null">
        entrance_app = #{record.entranceApp,jdbcType=VARCHAR},
      </if>
      <if test="record.prefixHeader != null">
        prefix_header = #{record.prefixHeader,jdbcType=VARCHAR},
      </if>
      <if test="record.appTopicToGroups != null">
        app_topic_to_groups = #{record.appTopicToGroups,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update swim_lane_group
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      app_list = #{record.appList,jdbcType=VARCHAR},
      descp = #{record.descp,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=INTEGER},
      entrance_app = #{record.entranceApp,jdbcType=VARCHAR},
      prefix_header = #{record.prefixHeader,jdbcType=VARCHAR},
      app_topic_to_groups = #{record.appTopicToGroups,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update swim_lane_group
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      app_list = #{record.appList,jdbcType=VARCHAR},
      descp = #{record.descp,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      type = #{record.type,jdbcType=INTEGER},
      entrance_app = #{record.entranceApp,jdbcType=VARCHAR},
      prefix_header = #{record.prefixHeader,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroup">
    update swim_lane_group
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="appList != null">
        app_list = #{appList,jdbcType=VARCHAR},
      </if>
      <if test="descp != null">
        descp = #{descp,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="entranceApp != null">
        entrance_app = #{entranceApp,jdbcType=VARCHAR},
      </if>
      <if test="prefixHeader != null">
        prefix_header = #{prefixHeader,jdbcType=VARCHAR},
      </if>
      <if test="appTopicToGroups != null">
        app_topic_to_groups = #{appTopicToGroups,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroup">
    update swim_lane_group
    set name = #{name,jdbcType=VARCHAR},
      app_list = #{appList,jdbcType=VARCHAR},
      descp = #{descp,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      entrance_app = #{entranceApp,jdbcType=VARCHAR},
      prefix_header = #{prefixHeader,jdbcType=VARCHAR},
      app_topic_to_groups = #{appTopicToGroups,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneGroup">
    update swim_lane_group
    set name = #{name,jdbcType=VARCHAR},
      app_list = #{appList,jdbcType=VARCHAR},
      descp = #{descp,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      entrance_app = #{entranceApp,jdbcType=VARCHAR},
      prefix_header = #{prefixHeader,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into swim_lane_group
    (name, app_list, descp, creator, create_time, type, entrance_app, prefix_header, 
      app_topic_to_groups)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.appList,jdbcType=VARCHAR}, #{item.descp,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.type,jdbcType=INTEGER}, 
        #{item.entranceApp,jdbcType=VARCHAR}, #{item.prefixHeader,jdbcType=VARCHAR}, #{item.appTopicToGroups,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into swim_lane_group (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'app_list'.toString() == column.value">
          #{item.appList,jdbcType=VARCHAR}
        </if>
        <if test="'descp'.toString() == column.value">
          #{item.descp,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=INTEGER}
        </if>
        <if test="'entrance_app'.toString() == column.value">
          #{item.entranceApp,jdbcType=VARCHAR}
        </if>
        <if test="'prefix_header'.toString() == column.value">
          #{item.prefixHeader,jdbcType=VARCHAR}
        </if>
        <if test="'app_topic_to_groups'.toString() == column.value">
          #{item.appTopicToGroups,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>