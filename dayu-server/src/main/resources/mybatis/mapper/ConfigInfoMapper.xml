<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.ConfigInfoMapper">
    <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.ConfigInfo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="data_id" jdbcType="VARCHAR" property="dataId" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="md5" jdbcType="VARCHAR" property="md5" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="src_user" jdbcType="VARCHAR" property="srcUser" />
        <result column="src_ip" jdbcType="VARCHAR" property="srcIp" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="c_desc" jdbcType="VARCHAR" property="cDesc" />
        <result column="c_use" jdbcType="VARCHAR" property="cUse" />
        <result column="effect" jdbcType="VARCHAR" property="effect" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="c_schema" jdbcType="VARCHAR" property="cSchema" />
    </resultMap>
    <resultMap id="BaseResultMapWithExtend" type="com.xiaomi.dayu.model.dto.ConfigInfoAndExtendDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="data_id" jdbcType="VARCHAR" property="dataId" />
        <result column="group_id" jdbcType="VARCHAR" property="groupId" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="md5" jdbcType="VARCHAR" property="md5" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="createTime" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="src_user" jdbcType="VARCHAR" property="srcUser" />
        <result column="src_ip" jdbcType="VARCHAR" property="srcIp" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="env_id" jdbcType="VARCHAR" property="envId" />
        <result column="env_name" jdbcType="VARCHAR" property="envName" />
        <result column="config_type" jdbcType="TINYINT" property="configType" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, data_id, group_id, md5, gmt_create, gmt_modified, src_user, src_ip,
    app_name, tenant_id, c_desc, c_use, effect, `type`, c_schema
    </sql>
    <sql id="Base_Column_List_WITH_BLOB">
        id, data_id, group_id, content, md5, gmt_create, gmt_modified, src_user, src_ip,
    app_name, tenant_id, c_desc, c_use, effect, `type`, c_schema
    </sql>
    <sql id="Extend_Column_List">
        c.id, c.data_id, group_id, md5, gmt_create, gmt_modified, src_user, src_ip,
    app_name, tenant_id, `type`, env_id, env_name, config_type
    </sql>
    <sql id="Extend_Column_List_WITH_BLOB">
        c.id, c.data_id, group_id, content, md5, gmt_create, gmt_modified, src_user, src_ip,
    app_name, tenant_id, `type`, env_id, env_name, config_type
    </sql>
    <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from config_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByExampleWithBlob" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_WITH_BLOB" />
        from config_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_WITH_BLOB" />
        from config_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from config_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample">
        delete from config_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfo" useGeneratedKeys="true">
        insert into config_info (data_id, group_id, content,
                                 md5, gmt_create, gmt_modified,
                                 src_user, src_ip, app_name,
                                 tenant_id, c_desc, c_use,
                                 effect, `type`, c_schema
        )
        values (#{dataId,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
                #{md5,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP},
                #{srcUser,jdbcType=VARCHAR}, #{srcIp,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR},
                #{tenantId,jdbcType=VARCHAR}, #{cDesc,jdbcType=VARCHAR}, #{cUse,jdbcType=VARCHAR},
                #{effect,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{cSchema,jdbcType=VARCHAR}
               )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfo" useGeneratedKeys="true">
        insert into config_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null">
                data_id,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="md5 != null">
                md5,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="srcUser != null">
                src_user,
            </if>
            <if test="srcIp != null">
                src_ip,
            </if>
            <if test="appName != null">
                app_name,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="cDesc != null">
                c_desc,
            </if>
            <if test="cUse != null">
                c_use,
            </if>
            <if test="effect != null">
                effect,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="cSchema != null">
                c_schema,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null">
                #{dataId,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="md5 != null">
                #{md5,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="srcUser != null">
                #{srcUser,jdbcType=VARCHAR},
            </if>
            <if test="srcIp != null">
                #{srcIp,jdbcType=VARCHAR},
            </if>
            <if test="appName != null">
                #{appName,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="cDesc != null">
                #{cDesc,jdbcType=VARCHAR},
            </if>
            <if test="cUse != null">
                #{cUse,jdbcType=VARCHAR},
            </if>
            <if test="effect != null">
                #{effect,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="cSchema != null">
                #{cSchema,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample" resultType="java.lang.Long">
        select count(*) from config_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update config_info
        <set>
<!--            <if test="record.id != null">-->
<!--                id = #{record.id,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="record.dataId != null">-->
<!--                data_id = #{record.dataId,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="record.groupId != null">-->
<!--                group_id = #{record.groupId,jdbcType=VARCHAR},-->
<!--            </if>-->
            <if test="record.content != null">
                content = #{record.content,jdbcType=VARCHAR},
            </if>
            <if test="record.md5 != null">
                md5 = #{record.md5,jdbcType=VARCHAR},
            </if>
<!--            <if test="record.gmtCreate != null">-->
<!--                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},-->
<!--            </if>-->
<!--            <if test="record.gmtModified != null">-->
<!--                gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},-->
<!--            </if>-->
            <if test="record.srcUser != null">
                src_user = #{record.srcUser,jdbcType=VARCHAR},
            </if>
            <if test="record.srcIp != null">
                src_ip = #{record.srcIp,jdbcType=VARCHAR},
            </if>
            <if test="record.appName != null">
                app_name = #{record.appName,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantId != null">
                tenant_id = #{record.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="record.cDesc != null">
                c_desc = #{record.cDesc,jdbcType=VARCHAR},
            </if>
            <if test="record.cUse != null">
                c_use = #{record.cUse,jdbcType=VARCHAR},
            </if>
            <if test="record.effect != null">
                effect = #{record.effect,jdbcType=VARCHAR},
            </if>
            <if test="record.type != null">
                `type` = #{record.type,jdbcType=VARCHAR},
            </if>
            <if test="record.cSchema != null">
                c_schema = #{record.cSchema,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update config_info
        set
#         id = #{record.id,jdbcType=BIGINT},
#         data_id = #{record.dataId,jdbcType=VARCHAR},
#         group_id = #{record.groupId,jdbcType=VARCHAR},
        content = #{record.content,jdbcType=VARCHAR},
        md5 = #{record.md5,jdbcType=VARCHAR},
#         gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
#         gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
        src_user = #{record.srcUser,jdbcType=VARCHAR},
        src_ip = #{record.srcIp,jdbcType=VARCHAR},
        app_name = #{record.appName,jdbcType=VARCHAR},
#         tenant_id = #{record.tenantId,jdbcType=VARCHAR},
        c_desc = #{record.cDesc,jdbcType=VARCHAR},
        c_use = #{record.cUse,jdbcType=VARCHAR},
        effect = #{record.effect,jdbcType=VARCHAR},
        `type` = #{record.type,jdbcType=VARCHAR},
        c_schema = #{record.cSchema,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfo">
        update config_info
        <set>
<!--            <if test="dataId != null">-->
<!--                data_id = #{dataId,jdbcType=VARCHAR},-->
<!--            </if>-->
<!--            <if test="groupId != null">-->
<!--                group_id = #{groupId,jdbcType=VARCHAR},-->
<!--            </if>-->
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="md5 != null">
                md5 = #{md5,jdbcType=VARCHAR},
            </if>
<!--            <if test="gmtCreate != null">-->
<!--                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--            </if>-->
<!--            <if test="gmtModified != null">-->
<!--                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--            </if>-->
            <if test="srcUser != null">
                src_user = #{srcUser,jdbcType=VARCHAR},
            </if>
            <if test="srcIp != null">
                src_ip = #{srcIp,jdbcType=VARCHAR},
            </if>
            <if test="appName != null">
                app_name = #{appName,jdbcType=VARCHAR},
            </if>
<!--            <if test="tenantId != null">-->
<!--                tenant_id = #{tenantId,jdbcType=VARCHAR},-->
<!--            </if>-->
            <if test="cDesc != null">
                c_desc = #{cDesc,jdbcType=VARCHAR},
            </if>
            <if test="cUse != null">
                c_use = #{cUse,jdbcType=VARCHAR},
            </if>
            <if test="effect != null">
                effect = #{effect,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="cSchema != null">
                c_schema = #{cSchema,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfo">
        update config_info
        set
#             data_id = #{dataId,jdbcType=VARCHAR},
#             group_id = #{groupId,jdbcType=VARCHAR},
            content = #{content,jdbcType=VARCHAR},
            md5 = #{md5,jdbcType=VARCHAR},
#             gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
#             gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            src_user = #{srcUser,jdbcType=VARCHAR},
            src_ip = #{srcIp,jdbcType=VARCHAR},
            app_name = #{appName,jdbcType=VARCHAR},
#             tenant_id = #{tenantId,jdbcType=VARCHAR},
            c_desc = #{cDesc,jdbcType=VARCHAR},
            c_use = #{cUse,jdbcType=VARCHAR},
            effect = #{effect,jdbcType=VARCHAR},
            `type` = #{type,jdbcType=VARCHAR},
            c_schema = #{cSchema,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="fuzzySearchConfig" resultMap="BaseResultMap">
        select * from config_info
        where
        app_name in
        <foreach collection="appNameList" item="appName" open=" (" close=")" separator=",">
            #{appName}
        </foreach>
        and data_id like CONCAT('%',#{congfigType})
    </select>
    <select id="queryCountByAppNameNotEmpty" resultType="int">
        select count(distinct app_name) from config_info
        where
            LENGTH(app_name) > 0
    </select>
    <select id="queryConfigAndExtendListByAppName" resultType="com.xiaomi.dayu.model.dto.ConfigInfoAndExtendDTO">
        select
            conf.data_id AS dataId,conf.group_id AS groupId, conf.content ,conf.md5,conf.gmt_create AS createTime ,
            conf.app_name AS  appName,case when  LENGTH(tenant_id) = 0 then 'public' else tenant_id  end AS tenantId,
            conf.type,extend.env_id AS envId ,extend.env_name AS envName, extend.config_type  AS configType

        from config_info conf left join config_info_extend extend on conf.id = extend.config_info_id
        where
            conf.app_name = #{appName} order by conf.id desc
    </select>
    <select id="searchConfig" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample"
            resultMap="BaseResultMap">
        select
           <include refid="Base_Column_List_WITH_BLOB"/>
        from config_info c
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="searchConfigAndExtend" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample"
            resultMap="BaseResultMapWithExtend">
        select
        <if test="distinct">
            distinct
        </if>
        <choose>
            <when test="withBlob">
                <include refid="Extend_Column_List_WITH_BLOB"/>
            </when>
            <otherwise>
                <include refid="Extend_Column_List"/>
            </otherwise>
        </choose>
        from config_info c
        left join config_info_extend e on c.id = e.config_info_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="searchConfigAndExtendTotal" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample" resultType="long">
        select
        <if test="distinct">
            distinct
        </if>
        count(1)
        from config_info c
        left join config_info_extend e on c.id = e.config_info_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="countConfigAndExtend" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExample"
            resultType="java.lang.Long">
        select count(*)
        from config_info c
        left join config_info_extend e on c.id = e.config_info_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
</mapper>