<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.SwimLaneMapper">
  <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.SwimLane">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="flow_control_tag" jdbcType="VARCHAR" property="flowControlTag" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="swim_lane_group_id" jdbcType="INTEGER" property="swimLaneGroupId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xiaomi.dayu.mybatis.entity.SwimLane">
    <result column="app_env_json" jdbcType="LONGVARCHAR" property="appEnvJson" />
    <result column="condition_json" jdbcType="LONGVARCHAR" property="conditionJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, flow_control_tag, status, swim_lane_group_id
  </sql>
  <sql id="Blob_Column_List">
    app_env_json, condition_json
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swim_lane
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from swim_lane
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from swim_lane
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from swim_lane
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneExample">
    delete from swim_lane
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLane">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into swim_lane (name, flow_control_tag, status, 
      swim_lane_group_id, app_env_json, condition_json
      )
    values (#{name,jdbcType=VARCHAR}, #{flowControlTag,jdbcType=VARCHAR}, #{status,jdbcType=BIT}, 
      #{swimLaneGroupId,jdbcType=INTEGER}, #{appEnvJson,jdbcType=LONGVARCHAR}, #{conditionJson,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLane">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into swim_lane
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="flowControlTag != null">
        flow_control_tag,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="swimLaneGroupId != null">
        swim_lane_group_id,
      </if>
      <if test="appEnvJson != null">
        app_env_json,
      </if>
      <if test="conditionJson != null">
        condition_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="flowControlTag != null">
        #{flowControlTag,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="swimLaneGroupId != null">
        #{swimLaneGroupId,jdbcType=INTEGER},
      </if>
      <if test="appEnvJson != null">
        #{appEnvJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="conditionJson != null">
        #{conditionJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLaneExample" resultType="java.lang.Long">
    select count(*) from swim_lane
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update swim_lane
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.flowControlTag != null">
        flow_control_tag = #{record.flowControlTag,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.swimLaneGroupId != null">
        swim_lane_group_id = #{record.swimLaneGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.appEnvJson != null">
        app_env_json = #{record.appEnvJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.conditionJson != null">
        condition_json = #{record.conditionJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update swim_lane
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      flow_control_tag = #{record.flowControlTag,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=BIT},
      swim_lane_group_id = #{record.swimLaneGroupId,jdbcType=INTEGER},
      app_env_json = #{record.appEnvJson,jdbcType=LONGVARCHAR},
      condition_json = #{record.conditionJson,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update swim_lane
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      flow_control_tag = #{record.flowControlTag,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=BIT},
      swim_lane_group_id = #{record.swimLaneGroupId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLane">
    update swim_lane
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="flowControlTag != null">
        flow_control_tag = #{flowControlTag,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="swimLaneGroupId != null">
        swim_lane_group_id = #{swimLaneGroupId,jdbcType=INTEGER},
      </if>
      <if test="appEnvJson != null">
        app_env_json = #{appEnvJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="conditionJson != null">
        condition_json = #{conditionJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLane">
    update swim_lane
    set name = #{name,jdbcType=VARCHAR},
      flow_control_tag = #{flowControlTag,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      swim_lane_group_id = #{swimLaneGroupId,jdbcType=INTEGER},
      app_env_json = #{appEnvJson,jdbcType=LONGVARCHAR},
      condition_json = #{conditionJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.SwimLane">
    update swim_lane
    set name = #{name,jdbcType=VARCHAR},
      flow_control_tag = #{flowControlTag,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      swim_lane_group_id = #{swimLaneGroupId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into swim_lane
    (name, flow_control_tag, status, swim_lane_group_id, app_env_json, condition_json
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.flowControlTag,jdbcType=VARCHAR}, #{item.status,jdbcType=BIT}, 
        #{item.swimLaneGroupId,jdbcType=INTEGER}, #{item.appEnvJson,jdbcType=LONGVARCHAR}, 
        #{item.conditionJson,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into swim_lane (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'flow_control_tag'.toString() == column.value">
          #{item.flowControlTag,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=BIT}
        </if>
        <if test="'swim_lane_group_id'.toString() == column.value">
          #{item.swimLaneGroupId,jdbcType=INTEGER}
        </if>
        <if test="'app_env_json'.toString() == column.value">
          #{item.appEnvJson,jdbcType=LONGVARCHAR}
        </if>
        <if test="'condition_json'.toString() == column.value">
          #{item.conditionJson,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>