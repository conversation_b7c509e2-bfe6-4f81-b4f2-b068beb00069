<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.ThreadpoolConfigHistoryMapper">
    <insert id="insertHistory" parameterType="com.xiaomi.dayu.mybatis.entity.ThreadpoolConfigHistory"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into threadpool_config_history (config_id, user, action, content, data_id)
        values (#{configID}, #{user}, #{action}, #{content}, #{dataID})
    </insert>

</mapper>
