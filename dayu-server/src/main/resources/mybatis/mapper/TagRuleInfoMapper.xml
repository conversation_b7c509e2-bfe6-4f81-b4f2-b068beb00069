<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.TagRuleInfoMapper">
  <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.TagRuleInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="route_tag" jdbcType="VARCHAR" property="routeTag" />
    <result column="condition_type" jdbcType="INTEGER" property="conditionType" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="ctime" jdbcType="BIGINT" property="ctime" />
    <result column="utime" jdbcType="BIGINT" property="utime" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xiaomi.dayu.mybatis.entity.TagRuleInfo">
    <result column="condition_list" jdbcType="LONGVARCHAR" property="conditionList" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, route_tag, condition_type, creator, updater, ctime, utime, app_id, app_name
  </sql>
  <sql id="Blob_Column_List">
    condition_list
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tag_rule_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tag_rule_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tag_rule_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tag_rule_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfoExample">
    delete from tag_rule_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tag_rule_info (route_tag, condition_type, creator, 
      updater, ctime, utime, 
      app_id, app_name, condition_list
      )
    values (#{routeTag,jdbcType=VARCHAR}, #{conditionType,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, 
      #{updater,jdbcType=VARCHAR}, #{ctime,jdbcType=BIGINT}, #{utime,jdbcType=BIGINT}, 
      #{appId,jdbcType=BIGINT}, #{appName,jdbcType=VARCHAR}, #{conditionList,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tag_rule_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="routeTag != null">
        route_tag,
      </if>
      <if test="conditionType != null">
        condition_type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="utime != null">
        utime,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="appName != null">
        app_name,
      </if>
      <if test="conditionList != null">
        condition_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="routeTag != null">
        #{routeTag,jdbcType=VARCHAR},
      </if>
      <if test="conditionType != null">
        #{conditionType,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="utime != null">
        #{utime,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=BIGINT},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="conditionList != null">
        #{conditionList,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfoExample" resultType="java.lang.Long">
    select count(*) from tag_rule_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tag_rule_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.routeTag != null">
        route_tag = #{record.routeTag,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionType != null">
        condition_type = #{record.conditionType,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.updater != null">
        updater = #{record.updater,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=BIGINT},
      </if>
      <if test="record.utime != null">
        utime = #{record.utime,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=BIGINT},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionList != null">
        condition_list = #{record.conditionList,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update tag_rule_info
    set id = #{record.id,jdbcType=BIGINT},
      route_tag = #{record.routeTag,jdbcType=VARCHAR},
      condition_type = #{record.conditionType,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=VARCHAR},
      updater = #{record.updater,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=BIGINT},
      utime = #{record.utime,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=BIGINT},
      app_name = #{record.appName,jdbcType=VARCHAR},
      condition_list = #{record.conditionList,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tag_rule_info
    set id = #{record.id,jdbcType=BIGINT},
      route_tag = #{record.routeTag,jdbcType=VARCHAR},
      condition_type = #{record.conditionType,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=VARCHAR},
      updater = #{record.updater,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=BIGINT},
      utime = #{record.utime,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=BIGINT},
      app_name = #{record.appName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfo">
    update tag_rule_info
    <set>
      <if test="routeTag != null">
        route_tag = #{routeTag,jdbcType=VARCHAR},
      </if>
      <if test="conditionType != null">
        condition_type = #{conditionType,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="utime != null">
        utime = #{utime,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=BIGINT},
      </if>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="conditionList != null">
        condition_list = #{conditionList,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfo">
    update tag_rule_info
    set route_tag = #{routeTag,jdbcType=VARCHAR},
      condition_type = #{conditionType,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      utime = #{utime,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=BIGINT},
      app_name = #{appName,jdbcType=VARCHAR},
      condition_list = #{conditionList,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.TagRuleInfo">
    update tag_rule_info
    set route_tag = #{routeTag,jdbcType=VARCHAR},
      condition_type = #{conditionType,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      utime = #{utime,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=BIGINT},
      app_name = #{appName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into tag_rule_info
    (route_tag, condition_type, creator, updater, ctime, utime, app_id, app_name, condition_list
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.routeTag,jdbcType=VARCHAR}, #{item.conditionType,jdbcType=INTEGER}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.updater,jdbcType=VARCHAR}, #{item.ctime,jdbcType=BIGINT}, #{item.utime,jdbcType=BIGINT}, 
        #{item.appId,jdbcType=BIGINT}, #{item.appName,jdbcType=VARCHAR}, #{item.conditionList,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into tag_rule_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'route_tag'.toString() == column.value">
          #{item.routeTag,jdbcType=VARCHAR}
        </if>
        <if test="'condition_type'.toString() == column.value">
          #{item.conditionType,jdbcType=INTEGER}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'updater'.toString() == column.value">
          #{item.updater,jdbcType=VARCHAR}
        </if>
        <if test="'ctime'.toString() == column.value">
          #{item.ctime,jdbcType=BIGINT}
        </if>
        <if test="'utime'.toString() == column.value">
          #{item.utime,jdbcType=BIGINT}
        </if>
        <if test="'app_id'.toString() == column.value">
          #{item.appId,jdbcType=BIGINT}
        </if>
        <if test="'app_name'.toString() == column.value">
          #{item.appName,jdbcType=VARCHAR}
        </if>
        <if test="'condition_list'.toString() == column.value">
          #{item.conditionList,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>