<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.SlaManageHisMapper">
  <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.SlaManageHis">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sla_manage_id" jdbcType="BIGINT" property="slaManageId" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="app_id" jdbcType="INTEGER" property="appId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="class_name" jdbcType="VARCHAR" property="className" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="dubbo_group" jdbcType="VARCHAR" property="dubboGroup" />
    <result column="dubbo_version" jdbcType="VARCHAR" property="dubboVersion" />
    <result column="sla_content" jdbcType="VARCHAR" property="slaContent" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del" jdbcType="INTEGER" property="del" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sla_manage_id, app_name, app_id, type, class_name, method_name, dubbo_group, 
    dubbo_version, sla_content, business_key, operator, create_time, update_time, del
  </sql>
  <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.example.SlaManageHisExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sla_manage_his
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sla_manage_his
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sla_manage_his
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.example.SlaManageHisExample">
    delete from sla_manage_his
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaomi.dayu.mybatis.entity.SlaManageHis">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sla_manage_his (sla_manage_id, app_name, app_id, 
      type, class_name, method_name, 
      dubbo_group, dubbo_version, sla_content, 
      business_key, operator, create_time, 
      update_time, del)
    values (#{slaManageId,jdbcType=BIGINT}, #{appName,jdbcType=VARCHAR}, #{appId,jdbcType=INTEGER}, 
      #{type,jdbcType=VARCHAR}, #{className,jdbcType=VARCHAR}, #{methodName,jdbcType=VARCHAR}, 
      #{dubboGroup,jdbcType=VARCHAR}, #{dubboVersion,jdbcType=VARCHAR}, #{slaContent,jdbcType=VARCHAR}, 
      #{businessKey,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{del,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaomi.dayu.mybatis.entity.SlaManageHis">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sla_manage_his
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="slaManageId != null">
        sla_manage_id,
      </if>
      <if test="appName != null">
        app_name,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="className != null">
        class_name,
      </if>
      <if test="methodName != null">
        method_name,
      </if>
      <if test="dubboGroup != null">
        dubbo_group,
      </if>
      <if test="dubboVersion != null">
        dubbo_version,
      </if>
      <if test="slaContent != null">
        sla_content,
      </if>
      <if test="businessKey != null">
        business_key,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="del != null">
        del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="slaManageId != null">
        #{slaManageId,jdbcType=BIGINT},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="className != null">
        #{className,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="dubboGroup != null">
        #{dubboGroup,jdbcType=VARCHAR},
      </if>
      <if test="dubboVersion != null">
        #{dubboVersion,jdbcType=VARCHAR},
      </if>
      <if test="slaContent != null">
        #{slaContent,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        #{del,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.example.SlaManageHisExample" resultType="java.lang.Long">
    select count(*) from sla_manage_his
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sla_manage_his
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.slaManageId != null">
        sla_manage_id = #{record.slaManageId,jdbcType=BIGINT},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.className != null">
        class_name = #{record.className,jdbcType=VARCHAR},
      </if>
      <if test="record.methodName != null">
        method_name = #{record.methodName,jdbcType=VARCHAR},
      </if>
      <if test="record.dubboGroup != null">
        dubbo_group = #{record.dubboGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.dubboVersion != null">
        dubbo_version = #{record.dubboVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.slaContent != null">
        sla_content = #{record.slaContent,jdbcType=VARCHAR},
      </if>
      <if test="record.businessKey != null">
        business_key = #{record.businessKey,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sla_manage_his
    set id = #{record.id,jdbcType=BIGINT},
      sla_manage_id = #{record.slaManageId,jdbcType=BIGINT},
      app_name = #{record.appName,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=VARCHAR},
      class_name = #{record.className,jdbcType=VARCHAR},
      method_name = #{record.methodName,jdbcType=VARCHAR},
      dubbo_group = #{record.dubboGroup,jdbcType=VARCHAR},
      dubbo_version = #{record.dubboVersion,jdbcType=VARCHAR},
      sla_content = #{record.slaContent,jdbcType=VARCHAR},
      business_key = #{record.businessKey,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.SlaManageHis">
    update sla_manage_his
    <set>
      <if test="slaManageId != null">
        sla_manage_id = #{slaManageId,jdbcType=BIGINT},
      </if>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="className != null">
        class_name = #{className,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        method_name = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="dubboGroup != null">
        dubbo_group = #{dubboGroup,jdbcType=VARCHAR},
      </if>
      <if test="dubboVersion != null">
        dubbo_version = #{dubboVersion,jdbcType=VARCHAR},
      </if>
      <if test="slaContent != null">
        sla_content = #{slaContent,jdbcType=VARCHAR},
      </if>
      <if test="businessKey != null">
        business_key = #{businessKey,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.SlaManageHis">
    update sla_manage_his
    set sla_manage_id = #{slaManageId,jdbcType=BIGINT},
      app_name = #{appName,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=INTEGER},
      type = #{type,jdbcType=VARCHAR},
      class_name = #{className,jdbcType=VARCHAR},
      method_name = #{methodName,jdbcType=VARCHAR},
      dubbo_group = #{dubboGroup,jdbcType=VARCHAR},
      dubbo_version = #{dubboVersion,jdbcType=VARCHAR},
      sla_content = #{slaContent,jdbcType=VARCHAR},
      business_key = #{businessKey,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into sla_manage_his
    (sla_manage_id, app_name, app_id, type, class_name, method_name, dubbo_group, dubbo_version, 
      sla_content, business_key, operator, create_time, update_time, del)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.slaManageId,jdbcType=BIGINT}, #{item.appName,jdbcType=VARCHAR}, #{item.appId,jdbcType=INTEGER}, 
        #{item.type,jdbcType=VARCHAR}, #{item.className,jdbcType=VARCHAR}, #{item.methodName,jdbcType=VARCHAR}, 
        #{item.dubboGroup,jdbcType=VARCHAR}, #{item.dubboVersion,jdbcType=VARCHAR}, #{item.slaContent,jdbcType=VARCHAR}, 
        #{item.businessKey,jdbcType=VARCHAR}, #{item.operator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.del,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into sla_manage_his (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'sla_manage_id'.toString() == column.value">
          #{item.slaManageId,jdbcType=BIGINT}
        </if>
        <if test="'app_name'.toString() == column.value">
          #{item.appName,jdbcType=VARCHAR}
        </if>
        <if test="'app_id'.toString() == column.value">
          #{item.appId,jdbcType=INTEGER}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'class_name'.toString() == column.value">
          #{item.className,jdbcType=VARCHAR}
        </if>
        <if test="'method_name'.toString() == column.value">
          #{item.methodName,jdbcType=VARCHAR}
        </if>
        <if test="'dubbo_group'.toString() == column.value">
          #{item.dubboGroup,jdbcType=VARCHAR}
        </if>
        <if test="'dubbo_version'.toString() == column.value">
          #{item.dubboVersion,jdbcType=VARCHAR}
        </if>
        <if test="'sla_content'.toString() == column.value">
          #{item.slaContent,jdbcType=VARCHAR}
        </if>
        <if test="'business_key'.toString() == column.value">
          #{item.businessKey,jdbcType=VARCHAR}
        </if>
        <if test="'operator'.toString() == column.value">
          #{item.operator,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'del'.toString() == column.value">
          #{item.del,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <select id="selectByExampleWithRowbounds" parameterType="com.xiaomi.dayu.mybatis.example.SlaManageHisExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sla_manage_his
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>