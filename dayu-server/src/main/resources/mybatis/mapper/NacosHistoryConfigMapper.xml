<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.NacosHistoryConfigMapper">
  <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.NacosHistoryConfig">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="nid" jdbcType="BIGINT" property="nid" />
    <result column="data_id" jdbcType="VARCHAR" property="dataId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="src_ip" jdbcType="VARCHAR" property="srcIp" />
    <result column="op_type" jdbcType="CHAR" property="opType" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xiaomi.dayu.mybatis.entity.NacosHistoryConfig">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="src_user" jdbcType="LONGVARCHAR" property="srcUser" />
    <result column="encrypted_data_key" jdbcType="LONGVARCHAR" property="encryptedDataKey" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, nid, data_id, group_id, app_name, md5, gmt_create, gmt_modified, src_ip, op_type, 
    tenant_id
  </sql>
  <sql id="Blob_Column_List">
    content, src_user, encrypted_data_key
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.example.NacosHistoryConfigExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from his_config_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.example.NacosHistoryConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from his_config_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.example.NacosHistoryConfigExample">
    delete from his_config_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaomi.dayu.mybatis.entity.NacosHistoryConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into his_config_info (nid, data_id, group_id, 
      app_name, md5, gmt_create, 
      gmt_modified, src_ip, op_type, 
      tenant_id, content, src_user, 
      encrypted_data_key)
    values (#{nid,jdbcType=BIGINT}, #{dataId,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, 
      #{appName,jdbcType=VARCHAR}, #{md5,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{srcIp,jdbcType=VARCHAR}, #{opType,jdbcType=CHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}, #{srcUser,jdbcType=LONGVARCHAR}, 
      #{encryptedDataKey,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaomi.dayu.mybatis.entity.NacosHistoryConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into his_config_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="nid != null">
        nid,
      </if>
      <if test="dataId != null">
        data_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="appName != null">
        app_name,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="srcIp != null">
        src_ip,
      </if>
      <if test="opType != null">
        op_type,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="srcUser != null">
        src_user,
      </if>
      <if test="encryptedDataKey != null">
        encrypted_data_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="nid != null">
        #{nid,jdbcType=BIGINT},
      </if>
      <if test="dataId != null">
        #{dataId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="srcIp != null">
        #{srcIp,jdbcType=VARCHAR},
      </if>
      <if test="opType != null">
        #{opType,jdbcType=CHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="srcUser != null">
        #{srcUser,jdbcType=LONGVARCHAR},
      </if>
      <if test="encryptedDataKey != null">
        #{encryptedDataKey,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.example.NacosHistoryConfigExample" resultType="java.lang.Long">
    select count(*) from his_config_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update his_config_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.nid != null">
        nid = #{record.nid,jdbcType=BIGINT},
      </if>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.srcIp != null">
        src_ip = #{record.srcIp,jdbcType=VARCHAR},
      </if>
      <if test="record.opType != null">
        op_type = #{record.opType,jdbcType=CHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.srcUser != null">
        src_user = #{record.srcUser,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.encryptedDataKey != null">
        encrypted_data_key = #{record.encryptedDataKey,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update his_config_info
    set id = #{record.id,jdbcType=BIGINT},
      nid = #{record.nid,jdbcType=BIGINT},
      data_id = #{record.dataId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      md5 = #{record.md5,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      src_ip = #{record.srcIp,jdbcType=VARCHAR},
      op_type = #{record.opType,jdbcType=CHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=LONGVARCHAR},
      src_user = #{record.srcUser,jdbcType=LONGVARCHAR},
      encrypted_data_key = #{record.encryptedDataKey,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update his_config_info
    set id = #{record.id,jdbcType=BIGINT},
      nid = #{record.nid,jdbcType=BIGINT},
      data_id = #{record.dataId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      md5 = #{record.md5,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
      src_ip = #{record.srcIp,jdbcType=VARCHAR},
      op_type = #{record.opType,jdbcType=CHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into his_config_info
    (nid, data_id, group_id, app_name, md5, gmt_create, gmt_modified, src_ip, op_type, 
      tenant_id, content, src_user, encrypted_data_key)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.nid,jdbcType=BIGINT}, #{item.dataId,jdbcType=VARCHAR}, #{item.groupId,jdbcType=VARCHAR}, 
        #{item.appName,jdbcType=VARCHAR}, #{item.md5,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.srcIp,jdbcType=VARCHAR}, #{item.opType,jdbcType=CHAR}, 
        #{item.tenantId,jdbcType=VARCHAR}, #{item.content,jdbcType=LONGVARCHAR}, #{item.srcUser,jdbcType=LONGVARCHAR}, 
        #{item.encryptedDataKey,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into his_config_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'nid'.toString() == column.value">
          #{item.nid,jdbcType=BIGINT}
        </if>
        <if test="'data_id'.toString() == column.value">
          #{item.dataId,jdbcType=VARCHAR}
        </if>
        <if test="'group_id'.toString() == column.value">
          #{item.groupId,jdbcType=VARCHAR}
        </if>
        <if test="'app_name'.toString() == column.value">
          #{item.appName,jdbcType=VARCHAR}
        </if>
        <if test="'md5'.toString() == column.value">
          #{item.md5,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_create'.toString() == column.value">
          #{item.gmtCreate,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'src_ip'.toString() == column.value">
          #{item.srcIp,jdbcType=VARCHAR}
        </if>
        <if test="'op_type'.toString() == column.value">
          #{item.opType,jdbcType=CHAR}
        </if>
        <if test="'tenant_id'.toString() == column.value">
          #{item.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="'content'.toString() == column.value">
          #{item.content,jdbcType=LONGVARCHAR}
        </if>
        <if test="'src_user'.toString() == column.value">
          #{item.srcUser,jdbcType=LONGVARCHAR}
        </if>
        <if test="'encrypted_data_key'.toString() == column.value">
          #{item.encryptedDataKey,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>