<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.ApprovalMapper">
  <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.Approval">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="approve_type" jdbcType="TINYINT" property="approveType" />
    <result column="operate_type" jdbcType="TINYINT" property="operateType" />
    <result column="relate_key" jdbcType="VARCHAR" property="relateKey" />
    <result column="relate_info" jdbcType="VARCHAR" property="relateInfo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="applicant" jdbcType="VARCHAR" property="applicant" />
    <result column="approver" jdbcType="VARCHAR" property="approver" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="apply_remark" jdbcType="VARCHAR" property="applyRemark" />
    <result column="operate_remark" jdbcType="VARCHAR" property="operateRemark" />
    <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del" jdbcType="TINYINT" property="del" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xiaomi.dayu.mybatis.entity.Approval">
    <result column="new_data" jdbcType="LONGVARCHAR" property="newData" />
    <result column="old_data" jdbcType="LONGVARCHAR" property="oldData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_name, approve_type, operate_type, relate_key, relate_info, status, applicant, 
    approver, operator, apply_remark, operate_remark, approve_time, create_time, update_time, 
    del,new_data,old_data
  </sql>
  <sql id="Blob_Column_List">
    new_data, old_data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.example.ApprovalExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from approval
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.example.ApprovalExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from approval
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from approval
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from approval
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.example.ApprovalExample">
    delete from approval
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xiaomi.dayu.mybatis.entity.Approval">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into approval (app_name, approve_type, operate_type, 
      relate_key, relate_info, status, 
      applicant, approver, operator, 
      apply_remark, operate_remark, approve_time, 
      create_time, update_time, del, 
      new_data, old_data)
    values (#{appName,jdbcType=VARCHAR}, #{approveType,jdbcType=TINYINT}, #{operateType,jdbcType=TINYINT}, 
      #{relateKey,jdbcType=VARCHAR}, #{relateInfo,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{applicant,jdbcType=VARCHAR}, #{approver,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{applyRemark,jdbcType=VARCHAR}, #{operateRemark,jdbcType=VARCHAR}, #{approveTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{del,jdbcType=TINYINT}, 
      #{newData,jdbcType=LONGVARCHAR}, #{oldData,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xiaomi.dayu.mybatis.entity.Approval">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into approval
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appName != null">
        app_name,
      </if>
      <if test="approveType != null">
        approve_type,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="relateKey != null">
        relate_key,
      </if>
      <if test="relateInfo != null">
        relate_info,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="applicant != null">
        applicant,
      </if>
      <if test="approver != null">
        approver,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="applyRemark != null">
        apply_remark,
      </if>
      <if test="operateRemark != null">
        operate_remark,
      </if>
      <if test="approveTime != null">
        approve_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="newData != null">
        new_data,
      </if>
      <if test="oldData != null">
        old_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="approveType != null">
        #{approveType,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relateKey != null">
        #{relateKey,jdbcType=VARCHAR},
      </if>
      <if test="relateInfo != null">
        #{relateInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="applicant != null">
        #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="approver != null">
        #{approver,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="applyRemark != null">
        #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="operateRemark != null">
        #{operateRemark,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        #{del,jdbcType=TINYINT},
      </if>
      <if test="newData != null">
        #{newData,jdbcType=LONGVARCHAR},
      </if>
      <if test="oldData != null">
        #{oldData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.example.ApprovalExample" resultType="java.lang.Long">
    select count(*) from approval
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update approval
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.approveType != null">
        approve_type = #{record.approveType,jdbcType=TINYINT},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=TINYINT},
      </if>
      <if test="record.relateKey != null">
        relate_key = #{record.relateKey,jdbcType=VARCHAR},
      </if>
      <if test="record.relateInfo != null">
        relate_info = #{record.relateInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.applicant != null">
        applicant = #{record.applicant,jdbcType=VARCHAR},
      </if>
      <if test="record.approver != null">
        approver = #{record.approver,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.applyRemark != null">
        apply_remark = #{record.applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.operateRemark != null">
        operate_remark = #{record.operateRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.approveTime != null">
        approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=TINYINT},
      </if>
      <if test="record.newData != null">
        new_data = #{record.newData,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.oldData != null">
        old_data = #{record.oldData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update approval
    set id = #{record.id,jdbcType=INTEGER},
      app_name = #{record.appName,jdbcType=VARCHAR},
      approve_type = #{record.approveType,jdbcType=TINYINT},
      operate_type = #{record.operateType,jdbcType=TINYINT},
      relate_key = #{record.relateKey,jdbcType=VARCHAR},
      relate_info = #{record.relateInfo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      applicant = #{record.applicant,jdbcType=VARCHAR},
      approver = #{record.approver,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      apply_remark = #{record.applyRemark,jdbcType=VARCHAR},
      operate_remark = #{record.operateRemark,jdbcType=VARCHAR},
      approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=TINYINT},
      new_data = #{record.newData,jdbcType=LONGVARCHAR},
      old_data = #{record.oldData,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update approval
    set id = #{record.id,jdbcType=INTEGER},
      app_name = #{record.appName,jdbcType=VARCHAR},
      approve_type = #{record.approveType,jdbcType=TINYINT},
      operate_type = #{record.operateType,jdbcType=TINYINT},
      relate_key = #{record.relateKey,jdbcType=VARCHAR},
      relate_info = #{record.relateInfo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      applicant = #{record.applicant,jdbcType=VARCHAR},
      approver = #{record.approver,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      apply_remark = #{record.applyRemark,jdbcType=VARCHAR},
      operate_remark = #{record.operateRemark,jdbcType=VARCHAR},
      approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.Approval">
    update approval
    <set>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="approveType != null">
        approve_type = #{approveType,jdbcType=TINYINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relateKey != null">
        relate_key = #{relateKey,jdbcType=VARCHAR},
      </if>
      <if test="relateInfo != null">
        relate_info = #{relateInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="applicant != null">
        applicant = #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="approver != null">
        approver = #{approver,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="applyRemark != null">
        apply_remark = #{applyRemark,jdbcType=VARCHAR},
      </if>
      <if test="operateRemark != null">
        operate_remark = #{operateRemark,jdbcType=VARCHAR},
      </if>
      <if test="approveTime != null">
        approve_time = #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=TINYINT},
      </if>
      <if test="newData != null">
        new_data = #{newData,jdbcType=LONGVARCHAR},
      </if>
      <if test="oldData != null">
        old_data = #{oldData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xiaomi.dayu.mybatis.entity.Approval">
    update approval
    set app_name = #{appName,jdbcType=VARCHAR},
      approve_type = #{approveType,jdbcType=TINYINT},
      operate_type = #{operateType,jdbcType=TINYINT},
      relate_key = #{relateKey,jdbcType=VARCHAR},
      relate_info = #{relateInfo,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      applicant = #{applicant,jdbcType=VARCHAR},
      approver = #{approver,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      apply_remark = #{applyRemark,jdbcType=VARCHAR},
      operate_remark = #{operateRemark,jdbcType=VARCHAR},
      approve_time = #{approveTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=TINYINT},
      new_data = #{newData,jdbcType=LONGVARCHAR},
      old_data = #{oldData,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.Approval">
    update approval
    set app_name = #{appName,jdbcType=VARCHAR},
      approve_type = #{approveType,jdbcType=TINYINT},
      operate_type = #{operateType,jdbcType=TINYINT},
      relate_key = #{relateKey,jdbcType=VARCHAR},
      relate_info = #{relateInfo,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      applicant = #{applicant,jdbcType=VARCHAR},
      approver = #{approver,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      apply_remark = #{applyRemark,jdbcType=VARCHAR},
      operate_remark = #{operateRemark,jdbcType=VARCHAR},
      approve_time = #{approveTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into approval
    (app_name, approve_type, operate_type, relate_key, relate_info, status, applicant, 
      approver, operator, apply_remark, operate_remark, approve_time, create_time, update_time, 
      del, new_data, old_data)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appName,jdbcType=VARCHAR}, #{item.approveType,jdbcType=TINYINT}, #{item.operateType,jdbcType=TINYINT}, 
        #{item.relateKey,jdbcType=VARCHAR}, #{item.relateInfo,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, 
        #{item.applicant,jdbcType=VARCHAR}, #{item.approver,jdbcType=VARCHAR}, #{item.operator,jdbcType=VARCHAR}, 
        #{item.applyRemark,jdbcType=VARCHAR}, #{item.operateRemark,jdbcType=VARCHAR}, #{item.approveTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.del,jdbcType=TINYINT}, 
        #{item.newData,jdbcType=LONGVARCHAR}, #{item.oldData,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into approval (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'app_name'.toString() == column.value">
          #{item.appName,jdbcType=VARCHAR}
        </if>
        <if test="'approve_type'.toString() == column.value">
          #{item.approveType,jdbcType=TINYINT}
        </if>
        <if test="'operate_type'.toString() == column.value">
          #{item.operateType,jdbcType=TINYINT}
        </if>
        <if test="'relate_key'.toString() == column.value">
          #{item.relateKey,jdbcType=VARCHAR}
        </if>
        <if test="'relate_info'.toString() == column.value">
          #{item.relateInfo,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'applicant'.toString() == column.value">
          #{item.applicant,jdbcType=VARCHAR}
        </if>
        <if test="'approver'.toString() == column.value">
          #{item.approver,jdbcType=VARCHAR}
        </if>
        <if test="'operator'.toString() == column.value">
          #{item.operator,jdbcType=VARCHAR}
        </if>
        <if test="'apply_remark'.toString() == column.value">
          #{item.applyRemark,jdbcType=VARCHAR}
        </if>
        <if test="'operate_remark'.toString() == column.value">
          #{item.operateRemark,jdbcType=VARCHAR}
        </if>
        <if test="'approve_time'.toString() == column.value">
          #{item.approveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'del'.toString() == column.value">
          #{item.del,jdbcType=TINYINT}
        </if>
        <if test="'new_data'.toString() == column.value">
          #{item.newData,jdbcType=LONGVARCHAR}
        </if>
        <if test="'old_data'.toString() == column.value">
          #{item.oldData,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>

  <select id="selectByExampleWithRowbounds" parameterType="com.xiaomi.dayu.mybatis.example.ApprovalExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from approval
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>


</mapper>