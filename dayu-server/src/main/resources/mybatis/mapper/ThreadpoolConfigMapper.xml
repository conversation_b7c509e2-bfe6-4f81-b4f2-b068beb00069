<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.dayu.dao.ThreadpoolConfigMapper">
    <select id="queryConfigsOfApp" resultType="com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig">
        select *
        from threadpool_config
        where app_name = #{appName}
    </select>
    <select id="queryConfigs" resultType="com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig">
        select *
        from threadpool_config
        where id = #{id}
    </select>
    <select id="queryConfigsByAppAndPool" resultType="com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig">
        select *
        from threadpool_config
        where app_name = #{appName}
          and pool_name = #{poolName}
    </select>
    <insert id="insertConfig" parameterType="com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into threadpool_config (app_name, pool_name, core_pool_size, maximum_pool_size, keep_alive_time,
                                       capacity, reject)
        values (#{appName}, #{poolName}, #{corePoolSize}, #{maximumPoolSize}, #{keepAliveTime}, #{capacity}, #{reject})
    </insert>
    <update id="updateConfig" parameterType="com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig">
        update threadpool_config
        set pool_name         = #{poolName},
            core_pool_size    = #{corePoolSize},
            maximum_pool_size = #{maximumPoolSize},
            keep_alive_time   = #{keepAliveTime},
            capacity          = #{capacity},
            reject            = #{reject}
        where id = #{id}
    </update>
</mapper>
