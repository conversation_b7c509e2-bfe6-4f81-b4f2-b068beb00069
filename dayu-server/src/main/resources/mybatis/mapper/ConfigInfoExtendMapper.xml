<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xiaomi.dayu.dao.ConfigInfoExtendMapper">
    <resultMap id="BaseResultMap" type="com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="config_info_id" jdbcType="BIGINT" property="configInfoId" />
        <result column="data_id" jdbcType="VARCHAR" property="dataId" />
        <result column="env_id" jdbcType="VARCHAR" property="envId" />
        <result column="env_name" jdbcType="VARCHAR" property="envName" />
        <result column="config_type" jdbcType="TINYINT" property="configType" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, config_info_id, data_id, env_id, env_name, config_type, create_time
    </sql>
    <select id="selectByExample" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExtendExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from config_info_extend
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from config_info_extend
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from config_info_extend
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExtendExample">
        delete from config_info_extend
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend" useGeneratedKeys="true">
        insert into config_info_extend (config_info_id, data_id, env_id,
                                        env_name, config_type, create_time
        )
        values (#{configInfoId,jdbcType=BIGINT}, #{dataId,jdbcType=VARCHAR}, #{envId,jdbcType=VARCHAR},
                #{envName,jdbcType=VARCHAR}, #{configType,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}
               )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend" useGeneratedKeys="true">
        insert into config_info_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configInfoId != null">
                config_info_id,
            </if>
            <if test="dataId != null">
                data_id,
            </if>
            <if test="envId != null">
                env_id,
            </if>
            <if test="envName != null">
                env_name,
            </if>
            <if test="configType != null">
                config_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configInfoId != null">
                #{configInfoId,jdbcType=BIGINT},
            </if>
            <if test="dataId != null">
                #{dataId,jdbcType=VARCHAR},
            </if>
            <if test="envId != null">
                #{envId,jdbcType=VARCHAR},
            </if>
            <if test="envName != null">
                #{envName,jdbcType=VARCHAR},
            </if>
            <if test="configType != null">
                #{configType,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.xiaomi.dayu.mybatis.example.ConfigInfoExtendExample" resultType="java.lang.Long">
        select count(*) from config_info_extend
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update config_info_extend
        <set>
<!--            <if test="record.id != null">-->
<!--                id = #{record.id,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="record.configInfoId != null">-->
<!--                config_info_id = #{record.configInfoId,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="record.dataId != null">-->
<!--                data_id = #{record.dataId,jdbcType=VARCHAR},-->
<!--            </if>-->
            <if test="record.envId != null">
                env_id = #{record.envId,jdbcType=VARCHAR},
            </if>
            <if test="record.envName != null">
                env_name = #{record.envName,jdbcType=VARCHAR},
            </if>
            <if test="record.configType != null">
                config_type = #{record.configType,jdbcType=TINYINT},
            </if>
<!--            <if test="record.createTime != null">-->
<!--                create_time = #{record.createTime,jdbcType=TIMESTAMP},-->
<!--            </if>-->
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update config_info_extend
        set
#         id = #{record.id,jdbcType=BIGINT},
#         config_info_id = #{record.configInfoId,jdbcType=BIGINT},
#         data_id = #{record.dataId,jdbcType=VARCHAR},
        env_id = #{record.envId,jdbcType=VARCHAR},
        env_name = #{record.envName,jdbcType=VARCHAR},
        config_type = #{record.configType,jdbcType=TINYINT}
#         ,
#         create_time = #{record.createTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend">
        update config_info_extend
        <set>
<!--            <if test="configInfoId != null">-->
<!--                config_info_id = #{configInfoId,jdbcType=BIGINT},-->
<!--            </if>-->
<!--            <if test="dataId != null">-->
<!--                data_id = #{dataId,jdbcType=VARCHAR},-->
<!--            </if>-->
            <if test="envId != null">
                env_id = #{envId,jdbcType=VARCHAR},
            </if>
            <if test="envName != null">
                env_name = #{envName,jdbcType=VARCHAR},
            </if>
            <if test="configType != null">
                config_type = #{configType,jdbcType=TINYINT},
            </if>
<!--            <if test="createTime != null">-->
<!--                create_time = #{createTime,jdbcType=TIMESTAMP},-->
<!--            </if>-->
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend">
        update config_info_extend
        set
#             config_info_id = #{configInfoId,jdbcType=BIGINT},
#             data_id = #{dataId,jdbcType=VARCHAR},
            env_id = #{envId,jdbcType=VARCHAR},
            env_name = #{envName,jdbcType=VARCHAR},
            config_type = #{configType,jdbcType=TINYINT}
#             ,
#             create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="queryByConfigInfoId" resultMap="BaseResultMap">
        select * from config_info_extend
        where  config_info_id = #{id}
    </select>
    <select id="queryByConfigInfoIds" resultMap="BaseResultMap">
        select * from config_info_extend
        where  config_info_id in
        <foreach collection="configInfoIds" item="id" open =" (" close=")" separator=",">
                  #{id}
        </foreach>
    </select>
    <insert id="insertOrUpdate" parameterType="com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend">
        insert into config_info_extend (data_id, config_info_id, env_id, env_name, config_type)
        values (#{dataId}, #{configInfoId}, #{envId}, #{envName}, #{configType})
        on duplicate key update env_id      = #{envId,jdbcType=VARCHAR},
                                env_name    = #{envName,jdbcType=VARCHAR},
                                config_type = #{configType,jdbcType=TINYINT}
    </insert>
</mapper>