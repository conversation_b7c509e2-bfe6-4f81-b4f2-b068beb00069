<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC
        "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <settings>
        <!-- 全局的映射器启用或禁用缓存。 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 全局启用或禁用延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 允许或不允许多种结果集从一个单独的语句中返回 -->
        <setting name="multipleResultSetsEnabled" value="true"/>
        <!-- 使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true"/>
        <!-- 允许JDBC支持生成的键 -->
        <setting name="useGeneratedKeys" value="false"/>
        <!-- 配置默认的执行器 -->
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <!-- 设置超时时间 -->
        <setting name="defaultStatementTimeout" value="60"/>
        <!-- 设置驼峰标识 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>


    <plugins>
        <plugin interceptor="com.github.pagehelper.PageInterceptor" />
<!--        <plugin interceptor="com.xiaomi.dayu.dao.MyInterceptor"></plugin>-->
    </plugins>

    <environments default="development" >
        <environment id="development" >
            <transactionManager type="JDBC">
                <property name="autoCommit" value="true"/>
            </transactionManager>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.jdbc.Driver"/>
                <property name="url" value="***********************************************************"/>
                <property name="username" value="***"/>
                <property name="password" value="***"/>
            </dataSource>
        </environment>
    </environments>

    <!--    <mappers>-->
    <!--        <package name="classpath:mybatis/mapper/*Mapper.xml"></package>-->
    <!--    </mappers>-->
    <mappers>
        <mapper resource="mybatis/mapper/HisConfigInfo.xml"></mapper>
       <!-- <mapper resource="mybatis/mapper/ApprovalMapper.xml"></mapper>-->
        <!--<package name="classpath:mybatis/mapper/*"/>-->
    </mappers>
</configuration>