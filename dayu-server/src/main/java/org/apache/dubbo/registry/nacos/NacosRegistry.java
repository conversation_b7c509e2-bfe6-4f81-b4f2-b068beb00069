/*
//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.apache.dubbo.registry.nacos;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.listener.EventListener;
import com.alibaba.nacos.api.naming.listener.NamingEvent;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import com.google.common.collect.Lists;
import com.xiaomi.dayu.common.util.Constants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.URLBuilder;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.registry.NotifyListener;
import org.apache.dubbo.registry.nacos.util.NacosInstanceManageUtil;
import org.apache.dubbo.registry.nacos.util.NacosNamingServiceUtils;
import org.apache.dubbo.registry.support.FailbackRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class NacosRegistry extends FailbackRegistry {
    private static final List<String> ALL_SUPPORTED_CATEGORIES = Arrays.asList("providers", "consumers", "routers", "configurators");
    private static final int CATEGORY_INDEX = 0;
    private static final int SERVICE_INTERFACE_INDEX = 1;
    private static final int SERVICE_VERSION_INDEX = 2;
    private static final int SERVICE_GROUP_INDEX = 3;
    private static final String WILDCARD = "*";
    private static final String SERVICE_NAME_SEPARATOR = System.getProperty("nacos.service.name.separator", ":");
    private static final int PAGINATION_SIZE = Integer.getInteger("nacos.service.names.pagination.size", 100);
    private static final long LOOKUP_INTERVAL = Long.getLong("nacos.service.names.lookup.interval", 30L);
    private volatile ScheduledExecutorService scheduledExecutorService;
    private final NacosNamingServiceWrapper namingService;

    private static final Logger logger = LoggerFactory.getLogger(NacosRegistry.class.getName());

    public NacosRegistry(URL url, NacosNamingServiceWrapper namingService) {
        super(url);
        this.namingService = namingService;
    }

    public boolean isAvailable() {
        return "UP".equals(this.namingService.getServerStatus());
    }

    public List<URL> lookup(final URL url) {
        List<URL> urls = new LinkedList();
        this.execute((namingService) -> {
            Set<String> serviceNames = this.getServiceNames(url, (NotifyListener)null);
            Iterator var5 = serviceNames.iterator();

            while(var5.hasNext()) {
                String serviceName = (String)var5.next();
                List<Instance> instances = namingService.getAllInstances(serviceName, this.getUrl().getParameter("group", "DEFAULT_GROUP"));
                urls.addAll(this.buildURLs(url, instances));
            }

        });
        return urls;
    }

    public void doRegister(URL url) {
        String serviceName = this.getServiceName(url);
        Instance instance = this.createInstance(url);
        this.execute((namingService) -> {
            namingService.registerInstance(serviceName, this.getUrl().getParameter("group", "DEFAULT_GROUP"), instance);
        });
    }

    public void doUnregister(final URL url) {
        this.execute((namingService) -> {
            String serviceName = this.getServiceName(url);
            Instance instance = this.createInstance(url);
            namingService.deregisterInstance(serviceName, this.getUrl().getParameter("group", "DEFAULT_GROUP"), instance.getIp(), instance.getPort());
        });
    }

    public void doSubscribe(final URL url, final NotifyListener listener) {
        Set<String> serviceNames = this.getServiceNames(url, listener);
        if (this.isServiceNamesWithCompatibleMode(url)) {
            Iterator var4 = serviceNames.iterator();

            while(var4.hasNext()) {
                String serviceName = (String)var4.next();
                NacosInstanceManageUtil.setCorrespondingServiceNames(serviceName, serviceNames);
            }
        }

        this.doSubscribe(url, listener, serviceNames);
    }

    private void doSubscribe(final URL url, final NotifyListener listener, final Set<String> serviceNames) {
        this.execute((namingService) -> {
            Iterator var6;
            String serviceName;
            if (this.isServiceNamesWithCompatibleMode(url)) {
                List<Instance> allCorrespondingInstanceList = Lists.newArrayList();
                var6 = serviceNames.iterator();

                while(var6.hasNext()) {
                    serviceName = (String)var6.next();
                    List<Instance> instances = namingService.getAllInstances(serviceName, this.getUrl().getParameter("group", "DEFAULT_GROUP"));
                    NacosInstanceManageUtil.initOrRefreshServiceInstanceList(serviceName, instances);
                    allCorrespondingInstanceList.addAll(instances);
                }
                this.notifySubscriber(url, listener, allCorrespondingInstanceList);
                var6 = serviceNames.iterator();

                while(var6.hasNext()) {
                    serviceName = (String)var6.next();
                    this.subscribeEventListener(serviceName, url, listener);
                }
            } else {
                var6 = serviceNames.iterator();

                while(var6.hasNext()) {
                    serviceName = (String)var6.next();
                    List<Instance> instancesx = namingService.getAllInstances(serviceName, this.getUrl().getParameter("group", "DEFAULT_GROUP"));
                    Map<String, String> parameters = new HashMap<>();

                    String[] split = serviceName.split(":");

                    if(CollectionUtils.isNotEmpty(instancesx)){
                        parameters = instancesx.get(0).getMetadata();
                    }else{
                        parameters.put("category",split[0]);
                        parameters.put("interface", split[1]);
                        if(split.length == 4){
                            parameters.put("version", split[2]);
                            parameters.put("group", split[3]);
                        }else if(split.length == 3){
                            try{
                                Long.parseLong(split[2]);
                                parameters.put("version", split[2]);
                            }catch (NumberFormatException exception){
                                parameters.put("group", split[2]);
                            }
                        }
                    }
                    URL url1 = new URL(url.getProtocol(),url.getHost(),url.getPort(),parameters);
                    this.notifySubscriber(url1, listener, instancesx);

                    this.subscribeEventListener(serviceName, url, listener);
                }
            }

        });
    }

    private boolean isServiceNamesWithCompatibleMode(final URL url) {
        return !this.isAdminProtocol(url) && this.createServiceName(url).isConcrete();
    }

    public void doUnsubscribe(URL url, NotifyListener listener) {
        if (this.isAdminProtocol(url)) {
            this.shutdownServiceNamesLookup();
        }

    }

    private void shutdownServiceNamesLookup() {
        if (this.scheduledExecutorService != null) {
            this.scheduledExecutorService.shutdown();
        }

    }

    private Set<String> getServiceNames(URL url, NotifyListener listener) {
        if (this.isAdminProtocol(url)) {
            this.scheduleServiceNamesLookup(url, listener);
            return this.getServiceNamesForOps(url);
        } else {
            return this.getServiceNames0(url);
        }
    }

    private Set<String> getServiceNames0(URL url) {
        NacosServiceName serviceName = this.createServiceName(url);
        Object serviceNames;
        if (serviceName.isConcrete()) {
            serviceNames = new LinkedHashSet();
            ((Set)serviceNames).add(serviceName.toString());
            String legacySubscribedServiceName = this.getLegacySubscribedServiceName(url);
            if (!serviceName.toString().equals(legacySubscribedServiceName)) {
                ((Set)serviceNames).add(legacySubscribedServiceName);
            }
        } else {
            serviceNames = this.filterServiceNames(serviceName);
        }

        return (Set)serviceNames;
    }

    private Set<String> filterServiceNames(NacosServiceName serviceName) {
        Set<String> serviceNames = new LinkedHashSet();
        this.execute((namingService) -> {
            Stream<NacosServiceName> var10001 = namingService.getServicesOfServer(1, 2147483647, this.getUrl().getParameter("group", "DEFAULT_GROUP")).getData().stream().filter(this::isConformRules).map(NacosServiceName::new);
            serviceName.getClass();
            serviceNames.addAll(var10001.filter(serviceName::isCompatible).map(NacosServiceName::toString).collect(Collectors.toList()));
        });
        return serviceNames;
    }

    private boolean isConformRules(String serviceName) {
        return serviceName.split(":", -1).length == 4;
    }

    private String getLegacySubscribedServiceName(URL url) {
        StringBuilder serviceNameBuilder = new StringBuilder("providers");
        this.appendIfPresent(serviceNameBuilder, url, "interface");
        this.appendIfPresent(serviceNameBuilder, url, "version");
        this.appendIfPresent(serviceNameBuilder, url, "group");
        return serviceNameBuilder.toString();
    }

    private void appendIfPresent(StringBuilder target, URL url, String parameterName) {
        String parameterValue = url.getParameter(parameterName);
        if (!StringUtils.isBlank(parameterValue)) {
            target.append(SERVICE_NAME_SEPARATOR).append(parameterValue);
        }

    }

    private boolean isAdminProtocol(URL url) {
        return "admin".equals(url.getProtocol());
    }

    private void scheduleServiceNamesLookup(final URL url, final NotifyListener listener) {
        if (this.scheduledExecutorService == null) {
            this.scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
            this.scheduledExecutorService.scheduleWithFixedDelay(() -> {
                Set<String> serviceNames = this.getAllServiceNames();
                logger.warn("scheduledExecutorService.scheduleWithFixedDelay：时间="+new Date()+"，service数量="+serviceNames.size());
                this.filterData(serviceNames, (serviceName) -> {
                    boolean accepted = false;
                    Iterator var2 = ALL_SUPPORTED_CATEGORIES.iterator();

                    while(var2.hasNext()) {
                        String category = (String)var2.next();
                        String prefix = category + SERVICE_NAME_SEPARATOR;
                        if (serviceName != null && serviceName.startsWith(prefix)) {
                            accepted = true;
                            break;
                        }
                    }

                    return accepted;
                });
                this.doSubscribe(url, listener, serviceNames);
            }, LOOKUP_INTERVAL, LOOKUP_INTERVAL, TimeUnit.SECONDS);
        }

    }

    private Set<String> getServiceNamesForOps(URL url) {
        Set<String> serviceNames = this.getAllServiceNames();
        this.filterServiceNames(serviceNames, url);
        return serviceNames;
    }

    private Set<String> getAllServiceNames() {
        Set<String> serviceNames = new LinkedHashSet();
        this.execute((namingService) -> {
            int pageIndex = 1;
            ListView<String> listView = namingService.getServicesOfServer(pageIndex, PAGINATION_SIZE, this.getUrl().getParameter("group", "DEFAULT_GROUP"));
            List<String> firstPageData = listView.getData();
            serviceNames.addAll(firstPageData);
            int count = listView.getCount();
            int pageNumbers = count / PAGINATION_SIZE;
            int remainder = count % PAGINATION_SIZE;
            if (remainder > 0) {
                ++pageNumbers;
            }

            while(pageIndex < pageNumbers) {
                ++pageIndex;
                listView = namingService.getServicesOfServer(pageIndex, PAGINATION_SIZE, this.getUrl().getParameter("group", "DEFAULT_GROUP"));
                serviceNames.addAll(listView.getData());
            }

        });
        return serviceNames;
    }

    private void filterServiceNames(Set<String> serviceNames, URL url) {
        List<String> categories = this.getCategories(url);
        String targetServiceInterface = url.getServiceInterface();
        String targetVersion = url.getParameter("version", "");
        String targetGroup = url.getParameter("group", "");
        this.filterData(serviceNames, (serviceName) -> {
            String[] segments = serviceName.split(SERVICE_NAME_SEPARATOR, -1);
            int length = segments.length;
            if (length != 4) {
                return false;
            } else {
                String category = segments[0];
                if (!categories.contains(category)) {
                    return false;
                } else {
                    String serviceInterface = segments[1];
                    if (!"*".equals(targetServiceInterface) && !StringUtils.isEquals(targetServiceInterface, serviceInterface)) {
                        return false;
                    } else {
                        String version = segments[2];
                        if (!"*".equals(targetVersion) && !StringUtils.isEquals(targetVersion, version)) {
                            return false;
                        } else {
                            String group = segments[3];
                            return group == null || "*".equals(targetGroup) || StringUtils.isEquals(targetGroup, group);
                        }
                    }
                }
            }
        });
    }

    private <T> void filterData(Collection<T> collection, NacosRegistry.NacosDataFilter<T> filter) {
        collection.removeIf((data) -> {
            return !filter.accept(data);
        });
    }

    */
/** @deprecated *//*

    @Deprecated
    private List<String> doGetServiceNames(URL url) {
        List<String> categories = this.getCategories(url);
        List<String> serviceNames = new ArrayList(categories.size());
        Iterator var4 = categories.iterator();

        while(var4.hasNext()) {
            String category = (String)var4.next();
            String serviceName = this.getServiceName(url, category);
            serviceNames.add(serviceName);
        }

        return serviceNames;
    }

    private List<URL> toUrlWithEmpty(URL consumerURL, Collection<Instance> instances) {
        List<URL> urls = this.buildURLs(consumerURL, instances);
        if (urls.size() == 0) {
            URL empty = URLBuilder.from(consumerURL).setProtocol(Constants.EMPTY_PROTOCOL).build();
            urls.add(empty);
        }

        return urls;
    }

    private List<URL> buildURLs(URL consumerURL, Collection<Instance> instances) {
        List<URL> urls = new LinkedList();
        if (instances != null && !instances.isEmpty()) {
            Iterator var4 = instances.iterator();

            while(var4.hasNext()) {
                Instance instance = (Instance)var4.next();
                try{
                    URL url = this.buildURL(instance);
                    //  if (UrlUtils.isMatch(consumerURL, url)) {
                    urls.add(url);
                    //}
                }catch (Exception e){
                    logger.error("buildURLs 处理instance={}时，抛异常e={}", JSON.toJSONString(instance),e);
                }
            }
        }

        return urls;
    }

    private void subscribeEventListener(String serviceName, final URL url, final NotifyListener listener) throws NacosException {
        EventListener eventListener = (event) -> {
            if (event instanceof NamingEvent) {
                NamingEvent e = (NamingEvent)event;
                List<Instance> instances = e.getInstances();
                if (this.isServiceNamesWithCompatibleMode(url)) {
                    NacosInstanceManageUtil.initOrRefreshServiceInstanceList(serviceName, instances);
                    instances = NacosInstanceManageUtil.getAllCorrespondingServiceInstanceList(serviceName);
                }
                if(StringUtils.isBlank(serviceName)){
                    this.notifySubscriber(url, listener, instances);
                }else{
                    Map<String, String> parameters = new HashMap<>();
                    parameters.put("category",url.getParameter("side"));
                    parameters.put("interface", url.getParameter("interface"));
                    parameters.put("version", url.getParameter("version",""));
                    parameters.put("group", url.getParameter("group",""));
//                    String[] split = serviceName.split(":");
                    */
/*if(split.length == 4){
                        parameters.put("version", split[2]);
                        parameters.put("group", split[3]);
                    }else if(split.length == 3){
                        try{
                            Long.parseLong(split[2]);
                            parameters.put("version", split[2]);
                        }catch (NumberFormatException exception){
                            parameters.put("group", split[2]);
                        }
                    }*//*

                    URL url1 = new URL(url.getProtocol(),url.getHost(),url.getPort(),parameters);
                    this.notifySubscriber(url1, listener, instances);
                }
            }

        };
        this.namingService.subscribe(serviceName, this.getUrl().getParameter("group", "DEFAULT_GROUP"), eventListener);
    }

    private void notifySubscriber(URL url, NotifyListener listener, Collection<Instance> instances) {
        List<Instance> enabledInstances = new LinkedList(instances);
*/
/*        if (enabledInstances.size() > 0) {
            this.filterEnabledInstances(enabledInstances);
        }*//*


        List<URL> urls = this.toUrlWithEmpty(url, enabledInstances);
        this.notify(url, listener, urls);
    }

    private List<String> getCategories(URL url) {
        return "*".equals(url.getServiceInterface()) ? ALL_SUPPORTED_CATEGORIES : Collections.singletonList("providers");
    }

    private URL buildURL(Instance instance) {
        Map<String, String> metadata = instance.getMetadata();
        String protocol = (String)metadata.get("protocol");
        String path = (String)metadata.get("path");
        metadata.put(Constants.ENABLED_KEY,instance.isEnabled()+"");
        metadata.put(Constants.HEALTHY_KEY,instance.isHealthy()+"");
        return new URL(protocol, instance.getIp(), instance.getPort(), path, metadata);
    }

    private Instance createInstance(URL url) {
        String category = url.getParameter("category", "providers");
        URL newURL = url.addParameter("category", category);
        newURL = newURL.addParameter("protocol", url.getProtocol());
        newURL = newURL.addParameter("path", url.getPath());
        newURL = newURL.addParameters(NacosNamingServiceUtils.getNacosPreservedParam(this.getUrl()));
        String ip = url.getHost();
        int port = url.getPort();
        Instance instance = new Instance();
        instance.setIp(ip);
        instance.setPort(port);
        instance.setMetadata(new HashMap(newURL.getParameters()));
        return instance;
    }

    private String getServiceName(URL url) {
        return this.getServiceName(url, url.getParameter("category", "providers"));
    }

    private NacosServiceName createServiceName(URL url) {
        return NacosServiceName.valueOf(url);
    }

    private String getServiceName(URL url, String category) {
        return category + SERVICE_NAME_SEPARATOR + url.getColonSeparatedKey();
    }

    private void execute(NacosRegistry.NamingServiceCallback callback) {
        try {
            callback.callback(this.namingService);
        } catch (NacosException var3) {
            if (this.logger.isErrorEnabled()) {
                this.logger.error(var3.getErrMsg(), var3);
            }
        }

    }

    private void filterEnabledInstances(Collection<Instance> instances) {
        this.filterData(instances, Instance::isEnabled);
    }

    interface NamingServiceCallback {
        void callback(NacosNamingServiceWrapper namingService) throws NacosException;
    }

    private interface NacosDataFilter<T> {
        boolean accept(T data);
    }
}
*/
