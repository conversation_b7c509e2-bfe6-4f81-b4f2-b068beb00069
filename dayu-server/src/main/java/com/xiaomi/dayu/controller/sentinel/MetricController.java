package com.xiaomi.dayu.controller.sentinel;

import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping(value = "/api/metric", produces = MediaType.APPLICATION_JSON_VALUE)
public class MetricController {


    @ResponseBody
    @GetMapping("/queryTopResourceMetric.json")
    public ResultResponse<?> queryTopResourceMetric(HttpServletRequest request,
                                                    @RequestParam String app,
                                                    @RequestParam(required = false) Integer pageIndex,
                                                    @RequestParam(required = false) Integer pageSize,
                                                    @RequestParam(required = false) Long startTime,
                                                    @RequestParam(required = false) Long endTime,
                                                    @RequestParam(required = false) String searchKey) {
        CheckPermissUtil.checkPermissByAppName(app);
        return HttpClient.sentinelProxyHttp(request);
    }
}