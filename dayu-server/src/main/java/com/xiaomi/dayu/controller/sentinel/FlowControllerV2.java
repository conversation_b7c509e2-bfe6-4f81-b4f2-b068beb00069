/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.controller.sentinel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.sentinel.DegradeRuleEntity;
import com.xiaomi.dayu.model.sentinel.FlowRuleEntity;
import com.xiaomi.dayu.model.sentinel.FlowRuleInfo;
import com.xiaomi.dayu.service.NamingInstanceService;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Flow rule controller (v2).
 *
 * <AUTHOR> Zhao
 * @since 1.4.0
 */
@RestController
@RequestMapping(value = "/api/v2/flow")
public class FlowControllerV2 {

    private final Logger logger = LoggerFactory.getLogger(FlowControllerV2.class);

    private final static String FLOW_RULE_URI="v2/flow/rule";

    @Autowired
    private NamingInstanceService namingInstanceService;

    @GetMapping("/rules")
    public ResultResponse<Object> apiQueryMachineRules(HttpServletRequest request,@RequestParam String app) {
        CheckPermissUtil.checkPermissByAppName(app);
        ResultResponse<Object> response =  HttpClient.sentinelProxyHttp(request);
        if(response.getData()==null){
            return ResultResponse.success(null);
        }
        List<FlowRuleEntity> flowRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<FlowRuleEntity>>() {
        });
        if(CollectionUtils.isNotEmpty(flowRuleEntities)){

            List<FlowRuleInfo> flowRuleInfos = new ArrayList<>(flowRuleEntities.size());
            flowRuleEntities.forEach(entity->{
                FlowRuleInfo flowRuleInfo = buildFlowRuleInfo(entity);
                flowRuleInfos.add(flowRuleInfo);
            });
            flowRuleInfos.sort(new Comparator<FlowRuleInfo>() {
                @Override
                public int compare(FlowRuleInfo o1, FlowRuleInfo o2) {
                    if (o1.equals(o2)) return 0;

                    // 2. 比较 service
                    int serviceCompare = o1.getService().compareTo(o2.getService());
                    if (serviceCompare != 0) {
                        return serviceCompare; // 按 service 升序
                    }

                    // 3. 比较 method（处理空值）
                    if (StringUtils.isBlank(o1.getMethod())) {
                        return StringUtils.isBlank(o2.getMethod()) ? 0 : -1; // o1.method 空时排前
                    }
                    if (StringUtils.isBlank(o2.getMethod())) {
                        return 1; // o2.method 空时排后
                    }

                    // 4. 按 method 升序
                    return o1.getMethod().compareTo(o2.getMethod());
                }
            });
            return ResultResponse.success(flowRuleInfos);

        }
        return ResultResponse.success(null);
    }

    private FlowRuleInfo buildFlowRuleInfo(FlowRuleEntity flowRuleEntity) {
        FlowRuleInfo flowRuleInfo = new FlowRuleInfo();
        BeanUtils.copyProperties(flowRuleEntity,flowRuleInfo);

        fillEntity(flowRuleInfo);
        if(StringUtils.isNotBlank(flowRuleEntity.getDefaultFallbackMethod())){
            String[] split1 = flowRuleEntity.getDefaultFallbackMethod().split(Constants.COLON);
            flowRuleInfo.setFallbackClass(split1[0]);
            flowRuleInfo.setFallbackMethod(split1[1]);
        }
        return flowRuleInfo;
    }

    @PostMapping("/rule")
    public ResultResponse<Object> apiAddFlowRule(HttpServletRequest request,
                                                 @RequestParam String app,
                                                 @RequestParam@ApiParam(value = "服务名") String  service,
                                                 @RequestParam(required = false)@ApiParam(value = "方法列表") List<String> methodList,
                                                 @RequestParam(required = false)@ApiParam(value = "dubbo分组") String  dubboGroup,
                                                 @RequestParam(required = false)@ApiParam(value = "dubbo版本") String  dubboVersion,
                                                 @RequestBody FlowRuleEntity flowRuleEntity) {
        CheckPermissUtil.checkPermissByAppName(app);
        Map<String, String> params = new HashMap<>();
        params.put("app",app);
        ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request, "v2/flow/rules", RequestMethod.GET.name(), null,params);
        if(response.getData() != null){
            List<FlowRuleEntity> flowRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<FlowRuleEntity>>() {});
            if(CollectionUtils.isNotEmpty(flowRuleEntities)){
                List<String> resouceList=new ArrayList<>();
                if(CollectionUtils.isEmpty(methodList)){
                    resouceList.add(getResource(service,null,dubboGroup,dubboVersion));
                }else{
                    methodList.forEach(method->{
                        resouceList.add(getResource(service,method,dubboGroup,dubboVersion));
                    });
                }
                List<String> existResouceList=new ArrayList<>();
                flowRuleEntities.forEach(entity->{
                    if(resouceList.contains(entity.getResource())){
                        existResouceList.add(entity.getResource());

                    }
                });
                if(existResouceList.size() != 0){
                    throw new ParamValidationException(Joiner.on(",").join(existResouceList)+"已经存在不允许新增！");
                }
            }
        }

        batchAdd(request,  service, methodList, flowRuleEntity);
        return ResultResponse.success(true);
    }

    private void batchAdd(HttpServletRequest request,String service,List<String> methodList,FlowRuleEntity entity) {
        List<FlowRuleEntity> addList = new ArrayList<>();
        if(entity.isClusterMode() && entity.getClusterConfig() != null){
            entity.getClusterConfig().setFlowId(null);
        }
        String fallback =null;
        if(StringUtils.isNotBlank(entity.getFallbackClass()) && StringUtils.isNotBlank(entity.getFallbackMethod())){
            fallback = entity.getFallbackClass()+Constants.COLON+entity.getFallbackMethod();
        }
        if(CollectionUtils.isNotEmpty(methodList)){
            String finalFallback = fallback;
            methodList.forEach(method->{
                FlowRuleEntity flowRuleEntity = new FlowRuleEntity();
                BeanUtils.copyProperties(entity,flowRuleEntity);
                flowRuleEntity.setResource(getResource(service,method,entity.getDubboGroup(), entity.getDubboVersion()));
                flowRuleEntity.setDefaultFallbackMethod(finalFallback);
                addList.add(flowRuleEntity);
            });
        }else{
            entity.setDefaultFallbackMethod(fallback);
            entity.setResource(service);
            addList.add(entity);
        }
        if(CollectionUtils.isNotEmpty(addList)){
            HttpClient.sentinelProxyHttp(request,"v2/flow/rule/add/batch", RequestMethod.POST.name(),addList);
        }
    }

    @PutMapping("/rule/{id}")
    public ResultResponse<Object> apiUpdateFlowRule(HttpServletRequest request,
                                                    @RequestParam String app,
                                                    @PathVariable("id") Long id,
                                                    @RequestParam(required = false)@ApiParam(value = "方法") String method,
                                                    @RequestParam@ApiParam(value = "服务名") String  service,
                                                    @RequestParam(required = false)@ApiParam(value = "dubbo分组") String  dubboGroup,
                                                    @RequestParam(required = false)@ApiParam(value = "dubbo版本") String  dubboVersion,
                                                    @RequestBody FlowRuleInfo flowRuleInfo) {
        CheckPermissUtil.checkPermissByAppName(app);
        if(StringUtils.isNotBlank(flowRuleInfo.getFallbackClass()) && StringUtils.isNotBlank(flowRuleInfo.getFallbackMethod())){
            flowRuleInfo.setDefaultFallbackMethod(flowRuleInfo.getFallbackClass()+Constants.COLON+flowRuleInfo.getFallbackMethod());
        }
        HttpClient.sentinelProxyHttp(request,"v2/flow/rule/"+id, RequestMethod.PUT.name(),flowRuleInfo);
        return ResultResponse.success(true);
    }
   /* @PutMapping("/rule")
    public ResultResponse<Object> apiBatchUpdateFlowRule(HttpServletRequest request,
                                                    @RequestParam String app,
//                                                    @RequestParam(required = false) @ApiParam(value = "删除ids") List<Integer> ids,
                                                    @RequestParam(required = false)@ApiParam(value = "方法列表") List<String> methodList,
                                                    @RequestParam@ApiParam(value = "服务名") String  service,
                                                    @RequestBody(required = false) FlowRuleEntity flowRuleEntity) {
        CheckPermissUtil.checkPermissByAppName(app);
        Map<String, String> params = new HashMap<>();
        params.put("app",app);
        ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request, "v2/flow/rules", RequestMethod.GET.name(), null,params);
        if(response.getData() != null){
            List<FlowRuleEntity> flowRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<FlowRuleEntity>>() {});
            if(CollectionUtils.isNotEmpty(flowRuleEntities)){
                List<Long> collect = flowRuleEntities.stream().filter(
                        entity ->  entity.getId().equals(flowRuleEntity.getId())? true:false
                ).map(FlowRuleEntity::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(collect)){
                    HttpClient.sentinelProxyHttp(request,"v2/flow/rule/delete/batch",RequestMethod.POST.name(),collect);
                }
            }
        }
        batchAdd(request, service, methodList, flowRuleEntity);
        return ResultResponse.success(true);
    }*/
    /*@PutMapping("/rule/disabled")
    public Object batchDisabled(HttpServletRequest request,
                           @RequestParam String app,
                           @RequestParam String service,
                           @RequestParam String id,
                           @RequestParam@ApiParam(value = "0. open 1.close default 0") Integer disabled,
                           @RequestParam(required = false) List<String> methodList ) {
        CheckPermissUtil.checkPermissByAppName(app);
        Map<String, String> params = new HashMap<>();
        params.put("app",app);
        ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request, "v2/flow/rules", RequestMethod.GET.name(), null,params);
        if(response.getData() != null){
            List<FlowRuleEntity> flowRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<FlowRuleEntity>>() {});
            if(CollectionUtils.isNotEmpty(flowRuleEntities)){
                List<FlowRuleEntity> collect = flowRuleEntities.stream().filter(
                        entity -> {
                            if (entity.getId().equals(id)) {
                                entity.setIsClose(disabled);
                                return true;
                            }
                            return false;
                        }
                ).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(collect)){
                    HttpClient.sentinelProxyHttp(request,"v2/flow/rule/batch/update",RequestMethod.PUT.name(),collect);
                }
            }
        }
        return ResultResponse.success(true);
    }*/


    @DeleteMapping("/rule/{id}")
    public ResultResponse<Object> apiDeleteRule(HttpServletRequest request,@PathVariable("id") Long id,@RequestParam String app) {
        CheckPermissUtil.checkPermissByAppName(app);
        return HttpClient.sentinelProxyHttp(request);
    }
    public static void fillEntity(FlowRuleInfo entity) {
        String service =null;
        String method =null;
        String dubboGroup =null;
        String dubboVersion =null;
        String tmp =null;
        String[] splitWell = entity.getResource().split(Constants.WELL);
        if(splitWell.length>1){
            dubboGroup = splitWell[0];
            tmp = splitWell[1];
        }else{
            tmp = splitWell[0];
        }
        String[] splitColon = tmp.split(Constants.COLON);
        if(splitColon.length>1) {
            service = splitColon[0];
            tmp = splitColon[1];
        }else{
            tmp = splitColon[0];
        }
        String[] splitEit = tmp.split(Constants.EIT);
        if(splitEit.length>1) {
            dubboVersion = splitEit[1];
        }
        if(service==null){
            service = splitEit[0];
        }else{
            method = splitEit[0];
        }
        entity.setService(service);
        entity.setMethod(method);
        entity.setDubboGroup(dubboGroup);
        entity.setDubboVersion(dubboVersion);
    }
    private String getResource(String service,String method,String dubboGroup,String dubboVersion){
        StringBuffer stringBuffer = new StringBuffer();
        if(StringUtils.isNotBlank(dubboGroup)){
            stringBuffer.append(dubboGroup).append(Constants.WELL);
        }
        stringBuffer.append(service);
        if(StringUtils.isNotBlank(method)){
            stringBuffer.append(Constants.COLON).append(method);
        }
        if(StringUtils.isNotBlank(dubboVersion)){
            stringBuffer.append(Constants.EIT).append(dubboVersion);
        }
        return stringBuffer.toString();
    }
}
