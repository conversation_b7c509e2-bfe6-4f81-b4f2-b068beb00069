package com.xiaomi.dayu.controller.iauth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.model.iauth.CreateIAuthWhiteReq;
import com.xiaomi.dayu.model.iauth.GrantedInfoSummary;
import com.xiaomi.dayu.model.iauth.SwappedWhiteInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
@Deprecated
@Slf4j
@Api(description = "申请-查询-删除权限")
@RestController
@RequestMapping("/api/iauth/api/whitelist")
public class IAuthWhitelistController {

    @GetMapping("/service")
    public ResultResponse<List<GrantedInfoSummary> > getIAuthWhitelist(HttpServletRequest request,
                                                                       @RequestParam("serviceId") String serviceId,
                                                                       @ApiParam(value = "0: 待授权， 1：取消授权, 2：已授权")@RequestParam(required = false) Integer status)  {
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request);
        if(response.isSuccess()) {
            List<SwappedWhiteInfo> swappedWhiteInfos = JSON.parseObject(response.getData(), new TypeReference<List<SwappedWhiteInfo>>() {
            });
            if(CollectionUtils.isNotEmpty(swappedWhiteInfos)){
                List<GrantedInfoSummary> list = new ArrayList<>();
                swappedWhiteInfos.stream().map(swappedWhiteInfo->{
                    if(StringUtils.isBlank(swappedWhiteInfo.getSubService())){
                        swappedWhiteInfo.setSubService("*");
                    }
                    return swappedWhiteInfo;
                }).filter(swappedWhiteInfo -> StringUtils.isNotEmpty(swappedWhiteInfo.getSubService())).collect(Collectors.groupingBy(SwappedWhiteInfo::getSubService)).forEach((key, value)->{
                    GrantedInfoSummary grantedInfoSummary = new GrantedInfoSummary();
                    if(StringUtils.isBlank(key) || key.equals("*")){
                        grantedInfoSummary.setLevel(1);
                        grantedInfoSummary.setName(serviceId);
                    }else{
                        grantedInfoSummary.setLevel(2);
                        grantedInfoSummary.setName(key);
                    }
                    grantedInfoSummary.setSwappedWhiteInfoList(value);
                    //0: 待授权， 1：取消授权, 2：已授权
                    grantedInfoSummary.setUnGrantedCount((int) value.stream().filter(swappedWhiteInfo -> swappedWhiteInfo.getAuthNStatus().equals(0)).count());
                    grantedInfoSummary.setGrantedCount((int) value.stream().filter(swappedWhiteInfo -> swappedWhiteInfo.getAuthNStatus().equals(2)).count());
                    list.add(grantedInfoSummary);
                });
                return ResultResponse.success(list);
            }
        }
        return ResultResponse.success(null);
    }

    @GetMapping("/app")
    public ResultResponse<String> getIAuthWhiteApplist(HttpServletRequest request,
                                                       @RequestParam("appId") long appId,
                                                       @RequestParam("appName") String appName,
                                                       @ApiParam(value = "0: 待授权， 1：取消授权, 2：已授权")@RequestParam(required = false) Integer status)  {
        return HttpClient.iauthProxyHttp(request);
    }

    @PostMapping("")
    public ResultResponse<Boolean> createIAuthWhitelist(HttpServletRequest request,
                                                       @RequestParam("appId") long appId,
                                                       @RequestParam("appName") String appName,
                                                       @RequestParam(value = "subApp",required = false) String subApp,
                                                       @RequestParam(value = "message") String message,
                                                       @RequestBody List<CreateIAuthWhiteReq> createList)  {
        HashMap<String, Object> params = new HashMap<>();
        params.put("appId",appId+"");
        params.put("appName",appName);
        params.put("subApps",StringUtils.isNotEmpty(subApp) ? subApp:"*");
        params.put("message",message);
        if(CollectionUtils.isNotEmpty(createList)){
            createList.forEach(create->{
                params.put("subServices",CollectionUtils.isNotEmpty(create.getSubServices()) ? Joiner.on(",").join(create.getSubServices()):"*");
                params.put("serviceId",create.getServiceId());
                ResultResponse<String> response = HttpClient.iauthProxyHttp(request, "/api/whitelist", RequestMethod.POST, null,params);
                if(!response.isSuccess()){
                    log.error("申请权限http接口异常，projectId={}，data={}",JSON.toJSONString(response.getData()));
                }
            });
        }
        AppListController.updateIauthConfig(request,appName,appId);
        return ResultResponse.success(true);
    }

    @PostMapping("post")
    public ResultResponse<Boolean> postIAuthWhitelist(HttpServletRequest request,
                                                        @RequestParam("appId") long appId,
                                                        @RequestParam("appName") String appName){
        AppListController.updateIauthConfig(request,appName,appId);
        return null;
    }

    @DeleteMapping("")
    public ResultResponse<String> deleteIAuthWhitelist(HttpServletRequest request,
                                                       @ApiParam(value = "根据id 删除")@RequestParam("ids") List<Long> ids,
                                                       @RequestParam("serviceId") String serviceId,
                                                       @RequestParam("appName") String appName,
                                                       @RequestParam("message") String message,
                                                       @RequestParam(value = "platform",required = false) String platform)  {
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        bodyParams.put("ids", ids);
        return HttpClient.iauthProxyHttp(request,bodyParams);
      
    }
    @PostMapping("/update")
    public ResultResponse<String> updateIAuthWhitelist(HttpServletRequest request,
                                       @RequestParam("id") long id,
                                       @RequestParam("serviceId") String serviceId,
                                       @RequestParam("appName") String appName,
                                       @RequestParam(required = false) String platform,
                                       @RequestParam("message") String message,
                                       @RequestParam("status")@ApiParam(value = "/* 0: 提交授权申请， 1：取消授权, 2：同意申请, 3: 驳回申请, 4: 开启鉴权，5: 关闭鉴权>*/") Integer status){
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        return HttpClient.iauthProxyHttp(request,bodyParams);
    }
}
