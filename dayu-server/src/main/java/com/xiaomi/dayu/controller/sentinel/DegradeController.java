/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.controller.sentinel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.sentinel.DegradeRuleEntity;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller regarding APIs of degrade rules. Refactored since 1.8.0.
 *
 * <AUTHOR> Lee
 * <AUTHOR> Zhao
 */
@RestController
@RequestMapping("/api/degrade")
public class DegradeController {

    private final Logger logger = LoggerFactory.getLogger(DegradeController.class);
    private final static String DEGRADE_RULE_URI="degrade/rule/";

    @GetMapping("/rules.json")
    public Object apiQueryMachineRules(HttpServletRequest request,
                                       @RequestParam String app,
                                       @RequestParam String ip,
                                       @RequestParam Integer port) {

        try {
            ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request);
            if(response.getData()==null){
                return ResultResponse.success(null);
            }
            List<DegradeRuleEntity> degradeRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<DegradeRuleEntity>>() {
            });
            //由于sentinel数据调整之后有延迟
            Thread.sleep(100);
            if(CollectionUtils.isNotEmpty(degradeRuleEntities)){

                degradeRuleEntities.forEach(entity -> {
                    fillEntity(entity);
                    if(StringUtils.isNotBlank(entity.getDefaultFallbackMethod())){
                        String[] split1 = entity.getDefaultFallbackMethod().split(Constants.COLON);
                        entity.setFallbackClass(split1[0]);
                        entity.setFallbackMethod(split1[1]);
                    }
                });
                return ResultResponse.success(degradeRuleEntities);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ResultResponse.success(null);
    }

    public static void fillEntity(DegradeRuleEntity entity) {
        String service =null;
        String method =null;
        String dubboGroup =null;
        String dubboVersion =null;
        String tmp =null;
        String[] splitWell = entity.getResource().split(Constants.WELL);
        if(splitWell.length>1){
            dubboGroup = splitWell[0];
            tmp = splitWell[1];
        }else{
            tmp = splitWell[0];
        }
        String[] splitColon = tmp.split(Constants.COLON);
        if(splitColon.length>1) {
            service = splitColon[0];
            tmp = splitColon[1];
        }else{
            tmp = splitColon[0];
        }
        String[] splitEit = tmp.split(Constants.EIT);
        if(splitEit.length>1) {
            dubboVersion = splitEit[1];
        }
        if(service==null){
            service = splitEit[0];
        }else{
            method = splitEit[0];
        }
        entity.setService(service);
        entity.setMethod(method);
        entity.setDubboGroup(dubboGroup);
        entity.setDubboVersion(dubboVersion);
    }
    @PostMapping("/rule")
    public Object apiAddRule(HttpServletRequest request,
                             @RequestParam String app,
                             @RequestParam String ip,
                             @RequestParam String port,
                             @RequestParam@ApiParam(value = "服务名") String  service,
                             @RequestParam(required = false)@ApiParam(value = "dubbo分组") String  dubboGroup,
                             @RequestParam(required = false)@ApiParam(value = "dubbo版本") String  dubboVersion,
                             @RequestParam(required = false)@ApiParam(value = "方法列表") List<String> methodList,
                             @RequestBody DegradeRuleEntity degradeRuleEntity) {

        CheckPermissUtil.checkPermissByAppName(app);
        HashMap<String, String> map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(methodList)){
            methodList.forEach(method->{
                String resource = getResource(service, method, dubboGroup, dubboVersion);
                map.put(resource,resource);
            });
        }else{
            String resource = getResource(service, null, dubboGroup, dubboVersion);
            map.put(resource,resource);
        }
        Map<String, String> params = new HashMap<>();
        params.put("app",app);
        params.put("ip",ip);
        params.put("port",port);
        ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request, "degrade/rules.json", RequestMethod.GET.name(), null,params);
        if(response.getData() != null){
            List<DegradeRuleEntity> degradeRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<DegradeRuleEntity>>() {});
            if(CollectionUtils.isNotEmpty(degradeRuleEntities)){
                degradeRuleEntities.forEach(entity->{
                    if(map.containsKey(entity.getResource())){
                       throw new ParamValidationException("服务名:"+service+",方法名："+map.get(entity.getResource())+"已经存在不允许新增！");
                    }
                });
            }
        }
        List<DegradeRuleEntity> entityList = new ArrayList<>();
        map.forEach((key,value)->{
            DegradeRuleEntity copy = new DegradeRuleEntity();
            BeanUtils.copyProperties(degradeRuleEntity,copy);
            copy.setResource(key);
            if(StringUtils.isNotBlank(degradeRuleEntity.getFallbackClass()) && StringUtils.isNotBlank(degradeRuleEntity.getFallbackMethod())){
                copy.setDefaultFallbackMethod(degradeRuleEntity.getFallbackClass()+Constants.COLON+degradeRuleEntity.getFallbackMethod());
            }
            copy.setApp(app);
            entityList.add(copy);
        });
        HttpClient.sentinelProxyHttp(request,"degrade/rule/add/batch",RequestMethod.POST.name(),entityList);
        return ResultResponse.success(true);
    }

    /*@PutMapping("/rule")
    public Object apiUpdateRule(HttpServletRequest request,
                                @RequestParam(required = false) @ApiParam(value = "删除ids") List<Long> ids,
                                @RequestBody(required = false) List<DegradeRuleEntity> entityList) {
        CheckPermissUtil.checkPermissByAppName(entityList.get(0).getApp());
        if(CollectionUtils.isNotEmpty(ids)){
            HttpClient.sentinelProxyHttp(request,"degrade/rule/delete/batch",RequestMethod.DELETE.name(),ids);
        }
        if(CollectionUtils.isNotEmpty(entityList)){
            Map<Boolean, List<DegradeRuleEntity>> collect = entityList.stream().collect(Collectors.groupingBy(entity -> entity.getId() == null));
            if(CollectionUtils.isNotEmpty(collect.get(true))){
                List<DegradeRuleEntity> degradeRuleEntities = collect.get(true);
                degradeRuleEntities.forEach(entity->{
                    if(StringUtils.isNotBlank(entity.getFallbackClass()) && StringUtils.isNotBlank(entity.getFallbackMethod())){
                        entity.setDefaultFallbackMethod(entity.getFallbackClass()+Constants.COLON+entity.getFallbackMethod());
                    }
                });
                HttpClient.sentinelProxyHttp(request,"degrade/rule/add/batch",RequestMethod.POST.name(),degradeRuleEntities);
            }
            if(CollectionUtils.isNotEmpty(collect.get(false))){
                List<DegradeRuleEntity> degradeRuleEntities = collect.get(false);
                degradeRuleEntities.forEach(entity->{
                    if(StringUtils.isNotBlank(entity.getFallbackClass()) && StringUtils.isNotBlank(entity.getFallbackMethod())){
                        entity.setDefaultFallbackMethod(entity.getFallbackClass()+Constants.COLON+entity.getFallbackMethod());
                    }
                });
                HttpClient.sentinelProxyHttp(request,"degrade/rule/update/batch",RequestMethod.PUT.name(),degradeRuleEntities);
            }
        }
        return ResultResponse.success(true);
    }*/


    @PutMapping("/rule/disabled")
    public Object disabled(HttpServletRequest request,
                           @RequestParam String app,
                           @RequestParam String ip,
                           @RequestParam String port,
                           @RequestParam@ApiParam(value = "服务名") String  service,
                           @RequestParam@ApiParam(value = "方法") String  method,
                           @RequestParam(required = false)@ApiParam(value = "dubbo分组") String  dubboGroup,
                           @RequestParam(required = false)@ApiParam(value = "dubbo版本") String  dubboVersion,
                           @RequestParam@ApiParam(value = "0. open 1.close default 0") Integer disabled) {
        CheckPermissUtil.checkPermissByAppName(app);
        Map<String, String> params = new HashMap<>();
        params.put("app",app);
        params.put("ip",ip);
        params.put("port",port);
        ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request, "degrade/rules.json", RequestMethod.GET.name(), null,params);
        if(response.getData() != null){
            List<DegradeRuleEntity> degradeRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<DegradeRuleEntity>>() {});
            if(CollectionUtils.isNotEmpty(degradeRuleEntities)){
                List<DegradeRuleEntity> collect = degradeRuleEntities.stream().filter(entity -> {
                    if (entity.getResource().equals(getResource(service,method,dubboGroup,dubboVersion))) {
                        entity.setIsClose(disabled);
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(collect)){
                    HttpClient.sentinelProxyHttp(request,"degrade/rule/update/batch",RequestMethod.PUT.name(),collect);
                }

            }
        }
        return ResultResponse.success(true);
    }

    @PutMapping("/rule/{id}")
    public Object apiUpdateRule(HttpServletRequest request,@PathVariable("id") Long id,@RequestBody DegradeRuleEntity entity) {
        CheckPermissUtil.checkPermissByAppName(entity.getApp());
        entity.setResource(getResource(entity.getService(),entity.getMethod(),entity.getDubboGroup(),entity.getDubboVersion()));

        if(StringUtils.isNotBlank(entity.getFallbackClass()) && StringUtils.isNotBlank(entity.getFallbackMethod())){
            entity.setDefaultFallbackMethod(entity.getFallbackClass()+Constants.COLON+entity.getFallbackMethod());
        }
        return HttpClient.sentinelProxyHttp(request,DEGRADE_RULE_URI+entity.getId(),RequestMethod.PUT.name(),entity);
    }

    @DeleteMapping("/rule/{id}")
    public Object delete(HttpServletRequest request,@RequestParam String app,@PathVariable("id") Long id) {
        CheckPermissUtil.checkPermissByAppName(app);
        return HttpClient.sentinelProxyHttp(request);
    }
    private String getResource(String service,String method,String dubboGroup,String dubboVersion){
        StringBuffer stringBuffer = new StringBuffer();
        if(StringUtils.isNotBlank(dubboGroup)){
            stringBuffer.append(dubboGroup).append(Constants.WELL);
        }
        stringBuffer.append(service);
        if(StringUtils.isNotBlank(method)){
            stringBuffer.append(Constants.COLON).append(method);
        }
        if(StringUtils.isNotBlank(dubboVersion)){
            stringBuffer.append(Constants.EIT).append(dubboVersion);
        }
        return stringBuffer.toString();
    }
}
