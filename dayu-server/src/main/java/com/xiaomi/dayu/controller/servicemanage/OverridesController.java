package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.exception.VersionValidationException;
import com.xiaomi.dayu.model.dto.DynamicConfigDTO;
import com.xiaomi.dayu.model.store.OverrideConfig;
import com.xiaomi.dayu.service.OverrideService;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.impl.ValidationPermissionService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Iterator;
import java.util.List;

/**
 * 动态配置
 */
@RestController
@RequestMapping("/api/rules/override")
public class OverridesController {

    private final OverrideService overrideService;
    private final ProviderService providerService;
    @Autowired
    private ValidationPermissionService validationPermissionService;

    @Autowired
    public OverridesController(OverrideService overrideService, ProviderService providerService) {
        this.overrideService = overrideService;
        this.providerService = providerService;
    }

    @RequestMapping(method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public ResultResponse<Boolean> createOverride(@RequestBody DynamicConfigDTO overrideDTO) {
        //只支持service
        overrideDTO.setScope("service");
        overrideDTO.setEnabled(true);
        String scope = overrideDTO.getScope();
        if (StringUtils.isEmpty(scope)) {
            throw new ParamValidationException("scope are Empty!");
        }
        validationPermissionService.checkPermission(overrideDTO);

        String application = overrideDTO.getApplication();
        if (StringUtils.isNotEmpty(application) && providerService.findVersionInApplication(application).equals("2.6")) {
            throw new VersionValidationException("dubbo 2.6 does not support application scope dynamic config");
        }
        Iterator<OverrideConfig> iterator = overrideDTO.getConfigs().iterator();
        while(iterator.hasNext()){
            OverrideConfig nextConfig = iterator.next();
            if(CollectionUtils.isEmpty(nextConfig.getAddresses())){
                iterator.remove();
            }else if(nextConfig.getAddresses().contains("0.0.0.0:*")){
                nextConfig.getAddresses().remove("0.0.0.0:*");
            }
        }
        overrideService.saveOverride(overrideDTO,null);
        return ResultResponse.success(true);
    }

    @RequestMapping( method = RequestMethod.PUT)
    public ResultResponse<Boolean> updateOverride( @RequestBody DynamicConfigDTO overrideDTO) {
        overrideDTO.setScope("service");
        overrideDTO.setEnabled(true);
        validationPermissionService.checkPermission(overrideDTO);
        
//        DynamicConfigDTO old = overrideService.findOverride(overrideDTO);
//        if (old == null) {
//            throw new ResourceNotFoundException("Unknown ID!");
//        }
        overrideService.updateOverride(overrideDTO,"weight");
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "queryConfig", method = RequestMethod.GET)
    public ResultResponse<List<DynamicConfigDTO>> queryConfig(@RequestParam String application, @RequestParam String side,@RequestParam(defaultValue = "weight") String type) {
        List<DynamicConfigDTO> list = overrideService.queryDynamicConfig(application);
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<DynamicConfigDTO> iterator = list.iterator();
            while (iterator.hasNext()) {
                DynamicConfigDTO next = iterator.next();

                List<OverrideConfig> configs = next.getConfigs();
                if (CollectionUtils.isNotEmpty(configs)) {
                    Iterator<OverrideConfig> configIterator = configs.iterator();
                    while (configIterator.hasNext()) {
                        OverrideConfig config = configIterator.next();
                        if (!side.equals(config.getSide())) {
                            configIterator.remove();
                            continue;
                        }
//                        if("weight".equals(type)){
//                            if(!"weight".equals(config.getType())){
//                                configIterator.remove();
//                            }
//                        }
                    }
                }
                if (CollectionUtils.isEmpty(configs)) {
                    iterator.remove();
                } else {
                    /*if(next.getScope() == "service"){
                        String[] split = next.getApplication().split(":");
                        next.setService(split[0]);
                        next.setServiceVersion(split[1]);
                        next.setServiceGroup(split[2]);
                    }*/
                }
            }
        }
        return ResultResponse.success(list);
    }


    @RequestMapping(value = "/general",method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public ResultResponse<Boolean> createCommonOverride(@RequestBody DynamicConfigDTO overrideDTO) {
        //只支持service
        overrideDTO.setScope("service");
        overrideDTO.setEnabled(true);
        String scope = overrideDTO.getScope();
        if (StringUtils.isEmpty(scope)) {
            throw new ParamValidationException("scope are Empty!");
        }
        validationPermissionService.checkPermission(overrideDTO);

        String application = overrideDTO.getApplication();
        if (StringUtils.isNotEmpty(application) && providerService.findVersionInApplication(application).equals("2.6")) {
            throw new VersionValidationException("dubbo 2.6 does not support application scope dynamic config");
        }
        overrideService.saveOverride(overrideDTO,"general");
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/general", method = RequestMethod.PUT)
    public ResultResponse<Boolean> updateGeneralOverride( @RequestBody DynamicConfigDTO overrideDTO) {
        overrideDTO.setScope("service");
        overrideDTO.setEnabled(true);
        validationPermissionService.checkPermission(overrideDTO);

//        DynamicConfigDTO old = overrideService.findOverride(overrideDTO);
//        if (old == null) {
//            throw new ResourceNotFoundException("Unknown ID!");
//        }
        overrideService.updateOverride(overrideDTO,"general");
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "queryAll", method = RequestMethod.GET)
    public ResultResponse<List<DynamicConfigDTO>> queryAll() {
        List<DynamicConfigDTO> findAll = overrideService.findAllOverrides();
        return ResultResponse.success(findAll);
    }



    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public DynamicConfigDTO detailOverride(@PathVariable String id) {
        DynamicConfigDTO override = overrideService.findOverride(id);
        if (override == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }
        return override;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    public ResultResponse<Boolean> deleteOverride(@PathVariable String id, @RequestParam String side, @RequestParam String type) {
        overrideService.deleteOverride(id, side, type);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/enable/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> enableRoute(@PathVariable String id, @RequestParam String side, @RequestParam String type) {
        overrideService.enableOverride(id, side, type);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/disable/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> disableRoute(@PathVariable String id, @RequestParam String side, @RequestParam String type) {
        overrideService.disableOverride(id, side, type);
        return ResultResponse.success(true);
    }
}
