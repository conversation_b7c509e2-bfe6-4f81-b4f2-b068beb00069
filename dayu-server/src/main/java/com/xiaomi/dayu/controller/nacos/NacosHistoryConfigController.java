package com.xiaomi.dayu.controller.nacos;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.constants.Constants;
import com.xiaomi.dayu.model.dto.NacosHistoryConfigPageDTO;
import com.xiaomi.dayu.mybatis.entity.NacosHistoryConfig;
import com.xiaomi.dayu.service.NacosConfigExtendService;
import com.xiaomi.dayu.service.NacosHistoryConfigService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @since 2022/1/10
 *
 * App name checking is not implemented because if a config file is deleted, the get config api returns empty.
 */
@Slf4j
@Api(tags = {"Nacos history APIs"})
@RestController
@RequestMapping(Constants.HISTORY_CONTROLLER_PATH)
public class NacosHistoryConfigController {

    @Autowired
    private NacosConfigExtendService nacosConfigExtendService;

    @Autowired
    private NacosHistoryConfigService nacosHistoryConfigService;

    @RequestMapping(params = "search=accurate", method = RequestMethod.GET)
    @ResponseBody
    public ResultResponse<Object> listConfigHistory(HttpServletRequest request,
                                                     @RequestParam("dataId") String dataId,
                                                     @RequestParam("group") String group,
                                                     @RequestParam(value = "tenant", required = false,
                                                             defaultValue = StringUtils.EMPTY) String tenant,
                                                     @RequestParam(value = "appName", required = false) String appName,
                                                     @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                     @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        NacosHistoryConfigPageDTO nacosHistoryConfigPageDTO = nacosHistoryConfigService.listConfigHistory(dataId, group, tenant, appName, pageNo, pageSize);
        return ResultResponse.success(nacosHistoryConfigPageDTO);
    }

    /**
     * 查看配置历史信息详情
     */
    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    public ResultResponse<Object> getConfigHistoryInfo(HttpServletRequest request,
                                                       @RequestParam("nid") Long nid,
                                                       ModelMap modelMap,
                                                       @RequestParam("dataId") String dataId,
                                                       @RequestParam("group") String group,
                                                       @RequestParam(value = "tenant", required = false,
                                                               defaultValue = StringUtils.EMPTY) String tenant) {
        NacosHistoryConfig nacosHistoryConfig = nacosHistoryConfigService.getConfigHistoryInfo(nid, dataId, group, tenant);

        if (nacosHistoryConfig != null) {
            String json = JacksonUtils.toJson(nacosHistoryConfig);
            JSONObject jsonObject = JSONObject.parseObject(json);
            this.nacosConfigExtendService.extendDataById(jsonObject, nacosHistoryConfig.getId());
            return ResultResponse.success(jsonObject);
        } else {
            log.warn("config history data is empty,  nid {}, dataId {}, group {}, tenant {}", nid, dataId, group, tenant);
            return ResultResponse.success(null);
        }
    }
}
