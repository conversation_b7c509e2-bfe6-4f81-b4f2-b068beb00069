package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.exception.VersionValidationException;
import com.xiaomi.dayu.model.dto.ConditionRouteDTO;
import com.xiaomi.dayu.service.ConsumerService;
import com.xiaomi.dayu.service.RouteService;
import com.xiaomi.dayu.service.impl.ValidationPermissionService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.constants.CommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 条件路由
 */
@Deprecated
@RestController
@RequestMapping("/api/rules/route/condition")
public class ConditionRoutesController {

    private final RouteService routeService;
    private final ConsumerService consumerService;
    @Autowired
    private ValidationPermissionService validationPermissionService;
    @Autowired
    public ConditionRoutesController(RouteService routeService, ConsumerService consumerService) {
        this.routeService = routeService;
        this.consumerService = consumerService;
    }

    @RequestMapping(method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public ResultResponse<Boolean> createRule(@RequestBody ConditionRouteDTO routeDTO) {
        validationPermissionService.checkPermission(routeDTO);

        String serviceName = routeDTO.getService();
        String app = routeDTO.getApplication();
        if (StringUtils.isEmpty(serviceName) && StringUtils.isEmpty(app)) {
            throw new ParamValidationException("serviceName and app is Empty!");
        }
        if (StringUtils.isNotEmpty(app) && consumerService.findVersionInApplication(app).equals("2.6")) {
            throw new VersionValidationException("dubbo 2.6 does not support application scope routing rule");
        }
        routeService.createConditionRoute(routeDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> updateRule(@PathVariable String id, @RequestBody ConditionRouteDTO newConditionRoute) {
        validationPermissionService.checkPermission(newConditionRoute);

        ConditionRouteDTO oldConditionRoute = routeService.findConditionRoute(id);
        if (oldConditionRoute == null) {
            throw new ResourceNotFoundException("can not find route rule for: " + id);
        }
        routeService.updateConditionRoute(newConditionRoute);
        return ResultResponse.success(true);
    }

    @RequestMapping(method = RequestMethod.GET)
    public ResultResponse<List<ConditionRouteDTO>> searchRoutes(@RequestParam(required = false) String application,
                                                      @RequestParam(required = false) String service,
                                                      @RequestParam(required = false) String serviceVersion,
                                                      @RequestParam(required = false) String serviceGroup) {
        validationPermissionService.checkPermission(CommonConstants.CONSUMER_SIDE,application, service, serviceGroup, serviceVersion);

        ConditionRouteDTO conditionRoute;
        List<ConditionRouteDTO> result = new ArrayList<>();
        ConditionRouteDTO crDTO = new ConditionRouteDTO();
        if (StringUtils.isNotBlank(application)) {
            crDTO.setApplication(application);
            conditionRoute = routeService.findConditionRoute(crDTO);
        } else if (StringUtils.isNotBlank(service)) {
            crDTO.setService(service);
            crDTO.setServiceVersion(serviceVersion);
            crDTO.setServiceGroup(serviceGroup);
            conditionRoute = routeService.findConditionRoute(crDTO);
        } else {
            throw new ParamValidationException("Either Service or application is required.");
        }
        if (conditionRoute != null && conditionRoute.getConditions() != null) {
            result.add(conditionRoute);
        }
        return ResultResponse.success(result);
    }
    @RequestMapping(value = "queryAll",method = RequestMethod.GET)
    public ResultResponse<List<ConditionRouteDTO>> queryAll(){
        List<ConditionRouteDTO> findAll = routeService.findAllConditionRoute();
        return ResultResponse.success(findAll);
    }
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public ConditionRouteDTO detailRoute(@PathVariable String id) {
        ConditionRouteDTO conditionRoute = routeService.findConditionRoute(id);
        if (conditionRoute == null || conditionRoute.getConditions() == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }
        return conditionRoute;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    public ResultResponse<Boolean> deleteRoute(@PathVariable String id) {
        routeService.deleteConditionRoute(id);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/enable/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> enableRoute(@PathVariable String id) {
        routeService.enableConditionRoute(id);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/disable/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> disableRoute(@PathVariable String id) {

        routeService.disableConditionRoute(id);
        return ResultResponse.success(true);
    }

}
