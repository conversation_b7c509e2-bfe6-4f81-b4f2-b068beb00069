package com.xiaomi.dayu.controller.servicemanage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.aegis.config.AegisConfig;
import com.xiaomi.aegis.utils.AegisSignUtil;
import com.xiaomi.aegis.vo.UserInfoVO;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.service.UserService;
import com.xiaomi.youpin.hermes.bo.response.Account;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.SignatureException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户登录
 */
@RestController
@RequestMapping("/api/user")
public class UserController {
    public static Map<String /*token*/, User /*user info*/> tokenMap = new ConcurrentHashMap<>();

    @Value("${admin.check.sessionTimeoutMilli:3600000}")
    private long sessionTimeoutMilli;

    @Autowired
    private UserService userService;
    @Autowired
    private AccountServiceRpc accountServiceRpc;



    @RequestMapping(value = "/all/list", method = RequestMethod.GET)
    public ResultResponse<List<Account>> getAllAccountList(HttpServletRequest request, HttpServletResponse response) {
        return ResultResponse.success(accountServiceRpc.getAllAccountList());
    }
    @RequestMapping(value = "/ticket", method = RequestMethod.GET)
    public ResultResponse<Boolean> ticket(HttpServletRequest request, HttpServletResponse response) {
        Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        String uid = (String) assertion.getAttributes().get("uid");
        String userName = (String) assertion.getAttributes().get("username");
        response.addHeader("depart",userService.queryFullDeptByUid(uid,userName));//1：全部，2：中国区，3：有品，
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/login", method = RequestMethod.GET)
    public Map<String,String> login(HttpServletRequest request, HttpServletResponse response) throws SignatureException {
        UserInfo userInfoThreadLocal = UserInfoThreadLocal.getUserInfo();
        String username = userInfoThreadLocal.getUserName();
        if(StringUtils.isNotBlank(username)){
            UUID uuid = UUID.randomUUID();
            String token = uuid.toString();
            User user = new User();
            user.setUserName(username);
            user.setLastUpdateTime(System.currentTimeMillis());
            tokenMap.put(token, user);

            Map<String,String> result = new HashMap<>();
            result.put("token",token);


//            Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
            UserInfoVO userInfo = (UserInfoVO)request.getAttribute("user-info");
            if(userInfo != null){
                result.put("username",userInfo.getUser());
                result.put("userImg",userInfo.getAvatar());
            }else{
                result.put("username",username);
            }


            String signAndUserSignData = request.getHeader("x-proxy-userdetail");
            if(StringUtils.isNotBlank(signAndUserSignData)){

                String[] publicKeys = AegisConfig.publicKeys;
                int len = publicKeys.length;
                String userJson = "";
                for(int i = 0; i < len; ++i) {
                    String key = publicKeys[i];
                    String verifyIdentityData = AegisSignUtil.verifySignGetInfo(signAndUserSignData, key);
                    if (StringUtils.isNotEmpty(verifyIdentityData)) {
                        userJson = verifyIdentityData;
                        break;
                    }
                }
                JSONObject jsonObject = JSON.parseObject(userJson);
//                String uid = jsonObject.getString("cas:uid");
/*                Pair<String, Boolean> pair = userService.queryDepAndAdminByUid(uid, username);
                String depart = pair.getLeft().toString();*/
                result.put("depart", request.getHeader("depart"));
//                response.addHeader("depart", request.getHeader("depart"));//1：全部，2：中国区，3：有品，
                result.put("userImg",jsonObject.getString("cas:avatar"));
            }

            String isAdmin = userService.checkAdmin(username) ? "1" : "0";
            result.put("is_admin", isAdmin);
            return result;
        }
        return null;
    }
/*
    @RequestMapping(value = "/login", method = RequestMethod.GET)
    public String login(@RequestParam String userName, @RequestParam String password) {
        if (StringUtils.isBlank(rootUserName) || (rootUserName.equals(userName) && rootUserPassword.equals(password))) {
            UUID uuid = UUID.randomUUID();
            String token = uuid.toString();
            User user = new User();
            user.setUserName(userName);
            user.setLastUpdateTime(System.currentTimeMillis());
            tokenMap.put(token, user);
            return token;
        }
        return null;
    }
*/

    @RequestMapping(value = "/logout")
    public boolean logout() {
        HttpServletRequest request =
                ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String token = request.getHeader("Authorization");
        return null != tokenMap.remove(token);
    }
/*    @RequestMapping(value = "/clearUserCache",method = RequestMethod.GET)
    public boolean clearUserCache() {
        UserApplicationInterceptor.clearUserInfoCache();
        return true;
    }*/

    @Scheduled(cron= "0 5 * * * ?")
    public void clearExpiredToken() {
        tokenMap.entrySet().removeIf(entry -> entry.getValue() == null || System.currentTimeMillis() - entry.getValue().getLastUpdateTime() > sessionTimeoutMilli);
    }

    public static class User {
        private String userName;
        private long lastUpdateTime;

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public long getLastUpdateTime() {
            return lastUpdateTime;
        }

        public void setLastUpdateTime(long lastUpdateTime) {
            this.lastUpdateTime = lastUpdateTime;
        }
    }

}
