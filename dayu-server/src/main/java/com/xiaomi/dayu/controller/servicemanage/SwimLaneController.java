package com.xiaomi.dayu.controller.servicemanage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.google.gson.annotations.JsonAdapter;
import com.xiaomi.dayu.common.ParameterCheckerUtil;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.model.dto.ProjectEnvsDTO;
import com.xiaomi.dayu.model.dto.SwimLaneDTO;
import com.xiaomi.dayu.model.dto.SwimLaneGroupDTO;
import com.xiaomi.dayu.model.dto.SwimLaneTagsDTO;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.dayu.service.ConfigParams;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.SwimLaneService;
import com.xiaomi.dayu.service.impl.LoginService;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import swimlane.bo.ConditionPairBo;
import swimlane.bo.ConditionPairCheckBo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;


/**
 * 泳道/组管理
 */
@RestController
@RequestMapping("/api/swimlane")
@Slf4j
public class SwimLaneController {

    private final ProviderService providerService;

    @Autowired
    private SwimLaneService swimLaneService;

    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;

    @Autowired
    public SwimLaneController(ProviderService providerService) {
        this.providerService = providerService;
    }

    @Autowired
    private LoginService loginService;

    @RequestMapping(value = "/createSwimLaneGroup", method = RequestMethod.POST)
    public ResultResponse<Boolean> createSwimLaneGroup(HttpServletRequest request,
                                                       HttpServletResponse response,
                                                       @RequestBody SwimLaneGroupDTO swimLaneGroupDTO) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.createSwimLaneGroup] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        swimLaneGroupDTO.setCreator(UserInfoThreadLocal.getUserInfo().getUserName());
        swimLaneGroupDTO.getAppList().forEach(appName -> {
            ProjectBo project = iProjectServiceRpc.getProjectByName(appName);
            if (Objects.isNull(project) || Objects.isNull(project.getId())) {
                throw new ParamValidationException("app dose not exist:" + appName);
            }
        });
        swimLaneService.createSwimLaneGroup(swimLaneGroupDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/updateSwimLaneGroup", method = RequestMethod.POST)
    public ResultResponse<Boolean> updateSwimLaneGroup(HttpServletRequest request,
                                                       HttpServletResponse response,
                                                       @RequestBody SwimLaneGroupDTO swimLaneGroupDTO) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.updateSwimLaneGroup] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/

        if (Objects.isNull(swimLaneGroupDTO.getId())) {
            throw new ParamValidationException("swim lane id is needed");
        }
        swimLaneService.updateSwimLaneGroup(swimLaneGroupDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/deleteSwimLaneGroup", method = RequestMethod.POST)
    public ResultResponse<Boolean> deleteSwimLaneGroup(HttpServletRequest request,
                                                       HttpServletResponse response,
                                                       Integer id) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.updateSwimLaneGroup] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        if (Objects.isNull(id)) {
            throw new ParamValidationException("Unknown ID!");
        }
        swimLaneService.deleteSwimLaneGroup(id);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/listSwimLaneGroup", method = RequestMethod.GET)
    public ResultResponse<List<SwimLaneGroupDTO>> listSwimLaneGroup(HttpServletRequest request,
                                                                    HttpServletResponse response) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.listSwimLaneGroup] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        return ResultResponse.success(swimLaneService.listSwimLaneGroup());
    }

    @RequestMapping(value = "/getSwimLaneGroup", method = RequestMethod.POST)
    public ResultResponse<SwimLaneGroupDTO> getSwimLaneGroup(HttpServletRequest request,
                                                             HttpServletResponse response, Integer groupId) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.getSwimLaneGroup] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        return ResultResponse.success(swimLaneService.getSwimLaneGroup(groupId));
    }

    @RequestMapping(value = "/loadProjectNameList", method = RequestMethod.GET)
    public ResultResponse<Set<String>> loadProjectNameList(HttpServletRequest request,
                                                           HttpServletResponse response) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.loadProjectList] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        return ResultResponse.success(swimLaneService.loadProjectList());
    }

    @RequestMapping(value = "/listProjectEnv", method = RequestMethod.POST)
    public ResultResponse<List<ProjectEnvsDTO>> listProjectEnv(HttpServletRequest request,
                                                               HttpServletResponse response, Integer groupId) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.listProjectEnv] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        return ResultResponse.success(swimLaneService.listProjectEnv(groupId, ConfigParams.getMilineEnv().getName()));
    }

    @RequestMapping(value = "/createSwimLane", method = RequestMethod.POST)
    public ResultResponse<Boolean> createSwimLane(HttpServletRequest request,
                                                  HttpServletResponse response,
                                                  @RequestBody SwimLaneDTO swimLaneDTO) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.createSwimLane] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/

        swimLaneDTO.setCreator(UserInfoThreadLocal.getUserInfo().getUserName());
        try {
            swimLaneService.createSwimLane(swimLaneDTO);
        } catch (Exception e) {
            log.error("SwimLane Controller createSwimLane failed,cause by:", e);
            return ResultResponse.fail(500, "创建泳道失败，请检查相关配置是否正确");
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/updateSwimLane", method = RequestMethod.POST)
    public ResultResponse<Boolean> updateSwimLane(HttpServletRequest request,
                                                  HttpServletResponse response,
                                                  @RequestBody SwimLaneDTO swimLaneDTO) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.updateSwimLane] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        try {
            swimLaneService.updateSwimLane(swimLaneDTO);
        } catch (Exception e) {
            log.error("SwimLane Controller updateSwimLane failed,cause by:", e);
            return ResultResponse.fail(500, "更新泳道失败，请检查相关配置是否正确");
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/deleteSwimLane", method = RequestMethod.POST)
    public ResultResponse<Boolean> deleteSwimLane(HttpServletRequest request,
                                                  HttpServletResponse response,
                                                  Integer swimLandId) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.deleteSwimLane] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        if (Objects.isNull(swimLandId)) {
            throw new ParamValidationException("Unknown ID!");
        }
        swimLaneService.deleteSwimLane(swimLandId);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/listSwimLane", method = RequestMethod.POST)
    public ResultResponse<List<SwimLaneDTO>> listSwimLane(HttpServletRequest request,
                                                          HttpServletResponse response,
                                                          Integer swimLaneGroupId
    ) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.listSwimLane] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        if (Objects.isNull(swimLaneGroupId)) {
            throw new ParamValidationException("Unknown ID!");
        }
        return ResultResponse.success(swimLaneService.listSwimLane(swimLaneGroupId));
    }

    @RequestMapping(value = "/enableSwimLane", method = RequestMethod.POST)
    public ResultResponse<Boolean> enableSwimLane(HttpServletRequest request,
                                                  HttpServletResponse response,
                                                  Integer swimLandId) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.enableSwimLane] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        if (Objects.isNull(swimLandId)){
            throw new ParamValidationException("Unknown ID!");
        }
        try {
            swimLaneService.enableSwimLane(swimLandId, UserInfoThreadLocal.getUserInfo().getUserName());
        } catch (Exception e) {
            log.error("enableSwimLane,swimLandId={}",swimLandId,e);
            if (e instanceof ParamValidationException) {
                return ResultResponse.fail(500, e.getMessage());
            }
            return ResultResponse.fail(500, "请确认相关应用标签路由功能都已开启");
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/disableSwimLane", method = RequestMethod.POST)
    public ResultResponse<Boolean> disableSwimLane(HttpServletRequest request,
                                                   HttpServletResponse response,
                                                   Integer swimLandId) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.disableSwimLane] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        if (Objects.isNull(swimLandId)) {
            throw new ParamValidationException("Unknown ID!");
        }
        try {
            swimLaneService.disableSwimLane(swimLandId);
        } catch (Exception e) {
            log.error("disableSwimLane,swimLandId={}",swimLandId,e);
            if (e instanceof ParamValidationException) {
                return ResultResponse.fail(500, e.getMessage());
            }
            return ResultResponse.fail(500, "请确认相关应用标签路由功能都已开启");
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/getSwimLaneGroupIdByTag", method = RequestMethod.POST)
    public ResultResponse<Map<String, Integer>> getSwimLaneGroupIdByTag(HttpServletRequest request,
                                                                        HttpServletResponse response,
                                                                        @RequestBody SwimLaneTagsDTO tagsDTO
    ) throws IOException {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[SwimLaneController.getSwimLaneGroupIdByTag] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        if (Objects.isNull(tagsDTO.getSwimLaneTags()) || tagsDTO.getSwimLaneTags().isEmpty()) {
            return ResultResponse.success(new HashMap<>());
        }
        return ResultResponse.success(swimLaneService.getSwimLaneGroupIdByTag(tagsDTO.getSwimLaneTags()));
    }
    @RequestMapping(value = "/checkParameterConfig", method = RequestMethod.POST)
    public ResultResponse<Boolean> checkParameterConfig(HttpServletRequest request,
                                                                        HttpServletResponse response,
                                                                        @RequestBody ConditionPairCheckBo conditionPairCheckBo) {
        if(conditionPairCheckBo.getConditionPairBo() == null){
            return ResultResponse.fail(500,"条件配置不能为空");
        }
        if(conditionPairCheckBo.getJsonParam() == null){
            return ResultResponse.fail(500,"参数内容不能为空");
        }
        if(conditionPairCheckBo.getConditionPairBo().getParamType() == 1){
            return ResultResponse.success(ParameterCheckerUtil.judgeParamSatisfy(conditionPairCheckBo));
        }else {
            return ResultResponse.fail(500,"只支持参数类型的参数校验");
        }
    }




}

