
package com.xiaomi.dayu.controller.nacos;


import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.service.InstanceService;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 */

@SentinelResource
@RestController
@RequestMapping("/api/nacos/v1/ns/instance")
public class InstanceController {
    @Resource
    private InstanceService instanceService;
    @RequestMapping(value = "/onlineOrOffline", method = {RequestMethod.PUT,RequestMethod.POST})
    public ResultResponse<Object> onlineOrOffline(HttpServletRequest request,
                                         @RequestParam @ApiParam(value = "providers:{service}:{version}:{group}") String serviceName,
                                         @RequestParam(required = false) String namespaceId,
                                         @RequestParam(required = false) String clusterName,
                                         @RequestParam @ApiParam(value = "集合json格式字符串") String ips,
                                         @RequestParam String enabled) throws InterruptedException {
        boolean b = instanceService.onlineOrOfflineByServiceName(serviceName, namespaceId, clusterName, ips, enabled, UserInfoThreadLocal.getUserInfo().getUserName(), Enums.ChannelType.HTTP);
        return b ?  ResultResponse.success(true) : ResultResponse.fail(500,"处理失败");
    }



}
