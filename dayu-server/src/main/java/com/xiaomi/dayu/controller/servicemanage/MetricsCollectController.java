package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.model.dto.RelationDTO;
import com.xiaomi.dayu.service.MetricsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/metrics")
public class MetricsCollectController {
    @Autowired
    private MetricsService metricsService;


/*    @RequestMapping(method = RequestMethod.POST)
    public String metricsCollect(@RequestParam String group, @PathVariable String env) {
        MetrcisCollectServiceImpl service = new MetrcisCollectServiceImpl();
        service.setUrl("dubbo://127.0.0.1:20880?scope=remote&cache=true");

        return service.invoke(group).toString();
    }*/

    @RequestMapping(value = "/relation", method = RequestMethod.GET)
    public RelationDTO getApplicationRelation(){
        return metricsService.getApplicationRelation();
    }

    /*private String getOnePortMessage(String group, String ip, String port, String protocol) {
        MetrcisCollectServiceImpl metrcisCollectService = new MetrcisCollectServiceImpl();
        metrcisCollectService.setUrl(protocol + "://" + ip + ":" + port +"?scope=remote&cache=true");
        String res = metrcisCollectService.invoke(group).toString();
        return res;
    }

    @RequestMapping( value = "/ipAddr", method = RequestMethod.GET)
    public List<MetricDTO> searchService(@RequestParam String ip, @RequestParam String group, @PathVariable String env) {

        Map<String, String> configMap = new HashMap<>();
        addMetricsConfigToMap(configMap, ip);

//         default value
        if (configMap.size() <= 0) {
            configMap.put("20880", "dubbo");
        }
        List<MetricDTO> metricDTOS = new ArrayList<>();
        for (String port : configMap.keySet()) {
            String protocol = configMap.get(port);
            String res = getOnePortMessage(group, ip, port, protocol);
            metricDTOS.addAll(new Gson().fromJson(res, new TypeToken<List<MetricDTO>>(){}.getType()));
        }

        return metricDTOS;
    }

    protected void addMetricsConfigToMap(Map<String, String> configMap, String ip) {
        List<Provider> providers = providerService.findByAddress(ip);
        if (providers.size() > 0) {
            Provider provider = providers.get(0);
            String service = provider.getService();
            MetadataIdentifier providerIdentifier = new MetadataIdentifier(Tool.getInterface(service), Tool.getVersion(service), Tool.getGroup(service),
                    Constants.PROVIDER_SIDE, provider.getApplication());
            String metaData = providerService.getProviderMetaData(providerIdentifier);
            FullServiceDefinition providerServiceDefinition = new Gson().fromJson(metaData, FullServiceDefinition.class);
            Map<String, String> parameters = providerServiceDefinition.getParameters();
            configMap.put(parameters.get(Constants.METRICS_PORT), parameters.get(Constants.METRICS_PROTOCOL));
        } else {
            List<Consumer> consumers = consumerService.findByAddress(ip);
            if (consumers.size() > 0) {
                Consumer consumer = consumers.get(0);
                String service = consumer.getService();
                MetadataIdentifier consumerIdentifier = new MetadataIdentifier(Tool.getInterface(service), Tool.getVersion(service), Tool.getGroup(service),
                        Constants.CONSUMER_SIDE, consumer.getApplication());
                String metaData = consumerService.getConsumerMetadata(consumerIdentifier);
                Map<String, String> consumerParameters = new Gson().fromJson(metaData, Map.class);
                configMap.put(consumerParameters.get(Constants.METRICS_PORT), consumerParameters.get(Constants.METRICS_PROTOCOL));
            }
        }
    }*/
}