/*

import io.swagger.annotations.Api;

import org.apache.log4j.Logger;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api("")
@RestController
@RequestMapping("/api/iauth/api/auth")
public class AuthListController {
    private static final Logger LOGGER = Logger.getLogger(AuthListController.class);

    @GetMapping("/")
    public ResultResponse<String> getDisplayList(HttpServletRequest request,
                                                 @RequestParam("source") int source,
                                                 @RequestParam(value = "platform",required = false)  String platform)  {
        return HttpClient.iauthProxyHttp(request);
    }

    @GetMapping("/query")
    public ResultResponse<String> getQueryList(HttpServletRequest request,
                                               @RequestParam("serviceId") String serviceId,
                                               @RequestParam("appId") long appId,
                                               @RequestParam("scopeId") int scopeId,
                                               @RequestParam(value = "platform",required = false) String platform)  {
        return HttpClient.iauthProxyHttp(request);
    }


    */
/**
     * 用于我的Service，查看 单个sid下的授权信息
     *
     * @return
     * @
     *//*

    @GetMapping("/service/{serviceId}")
    public ResultResponse<String> getListByServiceId(HttpServletRequest request,
                                                     @PathVariable("serviceId") String sid,
                                                     @RequestParam(value = "platform",required = false) String platform)  {
        return HttpClient.iauthProxyHttp(request);
    }
}
*/
