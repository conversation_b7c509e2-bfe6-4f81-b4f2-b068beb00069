package com.xiaomi.dayu.controller.servicemanage;

import com.google.gson.Gson;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.ConvertUtil;
import com.xiaomi.dayu.common.util.ServiceTestUtil;
import com.xiaomi.dayu.model.domain.MethodMetadata;
import com.xiaomi.dayu.model.dto.ServiceTestDTO;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.impl.GenericServiceImpl;
import com.xiaomi.dayu.service.impl.ValidationPermissionService;
import org.apache.dubbo.metadata.definition.model.FullServiceDefinition;
import org.apache.dubbo.metadata.definition.model.MethodDefinition;
import org.apache.dubbo.metadata.report.identifier.MetadataIdentifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 服务测试
 */
@RestController
@RequestMapping("/api/test")
public class ServiceTestController {
    private final GenericServiceImpl genericService;
    private final ProviderService providerService;
    @Autowired
    private ValidationPermissionService validationPermissionService;
    public ServiceTestController(GenericServiceImpl genericService, ProviderService providerService) {
        this.genericService = genericService;
        this.providerService = providerService;
    }

    @RequestMapping(method = RequestMethod.POST)
    public Object test( @RequestBody ServiceTestDTO serviceTestDTO) {
        try {
            String profile = System.getProperty("spring.profiles.active");
            if("pro".equals(profile)){
                throw new ServiceException("线上环境不支持测试功能");
            }
            validationPermissionService.checkByServiceId(UserInfoThreadLocal.getUserInfo(),serviceTestDTO.getService());
            return genericService.invoke(serviceTestDTO.getService(), serviceTestDTO.getMethod(), serviceTestDTO.getParameterTypes(), serviceTestDTO.getParams());
        } catch (Exception e) {
            throw e;
        }
    }

    @RequestMapping(value = "/method", method = RequestMethod.GET)
    public MethodMetadata methodDetail( @RequestParam String application, @RequestParam String service,
                                       @RequestParam String method) {
        Map<String, String> info = ConvertUtil.serviceName2Map(service);
        MetadataIdentifier identifier = new MetadataIdentifier(info.get(Constants.INTERFACE_KEY),
                info.get(Constants.VERSION_KEY),
                info.get(Constants.GROUP_KEY), Constants.PROVIDER_SIDE, application);
        String metadata = providerService.getProviderMetaData(identifier);
        MethodMetadata methodMetadata = null;
        if (metadata != null) {
            Gson gson = new Gson();
            FullServiceDefinition serviceDefinition = gson.fromJson(metadata, FullServiceDefinition.class);
            List<MethodDefinition> methods = serviceDefinition.getMethods();
            if (methods != null) {
                for (MethodDefinition m : methods) {
                    if (ServiceTestUtil.sameMethod(m, method)) {
                        methodMetadata = ServiceTestUtil.generateMethodMeta(serviceDefinition, m);
                        break;
                    }
                }
            }
        }
        return methodMetadata;
    }
}
