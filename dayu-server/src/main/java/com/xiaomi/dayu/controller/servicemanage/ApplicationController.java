package com.xiaomi.dayu.controller.servicemanage;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.PermissionDeniedException;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.service.ConfigParams;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.model.dto.ApplicationInfoDTO;
import com.xiaomi.dayu.model.dto.RadarSystems;
import com.xiaomi.dayu.model.dump.PipelineDeployVO;
import com.xiaomi.dayu.rpc.*;
import com.xiaomi.dayu.service.InstanceService;
import com.xiaomi.dayu.service.NamingInstanceService;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.UserService;
import com.xiaomi.mione.tesla.k8s.bo.IPZone;
import com.xiaomi.mone.application.api.bo.ProjectVo;
import com.xiaomi.mone.miline.api.bo.common.DeployTypeEnum;
import com.xiaomi.mone.tpc.common.vo.ProjectVoV2;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.gwdash.bo.ProjectRoleBo;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.quota.bo.ResourceBo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
@Slf4j
@Api(description = "应用")
@RestController
@RequestMapping("/api/application")
public class ApplicationController {
    @Autowired
    private AccountServiceRpc accountServiceRpc;

    @Autowired
    private GwdashApiServiceRpc gwdashApiServiceRpc;

    @Autowired
    private UserService userService;

    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;
    @Autowired
    private ProjectFacadeRpc projectFacadeRpc;
    @Autowired
    private OpenApiServiceRpc openApiServiceRpc;
    @Autowired
    private ResourceServiceRpc  resourceServiceRpc;

    @Resource
    private NamingInstanceService namingInstanceService;
    @Resource
    private InstanceService instanceService;
    @Resource
    private ProviderService providerService;
    @Resource
    private K8sProxyServiceRpc k8sProxyServiceRpc;
    @Resource
    private ZDubboServiceRpc zDubboServiceRpc;

    @Value("${monitor.url.pattern}")
    private String monitorUrlPattern;

    @Value("${hera.url}")
    private String heraUrl;

    private String env = "";//staging online

    private static Cache<String, ApplicationInfoDTO> localCache = CacheBuilder.newBuilder().expireAfterWrite(600, TimeUnit.SECONDS)
            .maximumSize(3000).build();

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResultResponse<PageResult<ApplicationInfoDTO>> queryApplicationList(
                @RequestParam(required = false) String appName,
                @RequestParam(required = false,defaultValue = "10") int pageSize,
                 @RequestParam(required = false,defaultValue = "1") int pageNum){

        PageResult<ApplicationInfoDTO> pageResult;
        if(UserInfoThreadLocal.getUserInfo().isAdmin()){
             pageResult = gwdashApiServiceRpc.queryApps( null,  appName,  true,  pageNum,  pageSize);
        }else{
             pageResult = gwdashApiServiceRpc.queryApps( UserInfoThreadLocal.getUserInfo().getUserName(),  appName,  false,  pageNum,  pageSize);
        }

        if(pageResult != null ){
            List<ApplicationInfoDTO> applicationList = pageResult.getContent();
            if(CollectionUtils.isNotEmpty(applicationList)){
                applicationList.forEach(applicationInfoDTO->{
                    try {
                        ApplicationInfoDTO cache = localCache.get(applicationInfoDTO.getName(), () -> {
                            applicationInfoDTO.setTraceUrl(heraUrl + applicationInfoDTO.getId() + "-" + applicationInfoDTO.getName());
                            applicationInfoDTO.setMonitorUrl(monitorUrlPattern.replace("projectId", applicationInfoDTO.getId() + "_" + applicationInfoDTO.getName().replace("-", "_")));

                            Map<String, List<String>> mapResult = openApiServiceRpc.qrySimplePipelineByProjectIds(applicationInfoDTO.getId(),ConfigParams.getMilineEnv().getName());
                            if (MapUtils.isNotEmpty(mapResult)) {
//                                List<Integer> collect = mapResult.values().stream().filter(list -> CollectionUtils.isNotEmpty(list)).map(list -> list.size()).collect(Collectors.toList());
                                applicationInfoDTO.setDeployGroupNum(mapResult.size());
//                                applicationInfoDTO.setServiceNum(CollectionUtils.isEmpty(collect) ? 0 : collect.stream().reduce((num1, num2) -> num1 + num2).get());
                            }
                            return applicationInfoDTO;
                        });
                        applicationInfoDTO.setTraceUrl(cache.getTraceUrl());
                        applicationInfoDTO.setMonitorUrl(cache.getMonitorUrl());
                        applicationInfoDTO.setDeployGroupNum(cache.getDeployGroupNum());
                        applicationInfoDTO.setServiceNum(cache.getServiceNum());
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    }
                });
                return ResultResponse.success(pageResult);
            }
        }
        return ResultResponse.success(new PageResult<>(null, 0,pageSize,pageNum));
    }
    @RequestMapping(value = "/simple-info", method = RequestMethod.GET)
    public ResultResponse<ApplicationInfoDTO> simpleInfo(@RequestParam String applicationName){
        ApplicationInfoDTO applicationInfoDTO = new ApplicationInfoDTO();
        List<UserInfoResult> userList = accountServiceRpc.queryUsersByAppName(applicationName);
        if(CollectionUtils.isNotEmpty(userList)){
            applicationInfoDTO.setDevelopers(userList.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
            String uid = userService.findUidByUserName(userList.get(0).getUserName());
            applicationInfoDTO.setDepartment(userService.findDepartmentFullName(uid));
        }else{
            return ResultResponse.fail(1001,"应用名"+applicationName+"不存在，\n原因：服务注册application和mone应用名不匹配！");
        }
        return ResultResponse.success(applicationInfoDTO);
    }
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResultResponse<ApplicationInfoDTO> detail(@RequestParam String applicationName,@RequestParam Long projectId){

        ApplicationInfoDTO applicationInfoDTO = new ApplicationInfoDTO();
        ProjectVoV2 projectVoV2 = projectFacadeRpc.getProject(applicationName,projectId);
        if(projectVoV2 != null && projectVoV2.getProjectVo() != null){
            ProjectVo projectVo = projectVoV2.getProjectVo();
            applicationInfoDTO.setId(projectVo.getId());
            applicationInfoDTO.setName(projectVo.getName());
            if(projectVo.getProjectGen()!= null){
                String type = projectVo.getProjectGen().getType();
                if("fe".equals(type)){
                    applicationInfoDTO.setType("前端");
                }else{
                    applicationInfoDTO.setType(type);
                }
            }
            applicationInfoDTO.setDescription(projectVo.getDesc());
            applicationInfoDTO.setCreationDate(new Date(projectVo.getCtime()));
            applicationInfoDTO.setModifyDate(new Date(projectVo.getUtime()));
            applicationInfoDTO.setGitAddress(projectVo.getGitAddress());
            if(projectVoV2.getNodeVo() != null && projectVoV2.getNodeVo().getOrgInfoVo() != null){
                applicationInfoDTO.setDepartment(projectVoV2.getNodeVo().getOrgInfoVo().getNamePath());
            }
        }
        List<UserInfoResult> userList = accountServiceRpc.queryUsersByAppName(applicationName);
        if(CollectionUtils.isNotEmpty(userList)){
            applicationInfoDTO.setDevelopers(userList.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
            if(StringUtils.isBlank(applicationInfoDTO.getDepartment())){
                String uid = userService.findUidByUserName(userList.get(0).getUserName());
                applicationInfoDTO.setDepartment(userService.findDepartmentFullName(uid));
            }
        }
        return ResultResponse.success(applicationInfoDTO);
    }

    private List<ApplicationInfoDTO> convert(List<RadarSystems.AppInfo> appInfoList) {
        List<ApplicationInfoDTO> lists = new ArrayList<>();

        for (RadarSystems.AppInfo appInfo : appInfoList) {
            ApplicationInfoDTO dto = new ApplicationInfoDTO();
            dto.setName(appInfo.getName());
            dto.setId(Long.parseLong(appInfo.getId()+""));

            String members = "";
            if (appInfo.getMembers() != null) {
                for (RadarSystems.Member member : appInfo.getMembers()) {
                    members += member.getUser_name() + ",";
                }
            }
            dto.setDevelopers(members);
            dto.setDescription(appInfo.getSystem_description());

            dto.setTraceUrl(heraUrl+appInfo.getId()+"-"+appInfo.getName());
            dto.setMonitorUrl(monitorUrlPattern.replace("projectId",appInfo.getId()+"").replace("projectName", appInfo.getName()));
            lists.add(dto);
        }
        return lists;
    }

    /*private ApplicationInfoDTO detailMit(String appName) {
        ApplicationInfoDTO applicationInfoDTO = new ApplicationInfoDTO();
        applicationInfoDTO.setType("信息技术部应用");
        applicationInfoDTO.setDescription("信息技术部应用");
        applicationInfoDTO.setAccessType("普通应用");
        applicationInfoDTO.setFramework("Sprint Boot");

        applicationInfoDTO.setDepartment(UserInfoThreadLocal.getUserInfo().getDeptName());

        RadarSystems.PageData pageData = accountServiceRpc.queryAppByName(UserInfoThreadLocal.getUserInfo().getUserName(), appName);
        if (pageData.getList().size() > 0) {
            RadarSystems.AppInfo appInfo = pageData.getList().get(0);
            applicationInfoDTO.setId(Long.parseLong(appInfo.getId()+""));
            applicationInfoDTO.setName(appInfo.getName());
            if (appInfo.getMembers() != null) {
                applicationInfoDTO.setDevelopers(appInfo.getMembers().stream().map(e -> e.getUser_name()).collect(Collectors.joining(",")));
            }

        }
        return applicationInfoDTO;
    }*/

    @RequestMapping(value = "/users", method = RequestMethod.GET)
    public Result<Object> searchAppUsers(HttpServletRequest request,
                                         @RequestParam(required = true) String appName) {
        List<UserInfoResult> data = this.accountServiceRpc.queryUsersByAppName(appName);
        return Result.success(data);
    }

    @RequestMapping(value = "/machineRoom/list", method = RequestMethod.GET)
    public Result<Object> machineRoomList(HttpServletRequest request,
                                         @RequestParam(required = true) String appName,
                                         @RequestParam(required = true) int projectId,
                                          @RequestParam(required = false) String milineEnv) {

        Map<String, String> ipAndMachineRoomMap = new HashMap<>();
        Map<Integer, List<PipelineDeployVO>> listMap = openApiServiceRpc.qryProjectContainerInfo(projectId, Arrays.stream(DeployTypeEnum.values()).map(DeployTypeEnum::getId).collect(Collectors.toList()),
                StringUtils.isNotBlank(milineEnv) ? milineEnv:ConfigParams.getMilineEnv().getName());
        Map<Integer, ArrayList<String>> deployTypeToIpList = new HashMap<>();
        listMap.forEach((deployType,list)->{
                list.forEach(vo->{
                    String ip = vo.getIp();
                    deployTypeToIpList.computeIfAbsent(deployType, k->new ArrayList<>()).add(ip);
                    ipAndMachineRoomMap.put(ip,"未知");
                });
            });
        if(CollectionUtils.isNotEmpty(deployTypeToIpList.get(DeployTypeEnum.K8S.getId()))){
            List<IPZone> ipZoneList = k8sProxyServiceRpc.getIPZone(deployTypeToIpList.get(DeployTypeEnum.K8S.getId()));
            if(CollectionUtils.isNotEmpty(ipZoneList)){
                ipZoneList.forEach(ipZone ->
                    ipAndMachineRoomMap.put(ipZone.getIP(),ipZone.getZone())
                );
            }
        }
        if(!ConfigParams.ENV_NAME.contains("sgp")){
            List<ResourceBo> list = this.resourceServiceRpc.getResourceByProjectId(projectId);
            if(CollectionUtils.isNotEmpty(list)){
                for (ResourceBo resourcebo : list) {
                    String ip = resourcebo.getIp();
                    Map<String, String> lables = resourcebo.getLables();
                    String cluster = lables.get("cluster");
                    if(StringUtils.isBlank(cluster)){
                        cluster = "未知";
                    }
                    ipAndMachineRoomMap.put(ip,cluster);
                }
            }
        }

        Map<String, List<String>> result = new HashMap<>();
        ipAndMachineRoomMap.forEach((ip,room)->{
            if(!result.containsKey(room)){
                result.put(room,new ArrayList<>());
            }
            result.get(room).add(ip);
        });
        return Result.success(result);
    }
    @RequestMapping(value = "/machineRoom/offline", method = RequestMethod.PUT)
    public Result<List<String>> offline(HttpServletRequest request,
                                          @RequestParam String appName,
                                          @RequestParam Integer projectId,
                                          @RequestParam Boolean enabled,
                                            @RequestBody List<String> ips) {
        //DEFAULT_GROUP@@providers:com.xiaomi.dayu.api.service.NacosConfigService:staging

        CheckPermissUtil.checkPermissByAppName(appName);

        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        if(!userInfo.isAdmin() && userInfo.getApplicationNames().contains(appName)){
            boolean match = false;
            List<ProjectRoleBo> members = gwdashApiServiceRpc.getMembersByProjectId(projectId);
            if(CollectionUtils.isNotEmpty(members)){
                match = members.stream().anyMatch(projectRoleBo -> projectRoleBo.getRoleType() == 0 && projectRoleBo.getUserName().equals(userInfo.getUserName()));
            }
            if(!match){
                throw new PermissionDeniedException("只有该项目的own才能操作！");
            }
        }
        log.error("用户{}，针对项目名{}，机房IPS{},进行{}操作",userInfo.getUserName(),appName,JSON.toJSONString(ips),(enabled ? "上线":"下线"));
        List<String> list = instanceService.onlineOrOfflineOneByOne(appName, projectId, enabled, ips, userInfo.getUserName(), Enums.ChannelType.HTTP);
        return Result.success(list);
    }
    @RequestMapping(value = "/diffAppName", method = RequestMethod.GET)
    public ResultResponse<HashMap<String, List>> diffAppName(){
        Set<String> providerAppNameList = namingInstanceService.queryAllAppName(Constants.PROVIDER_SIDE);
        log.warn("provider 端有 appName数量 {}",providerAppNameList.size());
        Set<String> consumerAppNameList = namingInstanceService.queryAllAppName(Constants.CONSUMER_SIDE);
        log.warn("consumer 端有 appName数量 {}",consumerAppNameList.size());
        Set<String> appNameSet = new HashSet<>(providerAppNameList.size()*2);
        appNameSet.addAll(providerAppNameList);
        appNameSet.addAll(consumerAppNameList);
        log.warn("dubbo 总共有 appName数量 {}",appNameSet.size());


        ArrayList<String> list = Lists.newArrayList(appNameSet);

        ArrayList<String> existsList = new ArrayList<>();
        ArrayList<String> notExistsList = new ArrayList<>();
        List<List<String>> partitions = Lists.partition(list, 100);

        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(partitions.size(), partitions.size(),
                6, TimeUnit.MINUTES,new LinkedBlockingQueue<>());

        CompletableFuture[] array = new CompletableFuture[partitions.size()];
        for (int i = 0; i < partitions.size(); i++) {
            int finalI = i;
            array[i] = CompletableFuture.runAsync(() -> {
                ArrayList<String> existsList1 = new ArrayList<>();
                ArrayList<String> notExistsList2 = new ArrayList<>();
                for (String appName : partitions.get(finalI)){
                    ProjectBo projectBo = iProjectServiceRpc.getProjectByName(appName);
                    if (projectBo.getName() != null) {
                        existsList1.add(appName);
                    } else {
                        notExistsList2.add(appName);
                    }
                }
                existsList.addAll(existsList1);
                notExistsList.addAll(notExistsList2);
            }, threadPoolExecutor);
        }
        try {
            CompletableFuture.allOf(array).get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        log.warn("existsList = {}",existsList.size());
        log.warn("notExistsList = {}",notExistsList.size());

        HashMap<String, List> map = new HashMap<>();
        map.put("existsList",existsList);
        map.put("notExistsList",notExistsList);
        log.warn("diffAppName = {}",JSON.toJSONString(map));
        return ResultResponse.success(map);
    }
    @RequestMapping(value = "/prompt", method = RequestMethod.GET)
    public ResultResponse<List<String>> prompt(@RequestParam String prompt ){

        return ResultResponse.success(zDubboServiceRpc.prompt(prompt));

    }
}
