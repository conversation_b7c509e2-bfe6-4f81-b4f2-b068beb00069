package com.xiaomi.dayu.controller.servicemanage;

import com.google.common.collect.Lists;
import com.xiaomi.dayu.api.bo.GetTagRuleListReq;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.ExceptionEnum;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.bo.SessionAccount;
import com.xiaomi.dayu.model.dto.AppTagRouteDTO;
import com.xiaomi.dayu.model.dto.EnableTagRouteDTO;
import com.xiaomi.dayu.model.dto.TagInstanceDTO;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.RouteService;
import com.xiaomi.dayu.service.impl.LoginService;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import com.xiaomi.mone.http.docs.annotations.HttpApiModule;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import swimlane.bo.TagRuleInfoDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * 标签路由
 */
@RestController
@RequestMapping("/api/rules/route/tag")
@Slf4j
@HttpApiModule(value = "标签路由模块",apiController = TagRoutesController.class)
public class TagRoutesController {

    private final RouteService routeService;
    private final ProviderService providerService;

    @Autowired
    public TagRoutesController(RouteService routeService, ProviderService providerService) {
        this.routeService = routeService;
        this.providerService = providerService;
    }

    @Autowired
    private LoginService loginService;

    @RequestMapping(value = "/enableTagRoute", method = RequestMethod.POST)
    public ResultResponse<Boolean> enableTagRoute(EnableTagRouteDTO dto) {
        String app = dto.getApplication();
        if (StringUtils.isEmpty(app)) {
            throw new ParamValidationException("app is Empty!");
        }
//        if (!providerService.findVersionInApplication(app).equals("2.7.12-mone-SNAPSHOT")) {
//            throw new VersionValidationException("please update dubbo version to 2.7.12-mone-SNAPSHOT to support tag route");
//        }
        if (routeService.findTagRoute(dto.getApplication()) == null) {
            throw new ResourceNotFoundException("can not find tag route, Id: " + dto.getApplication());
        }
        if (dto.isEnable()) {
            routeService.enableTagRoute(app);
        } else {
            routeService.disableTagRoute(app);
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/createTagForInstance", method = RequestMethod.POST)
    public ResultResponse<Boolean> createTagForInstance(HttpServletRequest request,
                                                        HttpServletResponse response,
                                                        TagInstanceDTO tagInstanceDTO) throws IOException {

        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[ScheduleController.create] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/

        String app = tagInstanceDTO.getApplication();

        if (StringUtils.isEmpty(app)) {
            throw new ParamValidationException("app is Empty!");
        }
        if (StringUtils.isEmpty(tagInstanceDTO.getTagName())){
            return ResultResponse.fail(ExceptionEnum.NO_EXISTS_SERVICE);
        }
//        if (!providerService.findVersionInApplication(app).equals("2.7.12-mone-SNAPSHOT")) {
//            throw new VersionValidationException("please update dubbo version to 2.7.12-mone-SNAPSHOT to support tag route");
//        }
        tagInstanceDTO.setAddresses(Lists.newArrayList(tagInstanceDTO.getAddress()));
        routeService.createTagForInstance(tagInstanceDTO, UserInfoThreadLocal.getUserInfo().getUserName(),Constants.TAG_CREATE_TYPE_DIY);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/removeTagForInstance", method = RequestMethod.POST)
    public ResultResponse<Boolean> removeTagForInstance(HttpServletRequest request,
                                                        HttpServletResponse response,
                                                        TagInstanceDTO tagInstanceDTO) throws IOException {

        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[ScheduleController.create] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }*/
        String app = tagInstanceDTO.getApplication();

        if (StringUtils.isEmpty(app)) {
            throw new ParamValidationException("app is Empty!");
        }
        //CheckPermissUtil.checkPermissByAppName(app);

        tagInstanceDTO.setAddresses(Lists.newArrayList(tagInstanceDTO.getAddress()));
        routeService.removeTagForInstance(tagInstanceDTO,false);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/searchInstancesByApp", method = RequestMethod.GET)
    public ResultResponse<AppTagRouteDTO> searchInstancesByApp(@RequestParam String application) {
        if (StringUtils.isBlank(application)) {
            throw new ParamValidationException("application is required.");
        }
        return ResultResponse.success(routeService.findRouteIndexByApp(application));
    }

    @RequestMapping(value = "/createTagRule", method = RequestMethod.POST)
    @HttpApiDoc(value = "/api/rules/route/tag/createTagRule",apiName = "新建规则标签",method = MiApiRequestMethod.POST,description = "新建规则标签")
    public ResultResponse<Boolean> createTagRule(HttpServletRequest request,@RequestBody TagRuleInfoDTO tagRuleInfoDTO) {

       /* SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[TagRoutesController.createTagRule] current user not have valid account info in session");
            return ResultResponse.fail(ExceptionEnum.NO_PERMISSION_APPLICATION);
        }*/
        try {
            tagRuleInfoDTO.setCreator(UserInfoThreadLocal.getUserInfo().getUserName());
            tagRuleInfoDTO.setUpdater(UserInfoThreadLocal.getUserInfo().getUserName());
            routeService.createTagRule(tagRuleInfoDTO);
        } catch (Exception e) {
            log.error("[TagRoutesController.createTagRule] failed,cause by :{}",e.getMessage());
            return ResultResponse.fail(500,"创建自定义标签失败:"+e.getMessage());
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/updateTagRule", method = RequestMethod.POST)
    @HttpApiDoc(value = "/api/rules/route/tag/updateTagRule",apiName = "更新规则标签",method = MiApiRequestMethod.POST,description = "更新规则标签")
    public ResultResponse<Boolean> updateTagRule(HttpServletRequest request,@RequestBody TagRuleInfoDTO tagRuleInfoDTO) {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[TagRoutesController.updateTagRule] current user not have valid account info in session");
            return ResultResponse.fail(ExceptionEnum.NO_PERMISSION_APPLICATION);
        }*/
        try {
            tagRuleInfoDTO.setUpdater(UserInfoThreadLocal.getUserInfo().getUserName());
            routeService.updateTagRule(tagRuleInfoDTO);
        } catch (Exception e) {
            log.error("[TagRoutesController.updateTagRule] failed,cause by :{}",e.getMessage());
            return ResultResponse.fail(500,"更新自定义标签失败:"+e.getMessage());
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/deleteTagRule", method = RequestMethod.POST)
    @HttpApiDoc(value = "/api/rules/route/tag/deleteTagRule",apiName = "删除规则标签",method = MiApiRequestMethod.POST,description = "删除规则标签")
    public ResultResponse<Boolean> deleteTagRule(HttpServletRequest request,
            @HttpApiDocClassDefine(value = "ruleId",description = "规则标签id",defaultValue = "10086")
            int ruleId) {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[TagRoutesController.deleteTagRule] current user not have valid account info in session");
            return ResultResponse.fail(ExceptionEnum.NO_PERMISSION_APPLICATION);

        }*/
        try {
            routeService.deleteTagRule(ruleId);
        } catch (Exception e) {
            log.error("[TagRoutesController.deleteTagRule] failed,cause by :{}",e.getMessage());
            return ResultResponse.fail(500,"删除自定义标签失败:"+e.getMessage());
        }
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/listTagRule", method = RequestMethod.POST)
    @HttpApiDoc(value = "/api/rules/route/tag/listTagRule",apiName = "获取规则标签列表",method = MiApiRequestMethod.POST,description = "获取规则标签")
    public ResultResponse<PageResult<TagRuleInfoDTO>> listTagRule(HttpServletRequest request,@RequestBody GetTagRuleListReq req) {
        /*SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[TagRoutesController.listTagRule] current user not have valid account info in session");
            return ResultResponse.fail(ExceptionEnum.NO_PERMISSION_APPLICATION);
        }*/
        try {
            return ResultResponse.success(routeService.listTagRule(req));
        } catch (Exception e) {
            log.error("[TagRoutesController.listTagRule] failed,cause by :{}",e.getMessage());
            return ResultResponse.fail(500,"获取自定义标签失败:"+e.getMessage());
        }
    }
}

