package com.xiaomi.dayu.controller.iauth;

import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
@Deprecated
@RestController
@RequestMapping(value = "/api/iauth/api/history")
@Slf4j
public class OperationHistoryController{

    @GetMapping(value = "")
    public ResultResponse<String> getAllAuthOperationHistory(HttpServletRequest request,
                                                             @RequestParam(value = "id", required = false) Long id,
                                                             @ApiParam(value = "0:白名单，2：app或service") @RequestParam(value = "category", required = false, defaultValue = "0") Integer category,
                                                             @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit){

        return HttpClient.iauthProxyHttp(request);
    }
}
