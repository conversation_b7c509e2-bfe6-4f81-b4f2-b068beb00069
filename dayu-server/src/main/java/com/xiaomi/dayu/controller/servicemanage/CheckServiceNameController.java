package com.xiaomi.dayu.controller.servicemanage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xiaomi.data.push.client.HttpClientV2;
import com.xiaomi.data.push.nacos.NacosNaming;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.dao.NamingInstanceMapper;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.mybatis.example.NamingInstanceExample;
import com.xiaomi.dayu.service.NacosLoginService;
import com.xiaomi.dayu.service.RegistryServerSync;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.common.URL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/servciename")
public class CheckServiceNameController {
    @Autowired
    private RegistryServerSync registryServerSync;
    @Autowired
    private NamingInstanceMapper namingInstanceMapper;

    @Autowired
    private NacosNaming namingService;
    @Autowired
    private NacosLoginService nacosLoginService;
//    private ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
//            Runtime.getRuntime().availableProcessors(),100L, TimeUnit.MINUTES,new LinkedBlockingDeque());
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public ResultResponse<HashMap<String, Object>> queryApplicationList() throws ExecutionException, InterruptedException {
        ConcurrentMap<String, ConcurrentMap<String, Map<String, URL>>> registryCache = registryServerSync.getRegistryCache();
        ConcurrentHashMap<String,HashMap<String,List<String>>> providerNoExist = new ConcurrentHashMap<>();
        ConcurrentHashMap<String,HashMap<String,List<String>>> consumerNoExist = new ConcurrentHashMap<>();

        ConcurrentMap<String, Map<String, URL>> providers = registryCache.get("providers");
        List<List<String>> partitionProvider = Lists.partition(new ArrayList(providers.keySet()), 100);
        List<CompletableFuture<Void>> taskList = new ArrayList<>();
        partitionProvider.forEach(
                list->taskList.add(CompletableFuture.runAsync(new CheckServiceNameThread("provider", providerNoExist,list,providers)))
        );
        ConcurrentMap<String, Map<String, URL>> consumers = registryCache.get("consumers");
        partitionProvider.forEach(
                list->taskList.add(CompletableFuture.runAsync(new CheckServiceNameThread("consumer", consumerNoExist,list,consumers)))
        );

        CompletableFuture.allOf(taskList.toArray(new CompletableFuture[taskList.size()])).get();

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("provider",providerNoExist);
        resultMap.put("consumer",consumerNoExist);
        return ResultResponse.success(resultMap);
    }
    @RequestMapping(value = "/check2", method = RequestMethod.GET)
    public ResultResponse<HashMap<String, Object>> queryApplicationList2() throws ExecutionException, InterruptedException {
        ConcurrentHashMap<String,HashMap<String,List<String>>> providerNoExist = new ConcurrentHashMap<>();
        ConcurrentHashMap<String,HashMap<String,List<String>>> consumerNoExist = new ConcurrentHashMap<>();

        List<String> providerList = queryServiceNameList("providers");
        List<List<String>> partitionProvider = Lists.partition(providerList, 100);
        List<CompletableFuture<Void>> taskList = new ArrayList<>();
        partitionProvider.forEach(
                list->taskList.add(CompletableFuture.runAsync(new CheckFromNacosServiceNameThread("provider", providerNoExist,list)))
        );

        List<String> consumerList = queryServiceNameList("consumers");
        List<List<String>> partitionConsumer = Lists.partition(consumerList, 100);
        partitionConsumer.forEach(
                list->taskList.add(CompletableFuture.runAsync(new CheckFromNacosServiceNameThread("consumer", consumerNoExist,list)))
        );
        CompletableFuture.allOf(taskList.toArray(new CompletableFuture[taskList.size()])).get();

        List<String> nacos_db_sql = new ArrayList<>();
        List<String> db_nacos_sql = new ArrayList<>();
        for (HashMap<String, List<String>> value : providerNoExist.values()) {
            if(MapUtils.isNotEmpty(value) ){
                List<String> sqlnacos_dbList = value.get("sqlnacos_db");
                if(CollectionUtils.isNotEmpty(sqlnacos_dbList)){
                    nacos_db_sql.addAll(sqlnacos_dbList);
                }
                List<String> sqldb_nacosList = value.get("sqldb_nacos");
                if(CollectionUtils.isNotEmpty(sqldb_nacosList)){
                    db_nacos_sql.addAll(sqldb_nacosList);
                }
            }
        }

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("provider",providerNoExist);
        resultMap.put("consumer",consumerNoExist);
        resultMap.put("sqlnacos_db",nacos_db_sql);
        resultMap.put("sqldb_nacos",db_nacos_sql);
        return ResultResponse.success(resultMap);
    }
//    @PostConstruct
    public void test(){
        try {
            ResultResponse<HashMap<String, Object>> response = queryApplicationList2();
            System.out.println(JSONObject.toJSON(response));
        } catch (ExecutionException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public  List<String> getInstanceIpList(String serviceName,AtomicInteger retryNum){
        List<String> ipList = new ArrayList<>();
        try {


            String queryInstanceUrl = "http://nacos.test.b2c.srv/nacos/v1/ns/catalog/instances?&accessToken=" + nacosLoginService.getNacosAccessToken() + "&serviceName=" + serviceName + "&clusterName=DEFAULT&groupName=DEFAULT_GROUP&pageSize=1000&pageNo=1&namespaceId=";
            String queryInstance = HttpClientV2.get(queryInstanceUrl, null);
            JSONObject jsonObject = JSONObject.parseObject(queryInstance);
            Integer count = jsonObject.getInteger("count");
            if (count != 0) {
                JSONArray list = jsonObject.getJSONArray("list");
                for (Object o : list) {
                    JSONObject object = (JSONObject) o;
                    ipList.add(object.getString("ip"));
                }
            }
            Thread.sleep(100L);
        }catch (Exception e){
            if(retryNum.incrementAndGet() == 10){
              //  log.error("查询instance异常：servcieName:{}",serviceName);
                System.err.println("查询instance异常：servcieName="+serviceName);
                return ipList;
            }
            ipList = getInstanceIpList(serviceName,retryNum);
        }
        return ipList;
    }

    private List<String> queryServiceNameList(String side){
        String nacosAccessToken = nacosLoginService.getNacosAccessToken();
        String queryServiceUrl="http://nacos.test.b2c.srv/nacos/v1/ns/catalog/services?hasIpCount=false&withInstances=false&pageNo="+1+"&pageSize="+10000+"&serviceNameParam="+side+"&groupNameParam=&accessToken="+nacosAccessToken;
        String queryService= HttpClientV2.get(queryServiceUrl,null);

        JSONObject jsonObject = JSON.parseObject(queryService);
        JSONArray jsonArray = (JSONArray)jsonObject.get("serviceList");
        ArrayList<String> list = new ArrayList<>();
        for (Object o : jsonArray) {
            JSONObject o1 = (JSONObject) o;
            String name = o1.getString("name");
            Integer ipCount = o1.getInteger("ipCount");
            list.add(name);
        }
        return list;
    }
    private Map<String,List<String>> parseServiceNameList2(String data){
        HashMap<String, List<String>> map = new HashMap<>();
        JSONArray jsonArray = JSON.parseArray(data);
        if(CollectionUtils.isNotEmpty(jsonArray)){
            for (Object o1 : jsonArray) {
                JSONObject o2 = (JSONObject) o1;
                String serviceName = o2.getString("serviceName");
                JSONObject clusterMap = (JSONObject)o2.get("clusterMap");
                JSONArray hosts = (JSONArray)clusterMap.get("hosts");
                if(CollectionUtils.isNotEmpty(hosts)){

                }
            }
        }



        JSONArray serviceJsonArray = new JSONArray();
//        for (Service service : services) {
//            ServiceView serviceView = new ServiceView();
//            serviceView.setName(NamingUtils.getServiceName(service.getName()));
//            serviceView.setGroupName(NamingUtils.getGroupName(service.getName()));
//            serviceView.setClusterCount(service.getClusterMap().size());
//            serviceView.setIpCount(service.allIPs().size());
//            serviceView.setHealthyInstanceCount(service.healthyInstanceCount());
//            serviceView.setTriggerFlag(service.triggerFlag() ? "true" : "false");
//            serviceJsonArray.add(serviceView);
//        }
//
//        result.put("serviceList", serviceJsonArray);
//        result.put("count", total);
        return null;
    }

    class CheckServiceNameThread extends Thread{
        private ConcurrentHashMap<String, HashMap<String,List<String>>> providerNoExist;
        private List<String> fullServices;
        private ConcurrentMap<String, Map<String, URL>> nacosDataMap;
        private String side;
        public CheckServiceNameThread(String side,ConcurrentHashMap<String,HashMap<String,List<String>>> providerNoExist,List<String> fullServices,ConcurrentMap<String, Map<String, URL>> nacosDataMap){
            this.providerNoExist=providerNoExist;
            this.fullServices=fullServices;
            this.nacosDataMap=nacosDataMap;
            this.side=side;
        }
        @Override
        public void run() {
            NamingInstanceExample example = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andSideEqualTo(this.side);
            criteria.andFullServiceIn(fullServices);
            criteria.andDelEqualTo(false);
            List<NamingInstance> namingInstances = namingInstanceMapper.selectByExample(example);
            Map<String, List<NamingInstance>> instanceMap;
            if(CollectionUtils.isEmpty(namingInstances)){
                instanceMap = new HashMap<>();
            }
            instanceMap = namingInstances.stream().collect(Collectors.groupingBy(NamingInstance::getFullService, Collectors.toList()));
            for (String fullService : fullServices) {
                List<NamingInstance> namingInstancesList = instanceMap.get(fullService);
                if(CollectionUtils.isEmpty(namingInstancesList)){
                    namingInstancesList = new ArrayList<>();
                }
                List<String> db_ip_list = namingInstancesList.stream().map(NamingInstance::getIp).collect(Collectors.toList());

                Map<String, URL> stringURLMap = nacosDataMap.get(fullService);
                if(MapUtils.isEmpty(stringURLMap)){
                    stringURLMap = new HashMap<>();
                }
                Collection<URL> values = stringURLMap.values();
                if(CollectionUtils.isEmpty(values)) {
                    values = new ArrayList<>();
                }
                List<String> nacos_ip_list = values.stream().map(URL::getIp).collect(Collectors.toList());
                Collection nacos_db = CollectionUtils.subtract(nacos_ip_list, db_ip_list);
                Collection db_nacos = CollectionUtils.subtract(db_ip_list, nacos_ip_list);
                if(CollectionUtils.isNotEmpty(nacos_db) || CollectionUtils.isNotEmpty(db_nacos)){
                    HashMap<String, List<String>> map = new HashMap<>();
                    providerNoExist.put(fullService,map);
                    if(CollectionUtils.isNotEmpty(nacos_db)){
                        map.put("nacos_db",(ArrayList)nacos_db);

                    }
                    if(CollectionUtils.isNotEmpty(db_nacos)){
                        map.put("db_nacos",(ArrayList)db_nacos);
                    }
                }
            }
        }

    }
    class CheckFromNacosServiceNameThread extends Thread{
        private ConcurrentHashMap<String, HashMap<String,List<String>>> providerNoExist;
        private List<String> servcies;
        private String side;
        public CheckFromNacosServiceNameThread(String side,ConcurrentHashMap<String,HashMap<String,List<String>>> providerNoExist,List<String> servcies){
            this.providerNoExist=providerNoExist;
            this.servcies=servcies;
            this.side=side;
        }
        @Override
        public void run() {

            for (String servcie : servcies) {
                //providers:com.xiaomi.rikaaa0928.lcsk8s.api.service.DubboHealthService:1.0:staging
                //DEFAULT_GROUP@@
                NamingInstanceExample example = new NamingInstanceExample();
                NamingInstanceExample.Criteria criteria = example.createCriteria();
                criteria.andSideEqualTo(this.side);
                criteria.andDelEqualTo(false);
                criteria.andServiceNameEqualTo("DEFAULT_GROUP@@"+servcie);
                List<NamingInstance> namingInstances = namingInstanceMapper.selectByExample(example);

                List<String> dbIpList = namingInstances.stream().map(NamingInstance::getIp).collect(Collectors.toList());
                AtomicInteger retryNum=new AtomicInteger(0);
                List<String> instanceIpList = getInstanceIpList(servcie,retryNum);
                Collection nacos_db = CollectionUtils.subtract(instanceIpList, dbIpList);
                Collection db_nacos = CollectionUtils.subtract(dbIpList,instanceIpList);
                if(CollectionUtils.isNotEmpty(nacos_db) || CollectionUtils.isNotEmpty(db_nacos)){
                    HashMap<String, List<String>> map = new HashMap<>();
                    providerNoExist.put(servcie,map);
                    if(CollectionUtils.isNotEmpty(nacos_db)){
                        map.put("nacos_db",(ArrayList)nacos_db);
                        String ips = "";
                        for (int i = 0; i < nacos_db.size(); i++) {
                            ips = ips +"'"+((ArrayList<?>) nacos_db).get(i).toString()+"'";
                            if(i<nacos_db.size()-1){
                                ips = ips+",";
                            }
                        }
                        String sql ="nacos_db select * from naming_instance where service_name = 'DEFAULT_GROUP@@"+servcie+"' and ip in ("+ips+");";
                        map.put("sqlnacos_db",Arrays.asList(sql));
                    }
                    if(CollectionUtils.isNotEmpty(db_nacos)){
                        map.put("db_nacos",(ArrayList)db_nacos);
                        String ips = "";
                        for (int i = 0; i < db_nacos.size(); i++) {
                            ips = ips +"'"+((ArrayList<?>) db_nacos).get(i).toString()+"'";
                            if(i<db_nacos.size()-1){
                                ips = ips+",";
                            }
                        }
                        String sql ="db_nacos select * from naming_instance where service_name = 'DEFAULT_GROUP@@"+servcie+"' and ip in ("+ips+");";
                        map.put("sqldb_nacos",Arrays.asList(sql));
                    }
                }
            }
        }

    }
}
