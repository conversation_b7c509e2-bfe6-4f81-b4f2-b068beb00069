/*


import io.swagger.annotations.Api;

import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
@Api("")
@RestController
@RequestMapping("/api/iauth/api/service")
public class AuthorizationController {

    @PostMapping("/{serviceId}/auth")
    public ResultResponse<String> addOrUpdate(HttpServletRequest request,
                                              @PathVariable("serviceId") String serviceId,
                                              @RequestParam("subServiceId") String subServiceId,
                                              @RequestParam(value = "platform",required = false) String platform) {
        return HttpClient.iauthProxyHttp(request);
    }

    @DeleteMapping("/{serviceId}/auth")
    public ResultResponse<String> delete(HttpServletRequest request,
                                         @RequestParam(value = "platform",required = false) String platform,
                                         @PathVariable("serviceId") String serviceId) {
        return HttpClient.iauthProxyHttp(request);
    }
}

*/
