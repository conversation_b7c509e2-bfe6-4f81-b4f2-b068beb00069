package com.xiaomi.dayu.controller.nacos;

import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.constants.Constants;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@Api(tags = {"Nacos namespace APIs"})
@RestController
@RequestMapping(Constants.NAMESPACE_CONTROLLER_PATH)
public class NamespaceController {


    private static final int NAMESPACE_ID_MAX_LENGTH = 128;

    /**
     * Get namespace list
     *
     * @param request  request
     * @param response response
     * @return namespace list
     */
    @GetMapping
    public ResultResponse<Object> getNamespaces(HttpServletRequest request, HttpServletResponse response) {

        return HttpClient.nacosProxyHttp(request);
    }



}
