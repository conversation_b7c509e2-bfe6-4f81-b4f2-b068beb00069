package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.exception.VersionValidationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.ConvertUtil;
import com.xiaomi.dayu.model.dto.BalancingDTO;
import com.xiaomi.dayu.service.OverrideService;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.impl.ValidationPermissionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 负载均衡
 */
@Deprecated
@RestController
@RequestMapping("/api/rules/balancing")
public class LoadBalanceController {

    private final OverrideService overrideService;
    private final ProviderService providerService;
    @Autowired
    private ValidationPermissionService validationPermissionService;
    @Autowired
    public LoadBalanceController(OverrideService overrideService, ProviderService providerService) {
        this.overrideService = overrideService;
        this.providerService = providerService;
    }

    @RequestMapping(method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public ResultResponse<Boolean> createLoadbalance(@RequestBody BalancingDTO balancingDTO) throws ParamValidationException {
        validationPermissionService.checkPermission(balancingDTO);
        
        if (StringUtils.isBlank(balancingDTO.getService()) && StringUtils.isBlank(balancingDTO.getApplication())) {
            throw new ParamValidationException("Either Service or application is required.");
        }
        String application = balancingDTO.getApplication();
        if (StringUtils.isNotEmpty(application) && this.providerService.findVersionInApplication(application).equals("2.6")) {
            throw new VersionValidationException("dubbo 2.6 does not support application scope load balancing config");
        }
        overrideService.saveBalance(balancingDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> updateLoadbalance(@PathVariable String id, @RequestBody BalancingDTO balancingDTO) throws ParamValidationException {
        validationPermissionService.checkPermission(balancingDTO);
        
        if (id == null) {
            throw new ParamValidationException("Unknown ID!");
        }
        BalancingDTO balancing = overrideService.findBalance(id);
        if (balancing == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }

        overrideService.saveBalance(balancingDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(method = RequestMethod.GET)
    public ResultResponse<List<BalancingDTO>> searchLoadbalances(@RequestParam(required = false) String service,
                                                 @RequestParam(required = false) String application,

                                                 @RequestParam(required = false) String serviceVersion,
                                                 @RequestParam(required = false) String serviceGroup) {

        if (StringUtils.isBlank(service) && StringUtils.isBlank(application)) {
            throw new ParamValidationException("Either service or application is required");
        }
        List<BalancingDTO> balancingDTOS = new ArrayList<>();
        validationPermissionService.checkPermission(Constants.PROVIDER_SIDE,application, service, serviceGroup,serviceVersion);
        
        BalancingDTO balancingDTO;
        if (StringUtils.isNotBlank(application)) {
            balancingDTO = overrideService.findBalance(application);
        } else {
            BalancingDTO dto = new BalancingDTO();
            dto.setService(service);
            dto.setServiceVersion(serviceVersion);
            dto.setServiceGroup(serviceGroup);
            String id = ConvertUtil.getIdFromDTO(dto);
            balancingDTO = overrideService.findBalance(id);
        }

        if (balancingDTO != null) {
            balancingDTOS.add(balancingDTO);
        }
        return ResultResponse.success(balancingDTOS);
    }
    @RequestMapping(value = "queryAll",method = RequestMethod.GET)
    public ResultResponse<List<BalancingDTO>> queryAll(){
        List<BalancingDTO> findAll = overrideService.findAllBalancing();
        return ResultResponse.success(findAll);
    }
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public BalancingDTO detailLoadBalance(@PathVariable String id) throws ParamValidationException {
        BalancingDTO balancingDTO = overrideService.findBalance(id);
        if (balancingDTO == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }
        return balancingDTO;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    public ResultResponse<Boolean> deleteLoadBalance(@PathVariable String id) {
        if (id == null) {
            throw new IllegalArgumentException("Argument of id is null!");
        }
        overrideService.deleteBalance(id);
        return ResultResponse.success(true);
    }


}
