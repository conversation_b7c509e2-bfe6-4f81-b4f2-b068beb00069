package com.xiaomi.dayu.controller.nacos;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.dayu.annotation.RecordOperation;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.constants.Constants;
import com.xiaomi.dayu.model.dto.ConfigInfoAndExtendDTO;
import com.xiaomi.dayu.model.requests.SearchConfigReq;
import com.xiaomi.dayu.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = {"Nacos config APIs"})
@RestController
@RequestMapping(Constants.CONFIG_CONTROLLER_PATH)
public class NacosConfigController {

    private static final String PUBLIC = "public";

    private static final String TENANT = "tenant";

    @Autowired
    private SearchConfigService searchConfigService;

    @Autowired
    private NacosConfigExtendService nacosConfigExtendService;

    @Autowired
    private CheckConfigService checkConfigService;

    @Autowired
    private UserService userService;
    @Autowired
    private ApprovalService approvalService;

    /**
     * 增加或更新非聚合数据。
     */
//    @RecordOperation
    @PostMapping
    public ResultResponse<Object> publishConfig(
            HttpServletRequest request,
            @RequestParam("dataId") String dataId,
            @RequestParam("group") String group,
            @RequestParam(value = "tenant", required = false, defaultValue = StringUtils.EMPTY) String tenant,
            @RequestParam("content") String content,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "appName") String appName,
            @RequestParam(value = "id", required = false) Long id,
            // see Enums.OperateType for more details
            @RequestParam(value = "operateType") int operateType,
            @RequestParam(value = "envId", required = false) String envId,
            @RequestParam(value = "envName", required = false) String envName,
            // see Enums.ConfigType for more details
            @RequestParam(value = "configType", required = false) Integer configType,
            @RequestParam(value = "approver", required = false) @ApiParam(value = "审批人列表，多个用‘,’连接") String approver,
            @RequestParam(value = "applyRemark", required = false)@ApiParam(value = "申请备注") String applyRemark
    ) {
        PublishConfigReq publishConfigReq = PublishConfigReq.builder()
                .dataId(dataId)
                .group(group)
                .tenant(tenant)
                .content(content)
                .type(type)
                .appName(appName)
                .id(id)
                .operateType(operateType)
                .envId(envId)
                .envName(envName)
                .configType(configType)
                .approver(approver)
                .applyRemark(applyRemark)
                .build();
        try{
            this.checkConfigService.checkPublishConfig(publishConfigReq, Enums.ChannelType.HTTP);
            ResultResponse<Object> response = this.nacosConfigExtendService.publishConfig(request, publishConfigReq);
            return response;
        }catch (Exception e){
            log.warn("publishConfig 异常，publishConfigReq {}", JSON.toJSONString(publishConfigReq.toString()));
            throw new ServiceException("10013："+ e.getMessage());
        }
    }

    /**
     * 取数据
     */
    @GetMapping
    public ResultResponse<Object> getConfig(
            HttpServletRequest request,
            @RequestParam("dataId") String dataId,
            @RequestParam("group") String group,
            @RequestParam(value = "tenant", required = false, defaultValue = StringUtils.EMPTY) String tenant) {
        this.checkConfigService.checkConfigDefault(tenant, group, dataId);
        // response.getData() is not always json; can not retrieve json from response data;
        return HttpClient.nacosProxyHttp(request);
    }

    /**
     * 取数据
     */
    @GetMapping(params = "show=all")
    public ResultResponse<Object> detailConfigInfo(
            HttpServletRequest request,
            @RequestParam("dataId") String dataId,
            @RequestParam("group") String group,
            @RequestParam(value = "tenant", required = false, defaultValue = StringUtils.EMPTY) String tenant) {
        this.checkConfigService.checkConfigDefault(tenant, group, dataId);
        ResultResponse<Object> response = HttpClient.nacosProxyHttp(request);

        if (response.isSuccess() && response.getData() != null) {
            JSONObject data = (JSONObject) response.getData();
            if (StringUtils.isBlank(data.getString("tenant"))) {
                data.put("tenant", "public");
            }
            this.nacosConfigExtendService.extendDataById(data, data.getLong("id"));
        } else {
            log.warn("get all config data is empty,  dataId {}, group {}, tenant {}", dataId, group, tenant);
        }
        return response;
    }

    @RecordOperation
    @DeleteMapping
    public ResultResponse<Object> deleteConfig(
            HttpServletRequest request,
            @RequestParam Long id,
            @RequestParam("dataId") String dataId,
            @RequestParam("group") String group,
            @RequestParam(value = "tenant", required = false, defaultValue = StringUtils.EMPTY) String tenant,
            @RequestParam("appName") String appName,
            @RequestParam(value = "approver") @ApiParam(value = "审批人列表，多个用‘,’连接") String approver,
            @RequestParam(value = "applyRemark")@ApiParam(value = "申请备注") String applyRemark
    ) {
        this.checkConfigService.checkConfigByType(tenant, group, dataId, Enums.OperateType.DELETE.getValue());

        PublishConfigReq publishConfigReq = PublishConfigReq.builder()
                .id(id)
                .dataId(dataId)
                .group(group)
                .tenant(tenant)
                .appName(appName)
                .applyRemark(applyRemark)
                .approver(approver)
                .build();

        approvalService.createApproval(publishConfigReq, Enums.OperateType.DELETE);
        //return HttpClient.nacosProxyHttp(request);
        return ResultResponse.success(true);
    }

    /**
     * 查询配置信息，返回JSON格式。
     */
    @GetMapping(params = "search=accurate")
    public ResultResponse<Object> searchConfig(HttpServletRequest request,
                                               @RequestParam(required = false) String appName,
                                               @RequestParam(required = false) Long pageNo,
                                               @RequestParam(required = false) Integer pageSize,
                                               @RequestParam(required = false) String tenant,
                                               @RequestParam(required = false) String dataId,
                                               @RequestParam(required = false) String groupId,
                                               @RequestParam(required = false) Integer configType,
                                               @RequestParam(required = false) String envId,
                                               @RequestParam(required = false) boolean withBlob,
                                               @RequestParam(required = false) boolean asc) {
        if (configType != null && Arrays.stream(Enums.ConfigType.values()).noneMatch(obj -> obj.getValue() == configType)) {
            throw new IllegalArgumentException("配置类型参数不对");
        }
        List<String> appNameList = null;

        if (!this.userService.checkAdmin(UserInfoThreadLocal.getUserInfo().getUserName()) &&
                Enums.ConfigType.requiresAppName(configType) && StringUtils.isBlank(appName)) {
            appNameList = UserInfoThreadLocal.getUserInfo().getApplicationNames();

            if (appNameList == null || appNameList.isEmpty()) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("pageItems", null);
                hashMap.put("totalCount", 0);
                return ResultResponse.success(hashMap);
            }
        }
        SearchConfigReq searchConfigReq = SearchConfigReq.builder()
                .appName(appName)
                .appNameList(appNameList)
                .pageNo(pageNo)
                .pageSize(pageSize)
                .tenantId(tenant)
                .dataId(dataId)
                .groupId(groupId)
                .configType(configType)
                .envId(envId)
                .withBlob(withBlob)
                .asc(asc)
                .build();
        List<ConfigInfoAndExtendDTO> configInfoExtends = this.searchConfigService.searchConfigAndExtend(searchConfigReq);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("pageItems", configInfoExtends);
        hashMap.put("totalCount", this.searchConfigService.countTotalConfigs(searchConfigReq));
        return ResultResponse.success(hashMap);
    }

    @GetMapping(params = "beta=true")
    public ResultResponse<Object> queryBeta(
            HttpServletRequest request,
            @RequestParam(value = "dataId") String dataId,
            @RequestParam(value = "group") String group,
            @RequestParam(value = "tenant", required = false, defaultValue = StringUtils.EMPTY) String tenant) {
        ResultResponse<Object> response = HttpClient.nacosProxyHttp(request);
        if (response.isSuccess() && response.getData() != null) {
            JSONObject jsonObject = (JSONObject) response.getData();
            if (jsonObject != null) {
                response.setMessage(jsonObject.getString("message"));
                response.setData(jsonObject.get("data"));
            }
        }
        return response;
    }

    @DeleteMapping(params = "beta=true")
    public ResultResponse<Object> stopBeta(
            HttpServletRequest request,
            @RequestParam(value = "dataId") String dataId,
            @RequestParam(value = "group") String group,
            @RequestParam(value = "tenant", required = false, defaultValue = StringUtils.EMPTY) String tenant) {
        ResultResponse<Object> response = HttpClient.nacosProxyHttp(request);
        if (response.isSuccess() && response.getData() != null) {
            JSONObject jsonObject = (JSONObject) response.getData();
            if (jsonObject != null) {
                response.setMessage(jsonObject.getString("message"));
                response.setData(jsonObject.get("data"));
            }
        }
        return response;
    }

    /**
     * 订阅改配置的客户端信息
     */
    @RequestMapping(value = "/listener", method = RequestMethod.GET)
    @ResponseBody
    public ResultResponse<Object> getListeners(HttpServletRequest request,
                                               @RequestParam("dataId") String dataId,
                                               @RequestParam("group") String group,
                                               @RequestParam(value = "tenant", required = false) String tenant,
                                               @RequestParam(value = "sampleTime", required = false,
                                                       defaultValue = "1") int sampleTime) {
        this.checkConfigService.checkConfigDefault(tenant, group, dataId);
        return HttpClient.nacosProxyHttp(request);
    }
    @RequestMapping(value = "/checkExists", method = RequestMethod.GET)
    @ResponseBody
    public ResultResponse<Boolean> checkExists(HttpServletRequest request,
                                               @RequestParam("dataId") String dataId,
                                               @RequestParam("group") String group,
                                               @RequestParam(value = "tenant", required = false) String tenant) {
        if (StringUtils.isNotBlank(HttpClient.getNacosConfig(tenant, group, dataId))) {
            return ResultResponse.success(true);
        }
        return ResultResponse.success(false);
    }
}
