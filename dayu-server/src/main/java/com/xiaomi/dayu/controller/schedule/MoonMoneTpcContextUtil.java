package com.xiaomi.dayu.controller.schedule;

import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.youpin.hermes.bo.RoleBo;
import com.xiaomi.youpin.hermes.bo.request.QueryRoleRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import run.mone.moon.api.bo.common.Constants;
import run.mone.moon.api.bo.user.MoonMoneTpcContext;
import run.mone.moon.api.enums.TenantEnum;

import java.util.List;

@Component
public class MoonMoneTpcContextUtil {
    @Autowired
    private AccountServiceRpc userService;

    public MoonMoneTpcContext getMoonMoneTpcContext() {
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        String userName = userInfo.getUserName();
        MoonMoneTpcContext moneTpcContext = new MoonMoneTpcContext();
        moneTpcContext.setAccount(userName);
        moneTpcContext.setUserType(0);

        moneTpcContext.setTenant(getEnv(userInfo.getCurrEnv()));
        moneTpcContext.setRole(Constants.ROLE_WORK);
        return moneTpcContext;
    }

    private String getEnv(String currEnv) {
        switch (currEnv) {
            case "sgp":
                return TenantEnum.SGP.tenant;
            case "global":
                return TenantEnum.GLOBAL.tenant;
            case "china":
                return TenantEnum.CNZone.tenant;
            case "eur":
                return TenantEnum.EUR.tenant;
            default:
                return TenantEnum.CNZone.tenant;
        }
    }

    /*private  int getRole(String userName) {
        QueryRoleRequest queryRoleRequest = new QueryRoleRequest();
        queryRoleRequest.setProjectName(Constants.PROJECT_NAME);
        queryRoleRequest.setUserName(userName);
        List<RoleBo> roles = userService.getRoleByProjectName(queryRoleRequest);
        int role;
        if (roles != null && roles.size() > 0 && roles.parallelStream().filter(e -> e.getName().contains("admin")).findAny().orElse(null) != null) {
            role = Constants.ROLE_ADMIN;
        } else if (roles != null && roles.size() > 0 && roles.parallelStream().filter(e -> e.getName().contains("work")).findAny().orElse(null) != null) {
            role = Constants.ROLE_WORK;
        } else {
            role = Constants.ROLE_WORK;
        }
        return role;
    }*/
}
