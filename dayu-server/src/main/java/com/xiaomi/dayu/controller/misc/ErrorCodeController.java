package com.xiaomi.dayu.controller.misc;

import com.xiaomi.dayu.rpc.ErrorCodeServiceRpc;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.mone.drizzleapi.bo.ErrorCodeBo;
import com.xiaomi.mone.drizzleapi.bo.ListErrorCodeReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.xiaomi.dayu.constants.Constants.DEFAULT_RPC_TIMEOUT_MS;

/**
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @since 2022/5/10
 */
@RestController
@RequestMapping("/api/misc/error/code")
public class ErrorCodeController {

    @Autowired
    private ErrorCodeServiceRpc errorCodeService;

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<Object> listCodes(HttpServletRequest request,
                                    @RequestParam Integer sid,
                                    @RequestParam(required = false) String keyword,
                                    @RequestParam(required = false) Integer pageNo,
                                    @RequestParam(required = false) Integer pageSize) throws ExecutionException, InterruptedException, TimeoutException {
        ListErrorCodeReq reqBody = ListErrorCodeReq.builder()
                .sid(sid)
                .keyword(keyword)
                .pageNo(pageNo)
                .pageSize(pageSize)
                .build();
        CompletableFuture<List<ErrorCodeBo>> f1 = CompletableFuture.supplyAsync(
                () -> this.errorCodeService.listCodes(reqBody)
        );
        CompletableFuture<Long> f2 = CompletableFuture.supplyAsync(
                () -> this.errorCodeService.countCodes(reqBody)
        );
        CompletableFuture<Void> cf = CompletableFuture.allOf(f1, f2);
        cf.get(DEFAULT_RPC_TIMEOUT_MS, TimeUnit.MILLISECONDS);

        Map<String, Object> dict = new HashMap<>();
        dict.put("codes", f1.get());
        dict.put("count", f2.get());
        return Result.success(dict);
    }
}
