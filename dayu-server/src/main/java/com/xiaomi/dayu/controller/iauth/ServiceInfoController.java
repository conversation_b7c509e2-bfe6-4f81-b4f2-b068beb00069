package com.xiaomi.dayu.controller.iauth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
@Deprecated
@Api(description = "服务端应用")
@RestController
@RequestMapping("/api/iauth/api/service")
public class ServiceInfoController {
    private static final Logger logger = LoggerFactory.getLogger(ServiceInfoController.class);

    private static final String TEMPLATE_IAUTH_CONFIG="iauth.sdk.service.mode=true\n" +
            "iauth.sdk.service.name=${name}\n" +
            "iauth.sdk.service.serverKey=${serviceKey}\n" +
            "iauth.sdk.service.env=staging\n" +
            "iauth.sdk.service.signVersion=2\n" +
            "iauth.sdk.service.platform=china-area";

    @GetMapping("/list")
    public ResultResponse<String> searchServiceList(HttpServletRequest request,
                                                    @RequestParam(value = "platform",required = false) String platform,
                                                    @RequestParam("serviceId") String serviceId,
                                                    @ApiParam(value = "查询条件") @RequestParam(value = "query",required = false) String query,
                                                    @ApiParam(value = "0：查询自己，2:查询其他") @RequestParam("source") int source,
                                                    @RequestParam(value = "filterNoSub", required = false, defaultValue = "true") Boolean filterNoSub,
                                                    @RequestParam("pageNo") Integer pageNo,
                                                    @RequestParam("pageSize") Integer pageSize,
                                                    @ApiParam(value = "排序：ASC：升序，DESC：降序")   @RequestParam("sort") String sort)  {
        return HttpClient.iauthProxyHttp(request);
    }
    /**
     * 创建新的Service
     *
     */
    @PostMapping("/")
    public ResultResponse<String> createService(HttpServletRequest request,
                                                @RequestParam(value = "description",required = false) String desc,
                                                @RequestParam("serviceId") String serviceId,
                                                @RequestParam(value = "platform",required = false) String platform,
                                                @RequestParam(value = "department",required = false) String department)  {
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request, "/api/service", RequestMethod.POST, null, bodyParams);
        bodyParams.put("department","中国区");

        if(response.isSuccess()){
            initIauthConfig(request,serviceId,response);
        }
        return response;
    }
    private void initIauthConfig(HttpServletRequest request,String appName, ResultResponse<String> response) {
        JSONObject jsonObject = JSON.parseObject(response.getData());
        HashMap<String, String> params = new HashMap<>();
        String content = TEMPLATE_IAUTH_CONFIG.replace("${name}", jsonObject.getString("serviceId")).replace("${serviceKey}", jsonObject.getString("serviceKey"));
        params.put("content",content);
        params.put("appName",appName);
        params.put("group","DEFAULT_GROUP");
        params.put("dataId","iauth-provider-"+appName);
        HttpClient.nacosProxyHttp(request,"/nacos/v1/cs/configs",RequestMethod.POST.name(),params);
    }

    /**
     * 更新私钥信息
     *
     */
    @GetMapping("/{serviceId}/update")
    public ResultResponse<String> updateSecret(HttpServletRequest request,
                                               @PathVariable("serviceId") String sid,
                                               @RequestParam(value = "platform",required = false) String platform)  {
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request);
        if(response.isSuccess()){
            updateIauthConfig(request,sid,response);
        }
        return response;
    }
    private void updateIauthConfig(HttpServletRequest request,String appName, ResultResponse<String> response) {
        HashMap<String, String> params = new HashMap<>();
        String content = TEMPLATE_IAUTH_CONFIG.replace("${name}", appName).replace("${serviceKey}", response.getData());
        params.put("content",content);
        params.put("appName",appName);
        params.put("group","DEFAULT_GROUP");
        params.put("dataId","iauth-provider-"+appName);
        HttpClient.nacosProxyHttp(request,"/nacos/v1/cs/configs",RequestMethod.POST.name(),params);
    }
    /**
     * 查看私钥信息
     *
     */
    @GetMapping("/{serviceId}")
    public ResultResponse<String> getSecret(HttpServletRequest request,
                                            @PathVariable("serviceId") String sid,
                                            @RequestParam(value = "platform",required = false) String platform,
                                            @ApiParam(value = "填写‘secret’") @RequestParam("query") String query)  {
        return HttpClient.iauthProxyHttp(request);
    }

}