package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.exception.VersionValidationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.ConvertUtil;
import com.xiaomi.dayu.model.dto.WeightDTO;
import com.xiaomi.dayu.registry.config.GovernanceConfiguration;
import com.xiaomi.dayu.service.OverrideService;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.impl.ValidationPermissionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 权重调整
 */
@Deprecated
@RestController
@RequestMapping("/api/rules/weight")
public class WeightController {

    private final OverrideService overrideService;
    private final ProviderService providerService;
    @Autowired
    private ValidationPermissionService validationPermissionService;

    @Autowired
    private GovernanceConfiguration governanceConfiguration;

    @Autowired
    public WeightController(OverrideService overrideService, ProviderService providerService) {
        this.overrideService = overrideService;
        this.providerService = providerService;
    }

    @RequestMapping(method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public ResultResponse<Boolean> createWeight(@RequestBody WeightDTO weightDTO) {
        validationPermissionService.checkPermission(weightDTO);
        
        if (StringUtils.isBlank(weightDTO.getService()) && StringUtils.isBlank(weightDTO.getApplication())) {
            throw new ParamValidationException("Either Service or application is required.");
        }
        String application = weightDTO.getApplication();
        if (StringUtils.isNotEmpty(application) && this.providerService.findVersionInApplication(application).equals("2.6")) {
            throw new VersionValidationException("dubbo 2.6 does not support application scope blackwhite list config");
        }
        overrideService.saveWeight(weightDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> updateWeight(@PathVariable String id, @RequestBody WeightDTO weightDTO) {
        validationPermissionService.checkPermission(weightDTO);
        
        if (id == null) {
            throw new ParamValidationException("Unknown ID!");
        }
        WeightDTO weight = overrideService.findWeight(id);
        if (weight == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }
        overrideService.updateWeight(weightDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(method = RequestMethod.GET)
    public ResultResponse<List<WeightDTO>> searchWeight(@RequestParam(required = false) String service,
                                        @RequestParam(required = false) String application,

                                        @RequestParam(required = false) String serviceVersion,
                                        @RequestParam(required = false) String serviceGroup) {
        if (StringUtils.isBlank(service) && StringUtils.isBlank(application)) {
            throw new ParamValidationException("Either service or application is required");
        }
        validationPermissionService.checkPermission(Constants.PROVIDER_SIDE,application,service,serviceGroup,serviceVersion);
        
        WeightDTO weightDTO;
        if (StringUtils.isNotBlank(application)) {
            weightDTO = overrideService.findWeight(application);
        } else {
            WeightDTO dto = new WeightDTO();
            dto.setService(service);
            dto.setServiceVersion(serviceVersion);
            dto.setServiceGroup(serviceGroup);
            String id = ConvertUtil.getIdFromDTO(dto);
            weightDTO = overrideService.findWeight(id);
        }
        List<WeightDTO> weightDTOS = new ArrayList<>();
        if (weightDTO != null) {
            weightDTOS.add(weightDTO);
        }

        return ResultResponse.success(weightDTOS);
    }
    @RequestMapping(value = "queryAll",method = RequestMethod.GET)
    public ResultResponse<List<WeightDTO>> queryAll(){
        List<WeightDTO> findAll = overrideService.findAllWeight();
        return ResultResponse.success(findAll);
    }
    @RequestMapping(value = "queryWeight",method = RequestMethod.GET)
    public ResultResponse<List<WeightDTO>> queryWeight(@RequestParam String application){
        List<WeightDTO> list = overrideService.queryWeight(application);
        return ResultResponse.success(list);
    }
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public WeightDTO detailWeight(@PathVariable String id) {
        WeightDTO weightDTO = overrideService.findWeight(id);
        if (weightDTO == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }
        return weightDTO;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    public ResultResponse<Boolean> deleteWeight(@PathVariable String id) {
        overrideService.deleteWeight(id);
        return ResultResponse.success(true);
    }
}
