package com.xiaomi.dayu.controller.approval;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.approval.ApprovalVO;
import com.xiaomi.dayu.service.ApprovalService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/approval")
public class ApprovalController {
    @Resource
    private ApprovalService approvalService;
    @GetMapping("/pageList")
    public ResultResponse<PageResult<ApprovalVO>> pageList(
            @RequestParam(required = false) @ApiParam String appName,
            @RequestParam(required = false)  @ApiParam(value = "申请类型：10：nacos配置") Integer approveType,
            @RequestParam(required = false)  @ApiParam(value = "审批状态：10：提交，20：通过，30：驳回，40：取消") Integer status,
            @RequestParam(required = false,defaultValue = "1")  @ApiParam Integer pageNum,
            @RequestParam(required = false,defaultValue = "10") @ApiParam Integer pageSize){
        if(StringUtils.isNotBlank(appName)){
            CheckPermissUtil.checkPermissByAppName(appName);
        }
        return ResultResponse.success(approvalService.queryByPage(appName,approveType,status,pageNum,pageSize));
    }
    @PostMapping("/updateStatus")
    public ResultResponse<Boolean> updateStatus(
            @RequestParam Integer id,
            @RequestParam String appName,
            @RequestParam Integer status,
            @RequestParam String operateRemark
            ){
        CheckPermissUtil.checkPermissByAppName(appName);
        return ResultResponse.success(approvalService.updateStatus(id,appName,status,operateRemark));
    }




}
