package com.xiaomi.dayu.controller.iauth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.dto.ServiceDTO;
import com.xiaomi.dayu.model.iauth.SubServiceInfo;
import com.xiaomi.dayu.model.iauth.SubServiceInfoDTO;
import com.xiaomi.dayu.service.NamingInstanceService;
import com.xiaomi.dayu.service.ProviderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Deprecated
@Api(description = "接口级别")
@RestController
@RequestMapping("/api/iauth/api/subservice")
public class SubServiceController {
    @Autowired
    private ProviderService providerService;

    @Autowired
    private NamingInstanceService namingInstanceService;
    @ApiOperation(value = "根据service查询子服务")
    @GetMapping("/")
    public ResultResponse<String> getSubServices(HttpServletRequest request,
                                                 @RequestParam(value = "platform",required = false) String platform,
                                                 @ApiParam(value = "对应serviceId") @RequestParam("serviceId") String serviceId,
                                                 @RequestParam(value = "query",required = false) String query,
                                                 @RequestParam(value = "filterNoScope", required = false, defaultValue = "true") Boolean filterNoScope) {
        return HttpClient.iauthProxyHttp(request);
    }

    @DeleteMapping("/")
    public ResultResponse<String> deleteSubService(HttpServletRequest request, 
                                                   @RequestParam(value = "platform",required = false) String platform,
                                                   @ApiParam(value = "对应serviceId")@RequestParam("serviceId") String serviceId,
                                                   @RequestParam("subServices") List<String> subServices) {
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        bodyParams.put("subServices",subServices);
        return HttpClient.iauthProxyHttp(request,bodyParams);
    }

    @PostMapping("/")
    public ResultResponse<String> createSubService(HttpServletRequest request,
                                               @RequestParam(value = "platform",required = false) String platform,
                                               @ApiParam(value = "对应serviceId")@RequestParam("serviceId") String serviceId,
                                               @RequestParam("subServices") @ApiParam(value = "多条数据用，‘,’") String subServices,
                                               @RequestParam(value = "description",required = true) String description,
                                               @RequestParam("interfaceName") String interfaceName) {
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        Map<String, String> collect = Arrays.stream(subServices.split(",")).collect(Collectors.toMap(String::toString, String::toString));
        bodyParams.put("subServices", collect);
        return HttpClient.iauthProxyHttp(request,bodyParams);
    }

    @PostMapping("/update")
    public ResultResponse<String> updateSubService(
                                                    HttpServletRequest request,
                                                    @RequestParam(value = "id",required = false) Integer id,
                                                    @RequestParam(value = "platform",required = false) String platform,
                                                    @RequestParam("serviceId") String serviceId,
                                                    @RequestParam("subService") String subService,
                                                    @RequestParam("status") Integer status) {
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        if(status == 1 && id == 0 ){
            bodyParams.put("subServices", Arrays.stream(subService.split(",")).collect(Collectors.toMap(String::toString, String::toString)));
            HttpClient.iauthProxyHttp(request,"/api/subservice",RequestMethod.POST, bodyParams);
        }
        return HttpClient.iauthProxyHttp(request,bodyParams);
    }

    @ApiOperation(value = "查询可鉴权服务")
    @GetMapping("/canIauth")
    public ResultResponse<String> canService(HttpServletRequest request,
                                                 @RequestParam(value = "platform",required = false) String platform,
                                                 @ApiParam(value = "对应serviceId") @RequestParam("serviceId") String serviceId,
                                                 @RequestParam(value = "query",required = false) String query) {
        HashMap<String, String> params = new HashMap<>();
        params.put("serviceId",serviceId);
        params.put("filterNoScope","false");
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request,"/api/subservice/",RequestMethod.GET,params,null);

        if(response.isSuccess()){
            List<SubServiceInfo> serviceInfos = JSON.parseObject(response.getData(), new TypeReference<List<SubServiceInfo>>() {
            });
            HashMap<String, SubServiceInfo> hashMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(serviceInfos)){
                for (SubServiceInfo serviceInfo : serviceInfos) {
                    hashMap.put(serviceInfo.getSubService(),serviceInfo);
                }
            }
            Collection<ServiceDTO> serviceDTOS = null;
            if(namingInstanceService.getIsQueryNaming4DB()){
                ResultResponse<PageResult<ServiceDTO>> providerServiceDTOS = namingInstanceService.getProviderServiceDTOS(Constants.APPLICATION,"*",  serviceId,true,1, 1, 100);
                serviceDTOS = providerServiceDTOS.getData().getContent();
            }else{
                serviceDTOS = providerService.getServiceDTOS(Constants.APPLICATION,serviceId ,true);
            }
            if(CollectionUtils.isEmpty(serviceDTOS)){
                return ResultResponse.success(null);
            }
            List<SubServiceInfoDTO> subServiceInfoDTOs = serviceDTOS.stream().collect(Collectors.toMap(ServiceDTO::getService, Function.identity(),(v1,v2)->v1)).values().stream().distinct().map(serviceDTO -> {
                if (hashMap.containsKey(serviceDTO.getService())) {
                    SubServiceInfo swappedSubServiceInfo = hashMap.get(serviceDTO.getService());
                    SubServiceInfoDTO subServiceInfoDTO = new SubServiceInfoDTO();
                    BeanUtils.copyProperties(swappedSubServiceInfo,subServiceInfoDTO);
                    return subServiceInfoDTO;
                } else {
                    SubServiceInfoDTO subServiceInfoDTO = covertServiceDTO(serviceDTO);
                    hashMap.put(serviceDTO.getService(),subServiceInfoDTO);
                    return subServiceInfoDTO;
                }
            }).sorted().collect(Collectors.toList());
            return ResultResponse.success(JSON.toJSONString(subServiceInfoDTOs));
        }
        return ResultResponse.success(null);

    }

    private SubServiceInfoDTO covertServiceDTO(ServiceDTO serviceDTO) {
        SubServiceInfoDTO subServiceInfoDTO = new SubServiceInfoDTO();
        subServiceInfoDTO.setSubService(serviceDTO.getService());
        subServiceInfoDTO.setStatus(0);

        return subServiceInfoDTO;
    }
}
