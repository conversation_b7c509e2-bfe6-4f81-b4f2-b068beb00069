package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.exception.VersionValidationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.ConvertUtil;
import com.xiaomi.dayu.model.dto.AccessDTO;
import com.xiaomi.dayu.model.dto.ConditionRouteDTO;
import com.xiaomi.dayu.service.ConsumerService;
import com.xiaomi.dayu.service.RouteService;
import com.xiaomi.dayu.service.impl.ValidationPermissionService;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.logger.Logger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 黑白名单
 */
@Api("")
@RestController
@RequestMapping("/api/rules/access")
public class AccessesController {
    private static final Logger logger = LoggerFactory.getLogger(AccessesController.class);

    private final RouteService routeService;
    private final ConsumerService consumerService;

    @Autowired
    private ValidationPermissionService validationPermissionService;

    @Autowired
    public AccessesController(RouteService routeService, ConsumerService consumerService) {
        this.routeService = routeService;
        this.consumerService = consumerService;
    }
    @RequestMapping(method = RequestMethod.GET)
    public ResultResponse<List<AccessDTO>> searchAccess(@RequestParam(required = false) String service,
                                                        @RequestParam(required = false) String application,
                                                        @RequestParam(required = false) String serviceVersion,
                                                        @RequestParam(required = false) String serviceGroup) {
        if (StringUtils.isBlank(service) && StringUtils.isBlank(application)) {
            throw new ParamValidationException("Either service or application is required");
        }
        List<AccessDTO> accessDTOS = new ArrayList<>();
        AccessDTO accessDTO;
        validationPermissionService.checkPermission(Constants.CONSUMER_SIDE,application, service, serviceGroup, serviceVersion);
        if (StringUtils.isNotBlank(application)) {
            accessDTO = routeService.findAccess(application);
        } else {
            AccessDTO dto = new AccessDTO();
            dto.setService(service);
            dto.setServiceVersion(serviceVersion);
            dto.setServiceGroup(serviceGroup);
            String id = ConvertUtil.getIdFromDTO(dto);
            accessDTO = routeService.findAccess(id);
        }
        if (accessDTO != null) {
            accessDTO.setEnabled(true);
            accessDTOS.add(accessDTO);
        }
        return ResultResponse.success(accessDTOS);
    }
    @RequestMapping(value = "queryAll",method = RequestMethod.GET)
    public ResultResponse<List<AccessDTO>> queryAll(){
        List<AccessDTO> findAll = routeService.findAllAccess();
        return ResultResponse.success(findAll);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public AccessDTO detailAccess(@PathVariable String id) {
        AccessDTO accessDTO = routeService.findAccess(id);
        if (accessDTO == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }
        return accessDTO;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    public ResultResponse<Boolean> deleteAccess(@PathVariable String id) {
        routeService.deleteAccess(id);
        return ResultResponse.success(true);
    }

    @RequestMapping(method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public ResultResponse<Boolean> createAccess(@RequestBody AccessDTO accessDTO) {
        if (StringUtils.isBlank(accessDTO.getService()) && StringUtils.isBlank(accessDTO.getApplication())) {
            throw new ParamValidationException("Either Service or application is required.");
        }
        String application = accessDTO.getApplication();
        if (StringUtils.isNotEmpty(application) && "2.6".equals(consumerService.findVersionInApplication(application))) {
            throw new VersionValidationException("dubbo 2.6 does not support application scope blackwhite list config");
        }
        //添加校验
        validationPermissionService.checkPermission(accessDTO);

        if (accessDTO.getBlacklist() == null && accessDTO.getWhitelist() == null) {
            throw new ParamValidationException("One of Blacklist/Whitelist is required.");
        }
        routeService.createAccess(accessDTO);
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    public ResultResponse<Boolean> updateAccess(@PathVariable String id, @RequestBody AccessDTO accessDTO) {
        validationPermissionService.checkPermission(accessDTO);

        ConditionRouteDTO route = routeService.findConditionRoute(id);
        if (Objects.isNull(route)) {
            throw new ResourceNotFoundException("Unknown ID!");
        }
        routeService.updateAccess(accessDTO);
        return ResultResponse.success(true);
    }

}
