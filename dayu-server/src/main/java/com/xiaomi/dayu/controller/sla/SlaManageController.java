package com.xiaomi.dayu.controller.sla;

import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.SlaManageDTO;
import com.xiaomi.dayu.api.bo.SlaManageRequest;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.service.SlaManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Api("")
@RestController
@RequestMapping("/api/sla")
public class SlaManageController {

//    @NacosValue(value = "${slaTypeMap}", autoRefreshed = true)
   // @NacosValue("#{T(com.alibaba.fastjson.JSON).parseObject(${slaTypeMap}, T(java.util.Map))}")
    private Map<String,String> slaTypeMap = null;
    {
        Map<String,String> slaTypeMap = new HashMap<>();
        this.slaTypeMap = slaTypeMap;
        slaTypeMap.put("QPS","");
        slaTypeMap.put("RT","");
        slaTypeMap.put("P99","");
        slaTypeMap.put("P95","");
       // {"QPS":"","RT":"","P99":"","P95":""}
    }

    @Resource
    private SlaManageService slaManageService;
    @ApiOperation(value = "查询配置列表")
    @PostMapping("/list")
    public ResultResponse<PageResult<SlaManageDTO>> queryList(@RequestBody SlaManageRequest slaManageRequest){
        if(slaManageRequest.getAppId() == null){
            throw new ParamValidationException("appId 不能为空");
        }
        if(StringUtils.isBlank(slaManageRequest.getAppName())){
            throw new ParamValidationException("appName 不能为空");
        }
        CheckPermissUtil.checkPermissByAppName(slaManageRequest.getAppName());
        PageResult<SlaManageDTO> result = slaManageService.queryList(slaManageRequest);
        return ResultResponse.success(result);
    }
    @ApiOperation(value = "创建配置")
    @PostMapping("/create")
    public ResultResponse<Boolean> create(@RequestBody SlaManageRequest slaManageRequest){
        if(slaManageRequest.getAppId() == null){
            throw new ParamValidationException("appId 不能为空");
        }
        if(StringUtils.isBlank(slaManageRequest.getAppName())){
            throw new ParamValidationException("appName 不能为空");
        }
        if(StringUtils.isBlank(slaManageRequest.getClassName())){
            throw new ParamValidationException("className 不能为空");
        }

        CheckPermissUtil.checkPermissByAppName(slaManageRequest.getAppName());
        Boolean result = slaManageService.create(slaManageRequest);
        return ResultResponse.success(result);
    }
    @ApiOperation(value = "更新配置")
    @PostMapping("/update")
    public ResultResponse<Boolean> update(@RequestBody SlaManageRequest slaManageRequest){
        if(slaManageRequest.getAppId() == null){
            throw new ParamValidationException("appId 不能为空");
        }
        if(StringUtils.isBlank(slaManageRequest.getAppName())){
            throw new ParamValidationException("appName 不能为空");
        }
        CheckPermissUtil.checkPermissByAppName(slaManageRequest.getAppName());
        Boolean result = slaManageService.update(slaManageRequest);
        return ResultResponse.success(result);
    }
    @ApiOperation(value = "删除配置")
    @PostMapping("/delete")
    public ResultResponse<Boolean> delete(@RequestBody SlaManageRequest slaManageRequest){
        if(slaManageRequest.getAppId() == null){
            throw new ParamValidationException("appId 不能为空");
        }
        if(StringUtils.isBlank(slaManageRequest.getAppName())){
            throw new ParamValidationException("appName 不能为空");
        }
        CheckPermissUtil.checkPermissByAppName(slaManageRequest.getAppName());
        Boolean result = slaManageService.delete(slaManageRequest);
        return ResultResponse.success(result);
    }
    @ApiOperation(value = "根据id查询历史记录")
    @PostMapping("/his")
    public ResultResponse<PageResult<SlaManageDTO>> his(@RequestBody SlaManageRequest slaManageRequest){
        if(slaManageRequest.getAppId() == null){
            throw new ParamValidationException("appId 不能为空");
        }
        if(StringUtils.isBlank(slaManageRequest.getAppName())){
            throw new ParamValidationException("appName 不能为空");
        }
        CheckPermissUtil.checkPermissByAppName(slaManageRequest.getAppName());
        PageResult<SlaManageDTO> result = slaManageService.queryHis(slaManageRequest);
        return ResultResponse.success(result);
    }
    @ApiOperation(value = "根据id查询历史记录")
    @PostMapping("/sla/map")
    public ResultResponse<Map<String,String>> getSlaTypeMap(){
        return ResultResponse.success(slaTypeMap);
    }


}
