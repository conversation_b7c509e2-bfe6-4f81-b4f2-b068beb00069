package com.xiaomi.dayu.controller.servicemanage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.xiaomi.dayu.InstanceCovert;
import com.xiaomi.dayu.api.bo.ServiceRelationDTO;
import com.xiaomi.dayu.api.bo.ServiceRelationReq;
import com.xiaomi.dayu.api.service.DubboSearchService;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.VersionValidationException;
import com.xiaomi.dayu.common.util.*;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.dto.*;
import com.xiaomi.dayu.model.iauth.SwappedWhiteInfo;
import com.xiaomi.dayu.model.nacos.MachineRoomInstance;
import com.xiaomi.dayu.model.sentinel.FlowRuleEntity;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.dayu.service.*;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import com.xiaomi.youpin.hermes.entity.Project;
import com.xiaomi.youpin.infra.rpc.Result;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.metadata.definition.model.FullServiceDefinition;
import org.apache.dubbo.metadata.definition.model.MethodDefinition;
import org.apache.dubbo.metadata.report.identifier.MetadataIdentifier;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 服务查询
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class ServiceController {

    private final ProviderService providerService;
    private final ConsumerService consumerService;
    private final Gson gson;
    @Autowired
    private AccountServiceRpc accountServiceRpc;
    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;
    @Autowired
    private UserService userService;
    @Autowired
    private RouteService routeService;
    @Autowired
    private OverrideService overrideService;
    @Autowired
    private RegistryServerSync registryServerSync;

    @Value("${monitor.url.pattern}")
    private String monitorUrlPattern;

    @Autowired
    private StatisticsService statisticsService;
    @Autowired
    private NamingInstanceService namingInstanceService;
    @Autowired
    private DubboSearchService dubboSearchService;


    @Autowired
    public ServiceController(ProviderService providerService, ConsumerService consumerService) {
        this.providerService = providerService;
        this.consumerService = consumerService;
        this.gson = new Gson();
    }

    @RequestMapping(value = "/service", method = RequestMethod.GET)
    public ResultResponse<PageResult<ServiceDTO>> searchService(@ApiParam(value = "service:服务名，application：应用，ip：ip") @RequestParam String pattern,
                                                                @ApiParam(value = "查询值") @RequestParam String filter,

                                                                @ApiParam(value = "false:查所有，true：查我参与") @RequestParam(required = false) boolean self,
                                                                @ApiParam(value = "0:服务查询，1：服务列表") @RequestParam(required = false, defaultValue = "0") Integer module,
                                                                @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                                @RequestParam(required = false, defaultValue = "1") int pageNum) {
        if (namingInstanceService.getIsQueryNaming4DB()) {
            ResultResponse<PageResult<ServiceDTO>> providerServiceDTOS = namingInstanceService.getProviderServiceDTOS(pattern, filter, null, self, module, pageNum, pageSize);
            providerService.fillDynamicConfig(providerServiceDTOS.getData().getContent(),"general","provider");
            return providerServiceDTOS;
        }
        final Set<ServiceDTO> serviceDTOS = providerService.getServiceDTOS(pattern, filter, self);
        final List<ServiceDTO> content =
                serviceDTOS.stream()
                        .skip((pageNum - 1) * pageSize)
                        .limit(pageSize)
                        .collect(Collectors.toList());
        providerService.fillExtendInfo(content);
        providerService.fillDynamicConfig(content,"general","provider");
        return ResultResponse.success(new PageResult<>(content, serviceDTOS.size(), pageSize, pageNum));
    }


    @RequestMapping(value = "/service/{service}/provider", method = RequestMethod.GET)
    public ResultResponse<PageResult<Provider>> serviceProvider(@PathVariable String service,
                                                                @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                                @RequestParam(required = false, defaultValue = "1") int pageNum) {
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        List<Provider> providers;
        List<Provider> data;
        int total = 0;
        if (namingInstanceService.getIsQueryNaming4DB()) {
            Page<NamingInstance> namingInstances = namingInstanceService.findPageByFullService(Constants.PROVIDER_SIDE, service, pageNum, pageSize);
            total = (int) namingInstances.getTotal();
            providers = namingInstances.stream().map(namingInstance -> InstanceCovert.namingInstanceCovertToProvider(namingInstance)).collect(Collectors.toList());
        } else {

            providers = providerService.findByService(service);
            total = providers.size();
            providers = providers.subList((pageNum - 1) * pageSize, Math.min(pageNum * pageSize, providers.size()));
        }
        if (CollectionUtils.isNotEmpty(providers)) {
            List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
            providers.forEach(provider -> {
                Project projectBo = UserInfoThreadLocal.getUserInfo().getApplicationMap().get(provider.getApplication());
                if (projectBo != null) {
                    provider.setMonitorUrl(monitorUrlPattern.replace("projectId", projectBo.getId() + "_" + projectBo.getName().replace("-", "_")) + provider.getAddress());
                    provider.setOwn(applicationNames.contains(provider.getApplication()));

                }
                if (!namingInstanceService.getIsQueryNaming4DB()) {
                    provider.setMoneCluster(InstanceUtils.getValueFromParam(provider.getParameters(), "mone.cluster"));
                }
            });
        }
        return ResultResponse.success(new PageResult<>(providers, total, pageSize, pageNum));
    }

    private String getNacosServicerName(String service) {
        //   "providers:{service}:{version}:{group}")
        String group = Tool.getGroup(service);
        String version = Tool.getVersion(service);
        String interfaze = Tool.getInterface(service);
        StringBuilder stringBuilder = new StringBuilder("providers").append(Constants.COLON).append(interfaze);
        if (StringUtils.isNotBlank(version)) {
            stringBuilder.append(Constants.COLON).append(version);
        }
        if (StringUtils.isNotBlank(group)) {
            stringBuilder.append(Constants.COLON).append(group);
        }
        return stringBuilder.toString();
    }

    @RequestMapping(value = "/service/{service}/machineRoom", method = RequestMethod.GET)//@RequestMapping注解，指定请求路径和请求方法
    public ResultResponse<Collection<MachineRoomInstance>> queryMachineRoom(@PathVariable String service) {//查询机房实例的方法，返回结果为ResultResponse<Collection<MachineRoomInstance>>
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        Map<String,MachineRoomInstance> map = new HashMap<>();
        if(namingInstanceService.getIsQueryNaming4DB()){
            Page<NamingInstance> namingInstances = namingInstanceService.findPageByFullService(Constants.PROVIDER_SIDE, service,  1, 100);
            if(CollectionUtils.isNotEmpty(namingInstances)){
                for (NamingInstance namingInstance : namingInstances) {
                    String moneCluster = InstanceUtils.getValueFromJson(namingInstance.getMetadata(), "mone.cluster");

                    if(StringUtils.isBlank(moneCluster)) {
                        moneCluster = "";
                    }
                    if(map.get(moneCluster) == null){
                        MachineRoomInstance machineRoomInstance = new MachineRoomInstance();
                        machineRoomInstance.setNamespaceId(namingInstance.getNamespaceId());
                        machineRoomInstance.setServiceName(namingInstance.getServiceName());
                        machineRoomInstance.setIps(new ArrayList<>());
                        machineRoomInstance.setMachineRoomName(moneCluster);
                        machineRoomInstance.setIps(new ArrayList<String>());
                        map.put(moneCluster,machineRoomInstance);
                    }
                    map.get(moneCluster).getIps().add(namingInstance.getIp()+":"+ namingInstance.getPort());

                }
            }
        }
        return ResultResponse.success(map.values());
    }

    @RequestMapping(value = "/service/{service}/consumer", method = RequestMethod.GET)
    public ResultResponse<PageResult<Consumer>> serviceConsumer(@PathVariable String service,
                                                                @RequestParam(required = false, defaultValue = "10") int pageSize,
                                                                @RequestParam(required = false, defaultValue = "1") int pageNum) {
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        List<Consumer> data;
        int total = 0;
        if (namingInstanceService.getIsQueryNaming4DB()) {
            Page<NamingInstance> namingInstances = namingInstanceService.findPageByFullService(Constants.CONSUMER_SIDE, service, pageNum, pageSize);
            total = (int) namingInstances.getTotal();
            data = namingInstances.stream().map(instance -> InstanceCovert.namingInstanceCovertToConsumer(instance)).collect(Collectors.toList());

        } else {
            List<Consumer> consumers = consumerService.findByService(service);
            total = consumers.size();
            data = consumers.subList((pageNum - 1) * pageSize, Math.min(pageNum * pageSize, consumers.size()));
        }

        if (CollectionUtils.isNotEmpty(data)) {
            List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
            for (Consumer consumer : data) {
                consumer.setOwn(applicationNames.contains(consumer.getApplication()) ? true : false);
                List<UserInfoResult> consumerUsers = accountServiceRpc.queryUsersByAppName(consumer.getApplication());
                if (CollectionUtils.isNotEmpty(consumerUsers)) {
                    consumer.setDevelopers(consumerUsers.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
                }
                ProjectBo projectBo = iProjectServiceRpc.getProjectByName(consumer.getApplication());
                if (projectBo != null && projectBo.getId() != null) {
                    consumer.setMonitorUrl(monitorUrlPattern.replace("projectId", projectBo.getId() + "_" + projectBo.getName().replace("-", "_")) + consumer.getAddress());
                }
                try {
                    List<UserInfoResult> userList = accountServiceRpc.queryUsersByAppName(consumer.getApplication());
                    if (CollectionUtils.isNotEmpty(userList)) {
                        consumer.setDevelopers(userList.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
                        String uid = userService.findUidByUserName(userList.get(0).getUserName());
                        consumer.setDepartment(userService.findDepartmentFullName(uid));
                    }
                } catch (Exception e) {
                    log.error("查询开发人员或部门信息时报错，e=", e);
                }
            }
        }
        return ResultResponse.success(new PageResult<>(data, total, pageSize, pageNum));
    }

    @RequestMapping(value = "/service/{service}/metedata", method = RequestMethod.GET)
    public ResultResponse<Map<String, String>> metedata(@PathVariable String service,
                                                        @RequestParam String application,
                                                        @RequestParam @ApiParam(value = "provider或consumer") String side) {
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        String group = Tool.getGroup(service);
        String version = Tool.getVersion(service);
        String interfaze = Tool.getInterface(service);
        if (!Constants.PROVIDER_SIDE.equals(side) && !Constants.CONSUMER_SIDE.equals(side)) {
            throw new VersionValidationException("side参数错误，必须为provider或consumer");
        }
        Map<String, String> parameters = new HashMap<>();
        /*MetadataIdentifier identifier = new MetadataIdentifier(interfaze, version, group,side, application);
        String metadata ;
        if(Constants.PROVIDER_SIDE.equals(side)){
            metadata = providerService.getProviderMetaData(identifier);
        }else{
            metadata = consumerService.getConsumerMetaData(identifier);
        }

        if (metadata != null) {
            try {
                // for dubbo version under 2.7, this metadata will represent as IP address, like ********.
                // So the json conversion will fail.
                JSONObject jsonObject = JSON.parseObject(metadata);
                try{
                    JSONArray methods = jsonObject.getJSONArray("methods");
                    if(CollectionUtils.isNotEmpty(methods)){
                        for (Object method : methods) {
                            JSONObject methodObject = (JSONObject) method;
                            methodObject.remove("annotations");
                        }
                    }
                }catch (Exception e){

                }
                FullServiceDefinition serviceDefinition = gson.fromJson(JSON.toJSONString(jsonObject), FullServiceDefinition.class);
                parameters = serviceDefinition.getParameters();
            } catch (JsonParseException e) {
               // throw new VersionValidationException("dubbo 2.6 does not support metadata");
                parameters =  gson.fromJson(metadata,Map.class);
            }
        }else{*/
        if (Constants.PROVIDER_SIDE.equals(side)) {
            List<Provider> providerList;
            if (namingInstanceService.getIsQueryNaming4DB()) {
                Page<NamingInstance> namingInstances = namingInstanceService.findPageByFullServiceAndApp(Constants.PROVIDER_SIDE, service, application, 1, 1);
                if (CollectionUtils.isNotEmpty(namingInstances)) {
                    parameters = gson.fromJson(namingInstances.get(0).getMetadata(), Map.class);
                }
            } else {
                providerList = providerService.findByService(service);
                if (CollectionUtils.isNotEmpty(providerList)) {
                    List<Provider> providers = providerList.stream().filter(provider -> application.equals(provider.getApplication())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(providers)) {
                        parameters = InstanceUtils.getParams(providers.get(0).getParameters());
                    }
                }
            }

        } else {
            List<Consumer> consumerList;
            if (namingInstanceService.getIsQueryNaming4DB()) {
                Page<NamingInstance> namingInstances = namingInstanceService.findPageByFullServiceAndApp(Constants.CONSUMER_SIDE, service, application, 1, 1);
                if (CollectionUtils.isNotEmpty(namingInstances)) {
                    parameters = gson.fromJson(namingInstances.get(0).getMetadata(), Map.class);
                }

            } else {
                consumerList = consumerService.findByService(service);
                if (CollectionUtils.isNotEmpty(consumerList)) {
                    List<Consumer> consumers = consumerList.stream().filter(consumer -> application.equals(consumer.getApplication())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(consumers)) {
                        parameters = InstanceUtils.getParams(consumers.get(0).getParameters());
                    }
                }
            }
        }

        // }
        return ResultResponse.success(parameters);
    }
    @RequestMapping(value = "/service/relation/list", method = RequestMethod.GET)
    public Result<Map<String, Map<String, ServiceRelationDTO>>> queryServiceRelations(@RequestParam String providerApp,@RequestParam String type) {
        ServiceRelationReq relationReq = new ServiceRelationReq();
        if("provider".equals(type)){
            relationReq.setProviderApplication(providerApp);
            return dubboSearchService.queryServiceRelations(relationReq);
        }else{
            relationReq.setProviderApplication(providerApp);
            return dubboSearchService.queryConsumerRelations(relationReq);
        }
    }

    @RequestMapping(value = "/service/{service}/detail", method = RequestMethod.GET)
    public ServiceDetailDTO serviceDetail2(@PathVariable String service) {
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        String group = Tool.getGroup(service);
        String version = Tool.getVersion(service);
        String interfaze = Tool.getInterface(service);
        List<Provider> providers;
        if(namingInstanceService.getIsQueryNaming4DB()){
            List<NamingInstance> namingInstances = namingInstanceService.findPageByFullService(Constants.PROVIDER_SIDE, service,1,1);
            providers = namingInstances.stream().map(instance -> InstanceCovert.namingInstanceCovertToProvider(instance)).collect(Collectors.toList());
        }else{
            providers = providerService.findByService(service);
        }
        String application = null;
        ServiceDetailDTO serviceDetailDTO = new ServiceDetailDTO();
        serviceDetailDTO.setGroup(group);
        serviceDetailDTO.setService(interfaze);
        serviceDetailDTO.setVersion(version);
        //非线上环境
        if(!"pro".equals(System.getProperty("spring.profiles.active"))){
            serviceDetailDTO.setDebugUrl("https://one.mi.com/miapi/#/test?projectID=120696&apiProtocol=2&serviceName=providers:"
                    +interfaze+":"
                    +(StringUtils.isBlank(version) ? "" :version)+":"
                    +(StringUtils.isBlank(group) ? "": group));
        }
        if (providers != null && providers.size() > 0) {
            application = providers.get(0).getApplication();
            serviceDetailDTO.setDubboVersion(InstanceUtils.getDubboVersion(providers.get(0)));
        }
        serviceDetailDTO.setIsOwn(UserInfoThreadLocal.getUserInfo().getApplicationNames().contains(application) ? true : false);
        MetadataIdentifier identifier = new MetadataIdentifier(interfaze, version, group,Constants.PROVIDER_SIDE, application);
        String metadata = providerService.getProviderMetaData(identifier);

        if (metadata != null) {
            try {
                // for dubbo version under 2.7, this metadata will represent as IP address, like ********.
                // So the json conversion will fail.
                JSONObject jsonObject = JSON.parseObject(metadata);
                JSONArray methods = jsonObject.getJSONArray("methods");
                if(CollectionUtils.isNotEmpty(methods)){
                    for (Object method : methods) {
                        JSONObject methodsObject = (JSONObject) method;
                        methodsObject.remove("annotations");
                    }
                }
                FullServiceDefinition serviceDefinition = JSON.parseObject(JSON.toJSONString(jsonObject), FullServiceDefinition.class);
                serviceDetailDTO.setMetadata(serviceDefinition);
            } catch (JsonParseException e) {
                throw new VersionValidationException("dubbo 2.6 does not support metadata");
            }
        }else if(CollectionUtils.isNotEmpty(providers)){
            String parameters = providers.get(0).getParameters();
            Map<String,String> map ;
            if(namingInstanceService.getIsQueryNaming4DB()){
                map = gson.fromJson(parameters,Map.class);

            }else{
                map = covertParamToMap(parameters);
            }
            FullServiceDefinition fullServiceDefinition = new FullServiceDefinition();
            fullServiceDefinition.setParameters(map);
            if(MapUtils.isNotEmpty(map)){
                String methods = map.get("methods");
                List<MethodDefinition> methodList = new ArrayList<>();
                if(StringUtils.isNotBlank(methods)){
                    String[] split = methods.split(",");
                    for (String method : split) {
                        MethodDefinition methodDefinition = new MethodDefinition();
                        methodDefinition.setName(method);
                        methodList.add(methodDefinition);
                    }
                }
                fullServiceDefinition.setMethods(methodList);
            }
            serviceDetailDTO.setMetadata(fullServiceDefinition);
        }
        try{
            List<UserInfoResult> userList = accountServiceRpc.queryUsersByAppName(application);
            if(CollectionUtils.isNotEmpty(userList)){
                serviceDetailDTO.setDevelopers(userList.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
                String uid = userService.findUidByUserName(userList.get(0).getUserName());
                serviceDetailDTO.setDepartment(userService.findDepartmentFullName(uid));
            }
        }catch (Exception e){
            log.error("查询开发人员或部门信息时报错，e=",e);
        }
        return serviceDetailDTO;
    }

    @RequestMapping(value = "/service/{service}", method = RequestMethod.GET)
    public ServiceDetailDTO serviceDetail(@PathVariable String service) {
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        String group = Tool.getGroup(service);
        String version = Tool.getVersion(service);
        String interfaze = Tool.getInterface(service);
        List<Provider> providers;
        List<Consumer> consumers;
        if (namingInstanceService.getIsQueryNaming4DB()) {
            List<NamingInstance> consumserNamingInstances = namingInstanceService.findPageByFullService(Constants.PROVIDER_SIDE, service, 1, 1);
            providers = consumserNamingInstances.stream().map(instance -> InstanceCovert.namingInstanceCovertToProvider(instance)).collect(Collectors.toList());
            List<NamingInstance> providerNamingInstances = namingInstanceService.findPageByFullService(Constants.CONSUMER_SIDE, service, 1, 1);
            consumers = providerNamingInstances.stream().map(instance -> InstanceCovert.namingInstanceCovertToConsumer(instance)).collect(Collectors.toList());

        } else {
            providers = providerService.findByService(service);
            consumers = consumerService.findByService(service);
        }

        String application = null;
        if (providers != null && providers.size() > 0) {
            application = providers.get(0).getApplication();
        }
        MetadataIdentifier identifier = new MetadataIdentifier(interfaze, version, group, Constants.PROVIDER_SIDE, application);
        ServiceDetailDTO serviceDetailDTO = new ServiceDetailDTO();
        serviceDetailDTO.setConsumers(consumers);
        serviceDetailDTO.setProviders(providers);
        serviceDetailDTO.setIsOwn(UserInfoThreadLocal.getUserInfo().getApplicationNames().contains(application) ? true : false);
        String metadata = providerService.getProviderMetaData(identifier);
        if (metadata != null) {
            try {
                // for dubbo version under 2.7, this metadata will represent as IP address, like ********.
                // So the json conversion will fail.
                JSONObject jsonObject = JSON.parseObject(metadata);
                JSONArray methods = jsonObject.getJSONArray("methods");
                if (CollectionUtils.isNotEmpty(methods)) {
                    for (Object method : methods) {
                        JSONObject methodObject = (JSONObject) method;
                        methodObject.remove("annotations");
                    }
                }
                FullServiceDefinition serviceDefinition = JSON.parseObject(JSON.toJSONString(jsonObject), FullServiceDefinition.class);
                serviceDetailDTO.setMetadata(serviceDefinition);
            } catch (JsonParseException e) {
                throw new VersionValidationException("dubbo 2.6 does not support metadata");
            }
        } else if (CollectionUtils.isNotEmpty(providers)) {
            String parameters = providers.get(0).getParameters();
            Map<String, String> map;
            if (namingInstanceService.getIsQueryNaming4DB()) {
                map = gson.fromJson(parameters, Map.class);
                serviceDetailDTO.setDubboVersion(InstanceUtils.getDubboVersion(map));
            } else {
                map = covertParamToMap(parameters);
                serviceDetailDTO.setDubboVersion(InstanceUtils.getDubboVersion(providers.get(0)));
            }
            FullServiceDefinition fullServiceDefinition = new FullServiceDefinition();
            fullServiceDefinition.setParameters(map);
            serviceDetailDTO.setMetadata(fullServiceDefinition);
        }

        serviceDetailDTO.setConsumers(consumers);
        serviceDetailDTO.setProviders(providers);
        serviceDetailDTO.setService(service);
        serviceDetailDTO.setApplication(application);
        try {
            List<UserInfoResult> userList = accountServiceRpc.queryUsersByAppName(application);
            if (CollectionUtils.isNotEmpty(userList)) {
                serviceDetailDTO.setDevelopers(userList.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
                String uid = userService.findUidByUserName(userList.get(0).getUserName());
                serviceDetailDTO.setDepartment(userService.findDepartmentFullName(uid));
            }
            for (Consumer consumer : consumers) {
                List<UserInfoResult> consumerUsers = accountServiceRpc.queryUsersByAppName(consumer.getApplication());
                if (CollectionUtils.isNotEmpty(consumerUsers)) {
                    consumer.setDevelopers(consumerUsers.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
                }
            }
        } catch (Exception e) {
            log.error("查询开发人员或部门信息时报错，e=", e);
        }
        return serviceDetailDTO;
    }


    private HashMap<String, String> covertParamToMap(String parameters) {
        HashMap<String, String> map = new HashMap<>();
        if (StringUtils.isBlank(parameters)) {
            return map;
        }
        String[] split = parameters.split("&");
        for (String sp : split) {
            String[] split1 = sp.split("=");
            if (split1.length == 2) {
                map.put(split1[0], split1[1]);
            }
        }
        return map;
    }

    @RequestMapping(value = "/services", method = RequestMethod.GET)
    public Set<String> allServices() {
        if (namingInstanceService.getIsQueryNaming4DB()) {
            return new HashSet<>(namingInstanceService.findServiceNames(Constants.PROVIDER_SIDE));
        } else {
            return new HashSet<>(providerService.findServices());
        }

    }

    @RequestMapping(value = "/applications", method = RequestMethod.GET)
    public Set<String> allApplications() {
        if (namingInstanceService.getIsQueryNaming4DB()) {
            return namingInstanceService.findApplicationNames(Constants.PROVIDER_SIDE);
        }
        return providerService.findApplications();
    }

    @RequestMapping(value = "/consumers", method = RequestMethod.GET)
    public Set<String> allConsumers() {
        if (namingInstanceService.getIsQueryNaming4DB()) {
            return namingInstanceService.findApplicationNames(Constants.CONSUMER_SIDE);
        }
        List<Consumer> consumers = consumerService.findAll();
        return consumers.stream().map(Consumer::getApplication).collect(Collectors.toSet());
    }

    @RequestMapping(value = "/projects", method = RequestMethod.GET)
    public ResultResponse<List<String>> projects() {
        return ResultResponse.success(UserInfoThreadLocal.getUserInfo().getApplicationNames());
    }

    @RequestMapping(value = "/projectEnvIdList", method = RequestMethod.GET)
    public ResultResponse<List<ProjectEnvDTO>> projectEnvIdList(@RequestParam String applicationName, @RequestParam(required = false) Integer projectId) {
        if (projectId == null) {
            Project project = UserInfoThreadLocal.getUserInfo().getApplicationMap().get(applicationName);
            if (project == null) {
                if (UserInfoThreadLocal.getUserInfo().isAdmin()) {
                    ProjectBo projectBo = iProjectServiceRpc.getProjectByName(applicationName);
                    projectId = projectBo.getId();
                } else {
                    throw new VersionValidationException("没有" + applicationName + "查询权限");
                }
            } else {
                projectId = project.getId().intValue();
            }
        }

        return ResultResponse.success(iProjectServiceRpc.listProjectEnvByProject(projectId));
    }

    @ApiOperation("接口服务治理数据汇总")
    @RequestMapping(value = "/service/{service}/collectGovern", method = RequestMethod.GET)
    public ResultResponse<GovernDataDTO> collectGovern(HttpServletRequest request,
                                                       @RequestParam String application,
                                                       @RequestParam(required = false) String serviceVersion,
                                                       @RequestParam(required = false) String serviceGroup,
                                                       @RequestParam String service) {
        GovernDataDTO governDataDTO = new GovernDataDTO();

/*        CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
            Map<String, String> params = new HashMap<>();
            params.put("app",application);
            params.put("ip","0.0.0.0");
            params.put("port","80");
            int size = 0;
            ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request,"degrade/rules.json", RequestMethod.GET.name(), null,params);
            if(response.getData() != null){
                List<DegradeRuleEntity> degradeRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<DegradeRuleEntity>>() {});
                if(CollectionUtils.isNotEmpty(degradeRuleEntities)){
                    size = degradeRuleEntities.stream().filter(entity->entity.getResource().equals(service)).collect(Collectors.toList()).size();
                }
            }
            governDataDTO.setDegradeCount(size);
        });*/
        CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
            Map<String, String> params = new HashMap<>();
            params.put("app", application);
            int size = 0;
            ResultResponse<Object> response = HttpClient.sentinelProxyHttp(request, "v2/flow/rules", RequestMethod.GET.name(), null, params);
            if (response.getData() != null) {
                List<FlowRuleEntity> flowRuleEntities = JSON.parseObject(response.getData().toString(), new TypeReference<List<FlowRuleEntity>>() {
                });
                if (CollectionUtils.isNotEmpty(flowRuleEntities)) {
                    Map<Integer, List<FlowRuleEntity>> collect = flowRuleEntities.stream().filter(entity -> entity.getResource().startsWith(service)).collect(Collectors.groupingBy(FlowRuleEntity::getGrade));
                    size = collect.size();
                }
            }
            governDataDTO.setFlowCount(size);
        });
/*        CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
            TagRouteDTO tagRoute = routeService.findTagRoute(application);
            governDataDTO.setTagRouteCount(tagRoute != null && tagRoute.getTags() != null? tagRoute.getTags().size():0);
        });*/
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        CompletableFuture<Void> future4 = CompletableFuture.runAsync(() -> {
            HashMap<String, String> params = new HashMap<>();
            params.put("serviceId", application);
            params.put("status", "2");
            ResultResponse<String> response = HttpClient.iauthProxyHttp(request, "/api/whitelist/service", RequestMethod.GET, params, null, userInfo);
            int serviceAuthCount = 0;
            if (response.isSuccess()) {
                List<SwappedWhiteInfo> swappedWhiteInfos = JSON.parseObject(response.getData(), new TypeReference<List<SwappedWhiteInfo>>() {
                });
                if (CollectionUtils.isNotEmpty(swappedWhiteInfos)) {
                    for (SwappedWhiteInfo swappedWhiteInfo : swappedWhiteInfos) {
                        if (service.equals(swappedWhiteInfo.getSubService()) || "*".equals(swappedWhiteInfo.getSubService())) {
                            serviceAuthCount++;
                        }
                    }
                }
            }
            governDataDTO.setServiceAuthCount(serviceAuthCount);
        });
        CompletableFuture<Void> future5 = CompletableFuture.runAsync(() -> {
            DynamicConfigDTO configDTO = new DynamicConfigDTO();
            configDTO.setService(service);
            configDTO.setServiceVersion(serviceVersion);
            configDTO.setServiceGroup(serviceGroup);
            String id = ConvertUtil.getIdFromDTO(configDTO);
            DynamicConfigDTO override = overrideService.findOverride(id);
            governDataDTO.setOverrideCount(override != null && override.getConfigs() != null ? override.getConfigs().size() : 0);
        });
        try {
            CompletableFuture.allOf(future2, future4, future5).get();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询collectGovern时报错，e=", e);
        }
        return ResultResponse.success(governDataDTO);
    }
/*    @ApiOperation("根据consumer application查询服务列表")
    @RequestMapping(value = "/service/{service}/collectGovern", method = RequestMethod.GET)
    public ResultResponse<GovernDataDTO> collectGovern() {
        GovernDataDTO governDataDTO = new GovernDataDTO();
        return ResultResponse.success(governDataDTO);
    }*/

    @RequestMapping(value = "/service/{service}/methods", method = RequestMethod.GET)
    public ResultResponse<List<String>> queryMethods(@PathVariable String service) {
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        List<Provider> providers;
        if(namingInstanceService.getIsQueryNaming4DB()){
            List<NamingInstance> namingInstances = namingInstanceService.findPageByFullService(Constants.PROVIDER_SIDE, service,1,1);
            providers = namingInstances.stream().map(instance -> InstanceCovert.namingInstanceCovertToProvider(instance)).collect(Collectors.toList());
        }else{
            providers = providerService.findByService(service);
        }
        if(CollectionUtils.isEmpty(providers)){
            return ResultResponse.success(null);
        }
        String parameters = providers.get(0).getParameters();

        Map<String,String> map ;
        if(namingInstanceService.getIsQueryNaming4DB()){
            map = gson.fromJson(parameters,Map.class);
        }else{
            map = covertParamToMap(parameters);
        }
        List<String> methodList = new ArrayList<>();
        if(MapUtils.isNotEmpty(map)){
            String methods = map.get("methods");
            if(StringUtils.isNotBlank(methods)){
                String[] split = methods.split(",");
                methodList.addAll(Lists.list(split));
            }
        }
        return ResultResponse.success(methodList);
    }


    @ApiOperation("手动触发查询")
    @RequestMapping(value = "/service/{service}/queryByHand", method = RequestMethod.GET)
    public ResultResponse<Map<String, Object>> queryByHand() {
        return ResultResponse.success(statisticsService.statisticsProject());
    }

    @Scheduled(cron = "0 0 23 * * *")
    public void project() {
        statisticsService.statisticsProject();
    }
}
