package com.xiaomi.dayu.controller.schedule;

import com.xiaomi.dayu.rpc.FunctionServiceRpc;
import com.xiaomi.dayu.rpc.MoonProjectDubboServiceRpc;
import com.xiaomi.mone.faas.api.bo.FaasFuncBO;
import com.xiaomi.mone.faas.api.bo.FaasFuncRequest;
import com.xiaomi.mone.faas.api.bo.Pager;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import run.mone.moon.api.bo.common.Result;
import run.mone.moon.api.bo.project.ProjectDto;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/api")
public class FunctionController {
    @Autowired
    private MoonMoneTpcContextUtil moonMoneTpcContextUtil;
    @Autowired
    public FunctionServiceRpc functionServiceRpc;
    @Autowired
    public MoonProjectDubboServiceRpc moonProjectDubboServiceRpc;

    @HttpApiDoc(apiName = "函数详情", value = "/api/moon/project/function/detail", method = MiApiRequestMethod.POST, description = "函数详情")
    @RequestMapping(value = "/function/detail", method = RequestMethod.POST, consumes = {"application/json"})
    public Result<FaasFuncBO> detailFaasFunction(HttpServletRequest request, @RequestBody FaasFuncRequest param) {
        return functionServiceRpc.readFaasFunction(moonMoneTpcContextUtil.getMoonMoneTpcContext(), param);
    }

    @HttpApiDoc(apiName = "判断函数是否可以创建任务", value = "/api/moon/project/function/canCreateTask", method = MiApiRequestMethod.POST, description = "判断函数是否可以创建任务")
    @RequestMapping(value = "/function/canCreateTask", method = RequestMethod.POST, consumes = {"application/json"})
    public Result<Boolean> canFaasFunctionToTask(HttpServletRequest request, @RequestBody FaasFuncRequest param) {

        return functionServiceRpc.canFaasFunctionToTask(moonMoneTpcContextUtil.getMoonMoneTpcContext(), param);
    }
    @HttpApiDoc(apiName = "获取函数列表", value = "/api/moon/project/function/list", method = MiApiRequestMethod.POST, description = "获取函数列表")
    @RequestMapping(value = "/function/list", method = RequestMethod.POST, consumes = {"application/json"})
    public Result<Pager<FaasFuncBO>> listFaasFunction(HttpServletRequest request, @RequestBody FaasFuncRequest param) {
        String moonEnv = request.getHeader("moonEnv");
        return functionServiceRpc.listFaasFunction(moonMoneTpcContextUtil.getMoonMoneTpcContext(), param);
    }
    @HttpApiDoc(apiName = "项目通知组", value = "/api/moon/project/detail", method = MiApiRequestMethod.GET, description = "项目详情")
    @RequestMapping(value = "/project/detail", method = RequestMethod.GET)
    public Result<ProjectDto> detail(HttpServletRequest request,
                             @RequestParam("id") long id) {
        return moonProjectDubboServiceRpc.projectDetail(moonMoneTpcContextUtil.getMoonMoneTpcContext(), id);
    }
}
