package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.bo.request.ChangeArthasMemberReq;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.ProjectFacadeRpc;
import com.xiaomi.dayu.service.InstrumentService;
import com.xiaomi.mone.tpc.api.service.UserFacade;
import com.xiaomi.mone.tpc.common.vo.NodeUserRelVo;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import com.xiaomi.youpin.hermes.bo.response.CheckAccessResp;
import com.xiaomi.youpin.hermes.entity.User;
import com.xiaomi.youpin.hermes.enums.PrivilegeEnum;
import com.xiaomi.youpin.hermes.enums.ResourceTypeEnum;
import com.xiaomi.youpin.hermes.service.AccessControlService;
import com.xiaomi.youpin.infra.rpc.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkArgument;
import static com.xiaomi.youpin.hermes.consts.ResourceConsts.RESOURCE_SCOPE_APPLICATION;

/**
 * <AUTHOR> Yang (<EMAIL>)
 * @version 1.0
 * @since 2022/4/8
 */
@RestController
@RequestMapping("/api/app/arthas")
@Validated
public class ArthasController {

    @Autowired
    private InstrumentService instrumentService;

    @Autowired
    private AccountServiceRpc accountService;

    private final ResourceTypeEnum resourceType = ResourceTypeEnum.ARTHAS;

    private final PrivilegeEnum minPrivilege = PrivilegeEnum.READ;

    private final int RoleType_Tester = 2;

    @DubboReference(check = false,interfaceClass = AccessControlService.class, retries = 1, group = "${rpc.hermes.AccountService.group}", tag = RESOURCE_SCOPE_APPLICATION ,timeout = 3000)
    private AccessControlService accessControlService;
    @Resource
    private ProjectFacadeRpc projectFacadeRpc;

    @RequestMapping(value = "/agents", method = RequestMethod.GET)
    public Result<Object> listAgents(HttpServletRequest request,
                                     @RequestParam String appName,
                                     @RequestParam Integer appId) {
        Map<String, Object> map = new HashMap<>();
        String projectId = this.pidFromAppNameAndId(appId, appName);
//        CheckAccessResp resp = this.accessControlService.checkAccess(this.resourceType, appName,
//                UserInfoThreadLocal.getUserInfo().getUserName(), this.minPrivilege.getValue());
        List<NodeUserRelVo> projectMembers = projectFacadeRpc.getProjectMembers(Lists.list(new Long(appId)));
        List<UserInfoResult> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(projectMembers)){
            for (NodeUserRelVo projectMember : projectMembers) {
                UserInfoResult userInfoResult = new UserInfoResult();
                userInfoResult.setUserName(projectMember.getAccount());
                userInfoResult.setName(projectMember.getAccount());
                list.add(userInfoResult);
            }
        }
//        int role = resp.getRole();
        int role = 0;
        map.put("role", role);
        map.put("reviewers", list);

        List<Object> agents = this.instrumentService.listAgents(projectId);
        map.put("agents", agents);
        return Result.success(map);
    }

    private String pidFromAppNameAndId(Integer appId, String appName) {
        return appId + "-" + appName;
    }

    @RequestMapping(value = "/members", method = RequestMethod.GET)
    public Result<Object> listMembers(HttpServletRequest request,
                                      @RequestParam(required = true) String appName,
                                      @RequestParam(required = true) Long projectId) {
        Map<String, Object> dict = new HashMap<>();
//        List<User> members = this.accessControlService.members(this.resourceType, appName,
//                this.minPrivilege.getValue());
        List<NodeUserRelVo> projectMembers = projectFacadeRpc.getProjectMembers(Lists.list(new Long(projectId)));
        List<UserInfoResult> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(projectMembers)){
            for (NodeUserRelVo projectMember : projectMembers) {
                UserInfoResult userInfoResult = new UserInfoResult();
                userInfoResult.setUserName(projectMember.getAccount());
                userInfoResult.setName(projectMember.getAccount());
                list.add(userInfoResult);
            }
        }
        dict.put("members", list);

//        List<UserInfoResult> reviewers = this.accountService.queryUsersInfoByAppNameAndRole(appName,projectId, this.RoleType_Tester);
        dict.put("reviewers", list);
        return Result.success(dict);
    }

    @RequestMapping(value = "/members", method = RequestMethod.POST)
    public Result<Object> changeMembers(HttpServletRequest request,
                                        @RequestBody @Valid ChangeArthasMemberReq reqBody) {
        checkArgument(reqBody.getPrivilege() == PrivilegeEnum.NONE.getValue() || reqBody.getPrivilege() == PrivilegeEnum.READ.getValue(),
                "ArthasController changeMembers invalid privilege " + reqBody.getPrivilege());
//        boolean b = this.accessControlService.changeUserPermission(reqBody.getAssignees(), this.resourceType,
//                reqBody.getAppName(), reqBody.getPrivilege(), UserInfoThreadLocal.getUserInfo().getUserName());
        return Result.success(false);
    }
}
