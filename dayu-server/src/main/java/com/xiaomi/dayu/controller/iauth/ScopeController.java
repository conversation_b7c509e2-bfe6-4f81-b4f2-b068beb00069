package com.xiaomi.dayu.controller.iauth;

import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
@Deprecated
@Api("方法级别")
@RestController
@RequestMapping("/api/iauth/api/service")
public class ScopeController {

    /**
     * 显示Scope详情
     *
     */
    @GetMapping("/{serviceId}/{subServiceId}/scope/{scopeId}")
    public ResultResponse<String> getIAuthScope(HttpServletRequest request,
                                                @PathVariable("serviceId") String serviceId,
                                                @PathVariable("subServiceId") String subServiceId,
                                                @PathVariable("scopeId") int scopeId,
                                                @RequestParam(value = "platform",required = false) String platform) {
        return HttpClient.iauthProxyHttp(request);
    }

    /**
     * 修改Scope信息
     *
     */
    @PostMapping("/{serviceId}/{subServiceId}/scope/{scopeId}")
    public ResultResponse<String> modifyInfo(HttpServletRequest request,
                                             @PathVariable("serviceId") String serviceId,
                                             @PathVariable("subServiceId") String subServiceId,
                                             @ApiParam(value = "int类型，scope标识") @PathVariable("scopeId") int scopeId,
                                             @RequestParam(value = "platform",required = false) String platform) {
        return HttpClient.iauthProxyHttp(request);
    }

    /**
     * 创建Scope
     *
     */
    @PostMapping("/{serviceId}/{subServiceId}/scope")
    public ResultResponse<String> addScope(HttpServletRequest request,
                                           @PathVariable("serviceId") String serviceId,
                                           @PathVariable("subServiceId") String subServiceId,
                                           @RequestParam(value = "platform",required = false) String platform,
                                           @ApiParam(value = "int类型，scope标识")@RequestParam("id") String id,
                                           @ApiParam(value = "对应serviceId") @RequestParam("sid") String sid,
                                           @ApiParam(value = "对应subServiceId") @RequestParam("ssid") String ssid,
                                           @ApiParam(value = "方法名")@RequestParam("method") String method
                                           ) {
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        return HttpClient.iauthProxyHttp(request,bodyParams);
    }

    /**
     * 删除Scope
     *
     */
    @DeleteMapping("/{serviceId}/{subServiceId}/scope")
    public ResultResponse<String> delete(HttpServletRequest request,
                                         @PathVariable("serviceId") String serviceId,
                                         @PathVariable("subServiceId") String subServiceId,
                                         @ApiParam(value = "int类型，scope标识")@RequestParam("id") int scopeId,
                                         @RequestParam(value = "platform",required = false) String platform) {
        return HttpClient.iauthProxyHttp(request);
    }
}
