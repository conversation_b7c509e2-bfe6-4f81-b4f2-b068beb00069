package com.xiaomi.dayu.controller.servicemanage;

import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.VersionValidationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.InstanceUtils;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.common.util.Tool;
import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.dto.ConsumerServiceDTO;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.service.ConsumerService;
import com.xiaomi.dayu.service.NamingInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.metadata.definition.model.FullServiceDefinition;
import org.apache.dubbo.metadata.report.identifier.MetadataIdentifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
@Api(description = "查询consumer调用的service")
@RestController
@RequestMapping("/api/consumer")
public class ConsumerController {
    @Autowired
    private ConsumerService consumerService;
    @Autowired
    private NamingInstanceService namingInstanceService ;


    private static Gson gson = new Gson();

    @RequestMapping(value = "/consumers", method = RequestMethod.GET)
    public Set<String> allConsumers() {
        if(namingInstanceService.getIsQueryNaming4DB()){
            return  namingInstanceService.findServiceNames(Constants.CONSUMER_SIDE);
        }
        return new HashSet<>(consumerService.findConsumers());
    }

    @RequestMapping(value = "/service", method = RequestMethod.GET)
    public ResultResponse<PageResult<ConsumerServiceDTO>> searchConsumerService(@ApiParam(value = "service:服务名，application：应用，ip：ip") @RequestParam String pattern,
                                                                @ApiParam(value = "查询值") @RequestParam String filter,

                                                                @ApiParam(value = "false:查所有，true：查我参与") @RequestParam(required = false) boolean self,
                                                                @RequestParam(required = false,defaultValue = "10") int pageSize,
                                                                @RequestParam(required = false,defaultValue = "1") int pageNum,
                                                                @ApiParam(value = "搜索页使用时传 true") @RequestParam(required = false,defaultValue = "true") boolean aggregate
                                                                                ) {
        if(namingInstanceService.getIsQueryNaming4DB()){
            return  namingInstanceService.getConsumerServiceDTOS(pattern, filter, self,pageNum,pageSize,aggregate);
        }else{
            final Set<ConsumerServiceDTO> serviceDTOS = consumerService.getServiceDTOS(pattern, filter, self, aggregate);
            final List<ConsumerServiceDTO> content =
                    serviceDTOS.stream()
                            .skip((pageNum-1)*pageSize)
                            .limit(pageSize)
                            .collect(Collectors.toList());
            consumerService.fillExtendInfo(content);
            return ResultResponse.success(new PageResult<ConsumerServiceDTO>(content, serviceDTOS.size(),pageSize,pageNum));
        }
    }


    @RequestMapping(value = "/{service}/metedata", method = RequestMethod.GET)
    public ResultResponse<Map<String,String>> metedata(@PathVariable String service ,
                                                       @RequestParam String application ,
                                                       @RequestParam @ApiParam(value = "provider或consumer") String side){
        service = service.replace(Constants.ANY_VALUE, Constants.PATH_SEPARATOR);
        String group = Tool.getGroup(service);
        String version = Tool.getVersion(service);
        String interfaze = Tool.getInterface(service);
        if(!Constants.PROVIDER_SIDE.equals(side) && !Constants.CONSUMER_SIDE.equals(side)){
            throw new VersionValidationException("side参数错误，必须为provider或consumer");
        }
        MetadataIdentifier identifier = new MetadataIdentifier(interfaze, version, group,side, application);
        String metadata = consumerService.getConsumerMetaData(identifier);
        Map<String, String> parameters = new HashMap<>();
        if (metadata != null) {
            try {
                // for dubbo version under 2.7, this metadata will represent as IP address, like ********.
                // So the json conversion will fail.
                FullServiceDefinition serviceDefinition = gson.fromJson(metadata, FullServiceDefinition.class);
                parameters = serviceDefinition.getParameters();
            } catch (JsonParseException e) {
                // throw new VersionValidationException("dubbo 2.6 does not support metadata");
                parameters =  gson.fromJson(metadata,Map.class);
            }
        }else{
            if(namingInstanceService.getIsQueryNaming4DB()){
                List<NamingInstance> namingInstances = namingInstanceService.findPageByService(Constants.CONSUMER_SIDE, service,application, null, 1, 100);
                if(CollectionUtils.isNotEmpty(namingInstances)){
                    parameters = InstanceUtils.getParams(namingInstances.get(0).getMetadata());
                }
            }else{
                List<Consumer> consumerList = consumerService.findByService(service);
                if(CollectionUtils.isNotEmpty(consumerList)){
                    List<Consumer> consumers = consumerList.stream().filter(consumer -> application.equals(consumer.getApplication())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(consumers)){
                        parameters = InstanceUtils.getParams(consumers.get(0).getParameters());
                    }
                }

            }

        }
        return ResultResponse.success(parameters);
    }
}
