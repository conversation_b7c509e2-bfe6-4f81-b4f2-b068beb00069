package com.xiaomi.dayu.controller.threadpool;

import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import com.xiaomi.data.push.nacos.NacosConfig;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.dao.ThreadpoolConfigHistoryMapper;
import com.xiaomi.dayu.dao.ThreadpoolConfigMapper;
import com.xiaomi.dayu.model.dto.ThreadpoolDTO;
import com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig;
import com.xiaomi.dayu.mybatis.entity.ThreadpoolConfigHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/17 22:40
 */
@Slf4j
@RestController
@RequestMapping("/api/heratbeat/threadpool")
public class HeartbeatThreadController {

    @Autowired
    private ThreadpoolConfigMapper threadpoolConfigMapper;

    @Autowired
    private ThreadpoolConfigHistoryMapper threadpoolConfigHistoryMapper;

    @Autowired
    private NacosConfig nacosConfig;

    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public ResultResponse<Boolean> register(@RequestBody List<ThreadpoolDTO> threadpoolDTOList) {
        if (CollectionUtils.isEmpty(threadpoolDTOList)) {
            return ResultResponse.success(true);
        }
        for (ThreadpoolDTO threadpoolDTO : threadpoolDTOList) {
            List<ThreadpoolConfig> li = threadpoolConfigMapper.queryConfigsByAppAndPool(threadpoolDTO.getAppName(), threadpoolDTO.getPoolName());
            if (!CollectionUtils.isEmpty(li)) {
                log.warn("threadpool has exist, threadPool:{}", threadpoolDTO.toString());
                continue;
            }
            ThreadpoolConfig conf = convertData(threadpoolDTO);
            threadpoolConfigMapper.insertConfig(conf);
            String dataId = threadpoolDTO.getAppName() + "_" + threadpoolDTO.getPoolName();
            Gson gson = new Gson();
            String content = gson.toJson(threadpoolDTO);
            try {
                nacosConfig.publishConfig(dataId, "DEFAULT_GROUP", content);
            } catch (NacosException e) {
                log.error("heratbeat/threadpool/register error, dataId:{}, content:{}", dataId, content, e);
                return ResultResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
            }

            threadpoolConfigHistoryMapper.insertHistory(
                    new ThreadpoolConfigHistory(
                            conf.getId(),
                            "system",
                            "create",
                            content,
                            dataId
                    )
            );
        }
        return ResultResponse.success(true);
    }

    private ThreadpoolConfig convertData(ThreadpoolDTO threadpoolDTO) {
        ThreadpoolConfig conf = new ThreadpoolConfig();
        conf.setId(threadpoolDTO.getId());
        conf.setCorePoolSize(threadpoolDTO.getCorePoolSize());
        conf.setAppName(threadpoolDTO.getAppName());
        conf.setCapacity(threadpoolDTO.getCapacity());
        conf.setPoolName(threadpoolDTO.getPoolName());
        conf.setMaximumPoolSize(threadpoolDTO.getMaximumPoolSize());
        conf.setReject(threadpoolDTO.getReject());
        conf.setKeepAliveTime(threadpoolDTO.getKeepAliveTime());
        return conf;
    }
}
