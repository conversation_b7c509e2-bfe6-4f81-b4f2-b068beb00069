package com.xiaomi.dayu.controller.servicemanage;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.service.StatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

@Slf4j
@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {
    @Autowired
    private StatisticsService statisticsService;
    @RequestMapping(value = "/queryUserView", method = RequestMethod.GET)
    public ResultResponse<Map<String,Set<String>>> queryUserView(@RequestParam String date, @RequestParam(required = false) int num) {
            LocalDate parse = LocalDate.parse(date);
            TreeMap<String,Set<String>> map = new TreeMap<>();
            for (int i =0 ;i<num;i++){
                String format = date;
                if(i>0){
                    LocalDate localDate = parse.plusDays(i);
                    format = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(localDate);
                }
                map.put(format,statisticsService.queryUserView(format));
            }
            return ResultResponse.success(map);
    }
}
