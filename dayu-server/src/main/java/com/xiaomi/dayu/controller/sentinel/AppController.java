package com.xiaomi.dayu.controller.sentinel;

import com.xiaomi.dayu.common.HttpClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api/app")
public class AppController {


    @GetMapping("/names.json")
    public Object queryApps(HttpServletRequest request) {
        return HttpClient.sentinelProxyHttp(request);
    }

    @GetMapping("/briefinfos.json")
    public Object queryAppInfos(HttpServletRequest request) {
        return HttpClient.sentinelProxyHttp(request);
    }

    @GetMapping(value = "/{app}/machines.json")
    public Object getMachinesByApp(HttpServletRequest request,@PathVariable("app") String app) {
        return HttpClient.sentinelProxyHttp(request);
    }
}
