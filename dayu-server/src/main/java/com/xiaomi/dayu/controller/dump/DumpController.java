package com.xiaomi.dayu.controller.dump;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xiaomi.dayu.common.HttpClientUtils;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.service.ConfigParams;
import com.xiaomi.dayu.model.dump.DownloadInput;
import com.xiaomi.dayu.model.dump.DumpInput;
import com.xiaomi.dayu.model.dump.PipelineDeployVO;
import com.xiaomi.dayu.rpc.OpenApiServiceRpc;
import com.xiaomi.mone.miline.api.bo.common.PipelineTypeEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/dump")
public class DumpController {

    @Value("${dump.project.url}")
    private String dumpProjectUrl;

    @Resource
    private OpenApiServiceRpc openApiServiceRpc;

    @GetMapping("/deploylist")
    public ResultResponse<Map<Integer,List<PipelineDeployVO>>> qryProjectContainerInfo(
            @RequestParam String app,
            @RequestParam long projectId,
            @RequestParam(required = false) String milineEnv){
        CheckPermissUtil.checkPermissByAppName(app);
        return ResultResponse.success(openApiServiceRpc.qryProjectContainerInfo(projectId,
                Lists.newArrayList(PipelineTypeEnum.K8S.getCode(),PipelineTypeEnum.MONE_DEPLOY.getCode()),
                StringUtils.isNotBlank(milineEnv)? milineEnv:ConfigParams.getMilineEnv().getName()));
    }
    @SneakyThrows
    @PostMapping("/createDump")
    public ResultResponse<Object> createDump(@RequestBody DumpInput dumpInput){
        CheckPermissUtil.checkPermissByAppName(dumpInput.getApp());
        dumpInput.setUserName(UserInfoThreadLocal.getUserInfo().getUserName());
        JSONObject jsonObject = HttpClientUtils.doPost(dumpProjectUrl + "dump/createDump", JSONObject.parseObject(JSON.toJSONString(dumpInput)));
        return ResultResponse.success(jsonObject);
    }
    @SneakyThrows
    @PostMapping("/download")
    public ResultResponse<Object> download(@RequestBody DownloadInput downloadInput){
        CheckPermissUtil.checkPermissByAppName(downloadInput.getApp());
        downloadInput.setUserName(UserInfoThreadLocal.getUserInfo().getUserName());
        JSONObject jsonObject = HttpClientUtils.doPost(dumpProjectUrl + "dump/download", JSONObject.parseObject(JSON.toJSONString(downloadInput)));
        return ResultResponse.success(jsonObject);
    }
    @GetMapping("/createDockerDump")
    public ResultResponse<Boolean> createDockerDump(@RequestParam String appName,
                                                    @RequestParam Integer pipelineId,
                                                   @RequestParam String ip,
                                                   @RequestParam String containerName){
        CheckPermissUtil.checkPermissByAppName(appName);
        return ResultResponse.success(openApiServiceRpc.dumpJvmProcess(pipelineId,ip,containerName, UserInfoThreadLocal.getUserInfo().getUserName(),ConfigParams.getMilineEnv().getName()));
    }
}
