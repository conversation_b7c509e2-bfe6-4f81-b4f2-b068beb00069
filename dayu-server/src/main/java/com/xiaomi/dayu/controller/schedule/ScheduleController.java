package com.xiaomi.dayu.controller.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.schedule.task.TaskDefBean;
import com.xiaomi.data.push.schedule.task.TaskParam;
import com.xiaomi.dayu.bo.request.*;
import com.xiaomi.dayu.bo.response.ListTaskResponse;
import com.xiaomi.dayu.bo.response.TaskBo;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.model.requests.response.DubboService;
import com.xiaomi.dayu.model.requests.response.TaskContentVo;
import com.xiaomi.dayu.rpc.AlertGorupFacadeRpc;
import com.xiaomi.dayu.service.ScheduleService;
import com.xiaomi.dayu.service.impl.LoginService;
import com.xiaomi.dayu.utils.ScheduleExcecute;
import com.xiaomi.dayu.utils.TaskProxy;
import com.xiaomi.mone.monitor.service.bo.AlertGroupQryInfo;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.mischedule.MethodInfo;
import com.xiaomi.youpin.mischedule.STaskDef;
import com.xiaomi.youpin.mischedule.api.service.bo.PageInfo;
import com.xiaomi.youpin.mischedule.api.service.bo.TaskDo;
import com.xiaomi.youpin.mischedule.api.service.bo.TaskHistoryBo;
import com.xiaomi.youpin.mischedule.api.service.bo.TaskQryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/api/schedule")
public class ScheduleController {

    @Autowired
    private ScheduleExcecute scheduleExcecute;

    @Autowired
    private TaskProxy taskProxy;

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private AlertGorupFacadeRpc alertGorupFacadeRpc;

    private static final String DAYU_MC_FLAG = "-dayu";
    /**
     * 查询任务列表
     *
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public Result<ListTaskResponse> list(HttpServletRequest request, @RequestBody FetchListParams fetchListParams) {
        CheckPermissUtil.checkPermissByAppName(fetchListParams.getBizId());
        ListTaskResponse listTaskResponse = new ListTaskResponse();
        Pair<List<TaskBo>, Integer> pair;

        TaskQryParam taskQryParam = new TaskQryParam();
        taskQryParam.setBid(fetchListParams.getBizId()+DAYU_MC_FLAG);
        taskQryParam.setName(fetchListParams.getName());
        taskQryParam.setId(fetchListParams.getId());

        pair = taskProxy.queryList(fetchListParams.getPage(), fetchListParams.getPageSize(), taskQryParam);
        listTaskResponse.setList(pair.getLeft());
        listTaskResponse.setTotal(pair.getRight());
        return Result.success(listTaskResponse);
    }

    /**
     * 查询任务详情
     *
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public Result<com.xiaomi.youpin.mischedule.api.service.bo.TaskDo> detail(@RequestParam(value = "taskId") int taskId) {
        return Result.success(taskProxy.getTaskById2(taskId));
    }

    /**
     * 查询任务历史
     *
     */
    @RequestMapping(value = "/history", method = RequestMethod.POST)
    public Result<Object> history(Integer page, Integer pageSize, Integer taskId) {
        Map<String,Object> result = new HashMap<>();

        PageInfo<TaskHistoryBo> taskHistoryPageInfo = taskProxy.taskHistory(page, pageSize, taskId);
        if(taskHistoryPageInfo != null && !CollectionUtils.isEmpty(taskHistoryPageInfo.getData())){
            result.put("page",taskHistoryPageInfo.getPageNum());
            result.put("pageSize",taskHistoryPageInfo.getPageSize());
            result.put("count",taskHistoryPageInfo.getTotal());
            List<TaskHistoryBo> historyBos = taskHistoryPageInfo.getData();
            List<TaskContentVo> data = new ArrayList<>();
            historyBos.forEach(taskHistoryBo -> {
                TaskContentVo contentVo = JSON.parseObject(taskHistoryBo.getContent(), TaskContentVo.class);
                contentVo.setTriggerType(taskHistoryBo.getTriggerType());
                data.add(contentVo);
            });
            result.put("list",data);
        }
        return Result.success(result);
    }
    /**
     * 查询警告组
     *
     */
    @RequestMapping(value = "/queryAlertGorup", method = RequestMethod.GET)
    public Result<List<AlertGroupQryInfo>> queryAlertGorup() {
        return Result.success(alertGorupFacadeRpc.query(UserInfoThreadLocal.getUserInfo().getUserName(),null));
    }

    /**
     * 创建定时任务
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultResponse<Boolean> create(HttpServletRequest request,
                                          HttpServletResponse response,
                                          @RequestBody Task task
    ) throws IOException {
        CheckPermissUtil.checkPermissByAppName(task.getApplicationName());
//        SessionAccount account = loginService.getAccountFromSession(request);
        String userName = UserInfoThreadLocal.getUserInfo().getUserName();
        if (Objects.isNull(userName)) {
            log.warn("[ScheduleController.create] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }
        String type = task.getType();
        String cron = Optional.ofNullable(task.getCron()).orElse("");
        String taskName = Optional.ofNullable(task.getName()).orElse("");
        task.setCreator(userName);
        //http 任务
        if (type.equals(STaskDef.HttpTask.type)) {
            HttpParams httpParams = task.getHttpParams();

            if (Objects.nonNull(task.getHttpParams().getEmail())) {
                if (task.getHttpParams().getEmail().equals("true")) {
                    task.setNeedEmail(true);
                }
            }

            if (Objects.nonNull(task.getNeedEmail()) && task.getNeedEmail()) {
                httpParams.setInterceptorName("TInterceptor");
            } else {
                httpParams.setInterceptorName("DefaultInterceptor");
            }
            scheduleExcecute.httpExcecute(taskName, httpParams, task.getDefineErrorRetryNum(), cron, task.getExecuteTime(), task,userName);
        } else if (type.equals(STaskDef.DubboTask.type)) {
            //dubbo 任务
            DubboParams dubboParams = task.getDubboParams();

            if (Objects.nonNull(task.getDubboParams().getEmail())) {
                if (task.getDubboParams().getEmail().equals("true")) {
                    task.setNeedEmail(true);
                }
            }
            if (task.getNeedEmail() != null && task.getNeedEmail()) {
                dubboParams.setInterceptorName("TInterceptor");
            } else {
                dubboParams.setInterceptorName("DefaultInterceptor");
            }
            scheduleExcecute.dubboExcecute(taskName, dubboParams, task.getDefineErrorRetryNum(), cron, task,userName);
        }
        return ResultResponse.success(true);
    }

    /**
     * 启动任务
     *
     * @return
     */
    @RequestMapping(value = "/start", method = RequestMethod.POST)
    public Result<Boolean> start(HttpServletRequest request,int taskId) {
        return taskProxy.taskStart(taskId);
    }

    /**
     * 暂停任务
     * @param taskId
     * @return
     */
    @RequestMapping(value = "/pause")
    public Result<Boolean> taskPause(Integer taskId) {
        return taskProxy.taskPause(taskId);
    }

    @RequestMapping(value = "/delTask",method = RequestMethod.POST)
    public Result<Boolean> delTaskById(@RequestParam(value = "taskID") int taskID, HttpServletRequest request, HttpServletResponse response) throws IOException {
        //SessionAccount account = loginService.getAccountFromSession(request);
        String account = UserInfoThreadLocal.getUserInfo().getUserName();
        if (Objects.isNull(account)) {
            log.warn("[ScheduleController.delTask] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }
        TaskDo task = taskProxy.getTaskById2(taskID);
        if(Objects.isNull(task)){
            return  null;
        }
        if(!task.getCreator().equals(account)){
            response.sendError(403,"只能删除创建的任务");
            return  null;
        }
        return taskProxy.delTask(taskID);
    }
    /**
     * 编辑任务
     * @param request
     * @param task
     * @return
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public Result<Boolean> taskEdit(HttpServletRequest request,
                                    HttpServletResponse response,
                                    @RequestBody Task task) throws IOException {
        String account = UserInfoThreadLocal.getUserInfo().getUserName();
        //SessionAccount account = loginService.getAccountFromSession(request);
        if (Objects.isNull(account)) {
            log.warn("[ScheduleController.edit] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }

        log.info("TaskController edit task:{}", task);

        TaskDefBean taskDefBean = null;
        Map<String, String> p = new HashMap<>();

        TaskDo fetchTask = taskProxy.getTaskById2(task.getId());
        TaskParam params1 = new Gson().fromJson(fetchTask.getParams(), TaskParam.class);

        // 只修改了 name
        if (!Objects.equals(fetchTask.getName(), task.getName())) {
            taskProxy.modifyTaskName(task.getId(), task.getName());
        }

        if (task.getType().equals(STaskDef.HttpTask.type)) {
            taskDefBean = new TaskDefBean(STaskDef.HttpTask);
            taskDefBean.setName(task.getName());
            if (!StringUtils.isEmpty(task.getTimeout())) {
                taskDefBean.setTimeOut(Integer.parseInt(task.getTimeout()));
            }
            HttpParams httpParams = task.getHttpParams();

            HttpTaskParam httpTaskParam = new HttpTaskParam();

            httpTaskParam.setHeaders(new HashMap<>());
            httpTaskParam.setBody(httpParams.getBody());
            httpTaskParam.setMethodType(httpParams.getMethod());
            httpTaskParam.setUrl(httpParams.getUrl());

            if (task.getNeedEmail() != null && task.getNeedEmail()) {
                httpParams.setInterceptorName("TInterceptor");
            }

            // httpParams.setInterceptorName("TInterceptor");
            p.put("param", new Gson().toJson(httpTaskParam));
            if (httpParams.getInterceptorName() != null) {
                p.put("interceptor", httpParams.getInterceptorName());
            }else{
                p.put("interceptor", "DefaultInterceptor");
            }
        } else if (task.getType().equals(STaskDef.DubboTask.type)) {
            taskDefBean = new TaskDefBean(STaskDef.DubboTask);
            taskDefBean.setName(task.getName());
            if (!StringUtils.isEmpty(task.getTimeout())) {
                taskDefBean.setTimeOut(Integer.parseInt(task.getTimeout()));
            }
            DubboParams dubboParams = task.getDubboParams();

            MethodInfo methodInfo = new MethodInfo();
            methodInfo.setServiceName(dubboParams.getServiceName());
            methodInfo.setMethodName(dubboParams.getMethodName());
            methodInfo.setGroup(dubboParams.getGroup());
            methodInfo.setRetries(dubboParams.getRetries());
            methodInfo.setIsOneway(dubboParams.getIsOneway());
            methodInfo.setShardBroadcast(dubboParams.getShardBroadcast());
            if (StringUtils.isNotEmpty(dubboParams.getVersion())) {
                methodInfo.setVersion(dubboParams.getVersion());
            }
            if (StringUtils.isEmpty(dubboParams.getParameterTypes())) {
                methodInfo.setParameterTypes(new String[]{});
            } else {
                String[] types = new Gson().fromJson(dubboParams.getParameterTypes(), new TypeToken<String[]>(){}.getType());
                methodInfo.setParameterTypes(types);
            }
            if (StringUtils.isNotEmpty(dubboParams.getArgs())){
                Object[] params = new Gson().fromJson(dubboParams.getArgs(), new TypeToken<Object[]>(){}.getType());
                if (params != null) {
                    methodInfo.setArgs(params);
                } else {
                    methodInfo.setArgs(new Object[]{});
                }
            }
            if (!StringUtils.isEmpty(task.getTimeout())) {
                methodInfo.setTimeout(Integer.parseInt(task.getTimeout()));
            }

            // dubboParams.setInterceptorName("TInterceptor");
            if (task.getNeedEmail() != null && task.getNeedEmail()) {
                dubboParams.setInterceptorName("TInterceptor");
            }
            HashMap<String, String> param = new HashMap<>();
            String interceptor = Optional.ofNullable(dubboParams.getInterceptorName()).orElse("");
            if (StringUtils.isNotBlank(interceptor)){
                p.put("interceptor", interceptor);
            }
            p.put("param", new Gson().toJson(methodInfo));
        }
        taskDefBean.setErrorRetryNum(task.getDefineErrorRetryNum());
        params1.setTaskDef(taskDefBean);
        params1.setParam(p);
        params1.setTaskId(task.getId());
        params1.setCron(task.getCron());
        if(task.getExecuteTime()!=0){
            params1.setExecuteTime(task.getExecuteTime());
        }

        // 修改把四个属性插入进去 需要判断dubbo和http
        if (task.getType().equals(STaskDef.DubboTask.type)) {
            params1.param.put("email", task.getDubboParams().getEmail());
            params1.param.put("feishu", task.getDubboParams().getFeishu());
            params1.param.put("history", task.getDubboParams().getHistory());
        }
        if (task.getType().equals(STaskDef.HttpTask.type)) {
            params1.param.put("email", task.getHttpParams().getEmail());
            params1.param.put("feishu", task.getHttpParams().getFeishu());
            params1.param.put("history", task.getHttpParams().getHistory());
        }

        params1.setAlarmUsername(task.getAlarmUsername());
        params1.setAlarmLevel(task.getAlarmLevel());
        params1.setAlarmGroup(task.getAlarmGroup());
        log.info("taskParam {}", params1);
        Result<Boolean> booleanResult = taskProxy.modifyTask(task.getId(), new Gson().toJson(params1, TaskParam.class));
        return Result.success(booleanResult.getData());
    }

    @RequestMapping(value = "/manualExecute", method = RequestMethod.GET)
    public Result<String> manualExecute(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "taskID") int taskID) throws IOException {
//        SessionAccount account = loginService.getAccountFromSession(request);
        String account = UserInfoThreadLocal.getUserInfo().getUserName();
        if (Objects.isNull(account)) {
            log.warn("[ScheduleController.manualExecute] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }
        return taskProxy.manualExecute(taskID);
    }

    /**
     * 根据服务名大致搜索nacos上的服务列表可搜dubbo服务
     *
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping(value = "/loadDubboServices", method = RequestMethod.POST)
    public Result<List<DubboService>> loadDubboApiServices(HttpServletRequest request,
                                                           HttpServletResponse response, String serviceName) throws IOException, NacosException {
//        SessionAccount account = loginService.getAccountFromSession(request);
        String account = UserInfoThreadLocal.getUserInfo().getUserName();
        if (Objects.isNull(account)) {
            log.warn("[AccountController.loadDubboApiServices] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }
        return scheduleService.loadDubboApiServices(serviceName);
    }

    /**
     * 方法名集合
     *
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping(value = "/getServiceMethod", method = RequestMethod.POST)
    public Result<List<String>> getServiceMethod(HttpServletRequest request,
                                                           HttpServletResponse response, String serviceName) throws IOException, NacosException {
        //SessionAccount account = loginService.getAccountFromSession(request);
        String account = UserInfoThreadLocal.getUserInfo().getUserName();
        if (Objects.isNull(account)) {
            log.warn("[AccountController.getServiceMethod] current user not have valid account info in session");
            response.sendError(401, "未登录或者无权限");
            return null;
        }
        return scheduleService.getServiceMethod(serviceName);
    }
}

