package com.xiaomi.dayu.controller.iauth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.model.iauth.SwappedWhiteInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 接口入参返参参考 http://mock.be.mi.com/project/3724/interface/api
 */
@Deprecated
@Api(description = "消费端操作")
@RestController
@RequestMapping("/api/iauth/api/app")
public class AppListController {
    private static final Logger logger = LoggerFactory.getLogger(AppListController.class);
    private static final String TEMPLATE_IAUTH_CONFIG="iauth.sdk.app.mode=true\n" +
            "iauth.sdk.app.appId=${appId}\n" +
            "iauth.sdk.app.appKey=${appKey}\n" +
            "iauth.sdk.app.env=${env}\n" +
            "iauth.sdk.app.signVersion=2\n" +
            "iauth.sdk.app.platform=china-area";
    @GetMapping("/list")
    public ResultResponse searchAPPList(HttpServletRequest request,
                                                @RequestParam(value = "platform",required = false) String platform,
                                                @ApiParam(value = "填写当前appName")  @RequestParam("appName") String appName,
                                                @ApiParam(value = "模糊查询条件") @RequestParam(value = "query",required = false) String query,
                                                @ApiParam(value = "0：查询自己，2:查询其他")  @RequestParam("source") int source,
                                                @RequestParam(value = "filterNoSub", required = false, defaultValue = "true") Boolean filterNoSub,
                                                @RequestParam("pageNo") Integer pageNo,
                                                @RequestParam("pageSize") Integer pageSize,
                                                @ApiParam(value = "排序：ASC：升序，DESC：降序")  @RequestParam("sort") String sort) {
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request);
        if(response.isSuccess() && source == 0){
            JSONObject jsonObject = JSON.parseObject(response.getData());
            if(jsonObject.getInteger("totalElements") > 0){
                JSONArray items = jsonObject.getJSONArray("content");
                for (Object item : items) {
                    JSONObject object = (JSONObject) item;
                    object.put("appId",object.getString("appId"));
                }
                return ResultResponse.success(jsonObject);
            }
        }
        return response;
    }

    /**
     * 创建新的APP
     *
     */
    @ApiOperation("创建新的APP")
    @PostMapping("/")
    public ResultResponse<String> createAPP(HttpServletRequest request,
                                            @RequestParam("appName") String appName,
                                            @RequestParam(value = "platform",required = false) String platform,
                                            @RequestParam(value = "department",required = false) String department) {
        HashMap<String, Object> bodyParams = new HashMap<>();
        request.getParameterMap().forEach((key,values)->{
            bodyParams.put(key, values[0]);
        });
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request, "/api/app", RequestMethod.POST, null, bodyParams);
        if(response.isSuccess()){
            initIauthConfig(request,appName,response);
        }
        return response;
    }

    private void initIauthConfig(HttpServletRequest request,String appName, ResultResponse<String> response) {
        JSONObject jsonObject = JSON.parseObject(response.getData());
        HashMap<String, String> params = new HashMap<>();
        String content = TEMPLATE_IAUTH_CONFIG.replace("${appId}", jsonObject.getString("appId"))
                .replace("${appKey}", jsonObject.getString("appSecret"))
                .replace("${env}", "pro".equals(System.getProperty("spring.profiles.active")) ? "prod":"staging");
        params.put("content",content);
        params.put("appName",appName);
        params.put("group","DEFAULT_GROUP");
        params.put("dataId","iauth-consumer-"+appName);
        HttpClient.nacosProxyHttp(request,"/nacos/v1/cs/configs",RequestMethod.POST.name(),params );
    }

    /**
     * 查看私钥
     *
     */
    @ApiOperation("查看私钥")
    @GetMapping("/{appId}")
    public ResultResponse<String> getSecret(HttpServletRequest request,
                                            @ApiParam(value = "创建时生成的id") @PathVariable("appId") long appId,
                                            @RequestParam("appName") String appName,
                                            @RequestParam(value = "platform",required = false) String platform) {
        return HttpClient.iauthProxyHttp(request);
    }

    /**
     * 更新私钥
     *
     */
    @ApiOperation("更新私钥")
    @GetMapping("/{appId}/update")
    public ResultResponse<String> updateSecret(HttpServletRequest request,
                                               @ApiParam(value = "创建时生成的id")@PathVariable("appId") long appId,
                                               @RequestParam("appName") String appName,
                                               @RequestParam(value = "platform",required = false) String platform) {
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request);
        if(response.isSuccess()){
            updateIauthConfig(request,appName,appId);
        }
        return response;
    }

    public static void updateIauthConfig(HttpServletRequest request,String appName,long appId) {
        ResultResponse<String> response = HttpClient.iauthProxyHttp(request, "/api/app/"+appId, RequestMethod.GET, null,null);
        if(response.isSuccess()){
            String content = TEMPLATE_IAUTH_CONFIG.replace("${appId}", appId+"")
                    .replace("${appKey}", response.getData())
                    .replace("${env}", "pro".equals(System.getProperty("spring.profiles.active")) ? "prod":"staging");
            HashMap<String,String> params = new HashMap<>();
            params.put("appId",appId+"");
            params.put("status","2");
            ResultResponse<String> response2 = HttpClient.iauthProxyHttp(request, "/api/whitelist/app", RequestMethod.GET, params,null);
            StringBuilder serviceBuild = new StringBuilder();
            if(response2.isSuccess()) {
                List<SwappedWhiteInfo> swappedWhiteInfos = JSON.parseObject(response2.getData(), new TypeReference<List<SwappedWhiteInfo>>() {});
                if(CollectionUtils.isNotEmpty(swappedWhiteInfos)){
                    List<String> serviceIdList = new ArrayList<>();
                    for (SwappedWhiteInfo swappedWhiteInfo : swappedWhiteInfos) {
                        if(!serviceIdList.contains(swappedWhiteInfo.getServiceId())){
                            serviceIdList.add(swappedWhiteInfo.getServiceId());
                        }
                    }
                    for (int i = 0; i < serviceIdList.size(); i++) {
                        serviceBuild.append("iauth.sdk.app.serviceId."+(i+1)).append("=").append(serviceIdList.get(i)).append("\n");
                    }
                }
            }
            params.put("content",content+"\n"+serviceBuild.toString());
            params.put("appName",appName);
            params.put("group","DEFAULT_GROUP");
            params.put("dataId","iauth-consumer-"+appName);
            HttpClient.nacosProxyHttp(request,"/nacos/v1/cs/configs",RequestMethod.POST.name(),params);
        }

    }

    /**
     * 查看该APP的授权信息
     *
     */
    @ApiOperation("APP的授权信息")
    @GetMapping("/{appId}/auth")
    public ResultResponse<String> getAuthInfo(HttpServletRequest request,
                                              @PathVariable("appId") long appId,
                                              @RequestParam("appName") String appName,
                                              @RequestParam(value = "platform",required = false) String platform) {
        return HttpClient.iauthProxyHttp(request);
    }
}
