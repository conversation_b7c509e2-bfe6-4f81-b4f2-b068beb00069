package com.xiaomi.dayu.controller.nacos;

import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.constants.Constants;
import com.xiaomi.dayu.service.NacosLoginService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

@Slf4j
@Api(tags = {"Nacos Catalog APIs"})
@RestController
@RequestMapping(Constants.NACOS_NAMING_CONTEXT)
public class NacosCatalogController {

    @Autowired
    private NacosLoginService nacosLoginService;

    @GetMapping("/services")
    public ResultResponse<Object> list(HttpServletRequest request,
                                                 @RequestParam(required = false) boolean withInstances,
                                                 @RequestParam(defaultValue = com.alibaba.nacos.api.common.Constants.DEFAULT_NAMESPACE_ID) String namespaceId,
                                                 @RequestParam(required = false) int pageNo,
                                                 @RequestParam(required = false) int pageSize,
                                                 @RequestParam(name = "serviceNameParam", defaultValue = StringUtils.EMPTY) String serviceName,
                                                 @RequestParam(name = "groupNameParam", defaultValue = StringUtils.EMPTY) String groupName,
                                                 @RequestParam(name = "instance", defaultValue = StringUtils.EMPTY) String containedInstance,
                                                 @RequestParam(required = false) boolean hasIpCount
    ) {

        HashMap<String, String> params = new HashMap<>();
        params.put("accessToken",nacosLoginService.getNacosAccessToken());
        ResultResponse<Object> response = HttpClient.nacosProxyHttp(request, RequestMethod.GET.name(), params);
        return ResultResponse.success(response);
    }

    @GetMapping("/service")
    public ResultResponse<Object> serviceDetail(HttpServletRequest request,
                                                @RequestParam(defaultValue = com.alibaba.nacos.api.common.Constants.DEFAULT_NAMESPACE_ID) String namespaceId,
                                                @RequestParam String groupName,
                                                @RequestParam String serviceName){

        HashMap<String, String> params = new HashMap<>();
        params.put("accessToken",nacosLoginService.getNacosAccessToken());
        ResultResponse<Object> response = HttpClient.nacosProxyHttp(request, RequestMethod.GET.name(), params);
        return ResultResponse.success(response);
    }

    @GetMapping(value = "/instances")
    public ResultResponse<Object> instanceList(HttpServletRequest request,
                                                   @RequestParam(defaultValue = com.alibaba.nacos.api.common.Constants.DEFAULT_NAMESPACE_ID) String namespaceId,
                                                   @RequestParam String groupName,
                                                   @RequestParam String serviceName,
                                                   @RequestParam String clusterName,
                                                   @RequestParam(name = "pageNo") int page,
                                                   @RequestParam int pageSize){

        HashMap<String, String> params = new HashMap<>();
        params.put("accessToken",nacosLoginService.getNacosAccessToken());
        ResultResponse<Object> response = HttpClient.nacosProxyHttp(request, RequestMethod.GET.name(), params);
        return ResultResponse.success(response);
    }
}
