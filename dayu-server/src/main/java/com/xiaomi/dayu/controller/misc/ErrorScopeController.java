package com.xiaomi.dayu.controller.misc;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xiaomi.dayu.bo.request.BindScopeReq;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.rpc.ErrorScopeServiceRpc;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.dayu.rpc.PolicyServiceRpc;
import com.xiaomi.dayu.service.ScopeReviewerService;
import com.xiaomi.dayu.service.UserService;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.hermes.bo.request.ChangePermissionReq;
import com.xiaomi.youpin.hermes.enums.PrivilegeEnum;
import com.xiaomi.youpin.hermes.enums.ResourceTypeEnum;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.mone.drizzleapi.bo.ErrorScopeBo;
import com.xiaomi.mone.drizzleapi.bo.ListErrorScopeReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkArgument;
import static com.xiaomi.dayu.constants.Constants.DEFAULT_RPC_TIMEOUT_MS;

/**
 * <AUTHOR> Yang (<EMAIL>)
 * @version 1.0
 * @since 2022/5/10
 */
@Validated
@RestController
@RequestMapping("/api/misc/error/scope")
public class ErrorScopeController {

    @Autowired
    private ErrorScopeServiceRpc errorScopeService;

    @Autowired
    private PolicyServiceRpc policyService;

    @Autowired
    private ScopeReviewerService scopeReviewerService;

    @Autowired
    private IProjectServiceRpc projectService;

    @Autowired
    private UserService userService;

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<Object> listScopes(HttpServletRequest request,
                                     @RequestParam(required = false) Integer projectId,
                                     @RequestParam(required = false) String keyword,
                                     @RequestParam(required = false) Integer pageNo,
                                     @RequestParam(required = false) Integer pageSize)
            throws ExecutionException, InterruptedException, TimeoutException {
        Map<String, Object> dict = new HashMap<>();

        if (projectId != null) {
            List<String> sids = this.policyService.searchResources(
                    Collections.singletonList(String.valueOf(projectId)),
                    ResourceTypeEnum.SCOPE, PrivilegeEnum.READ.getValue()
            );
            List<ErrorScopeBo> details = sids.isEmpty() ? new ArrayList<>() :
                    this.errorScopeService.scopeDetails(
                            sids.stream().map(Integer::valueOf).collect(Collectors.toList())
                    );
            dict.put("scopes", details);
            dict.put("count", details.size());
        } else {
            ListErrorScopeReq reqBody = ListErrorScopeReq.builder()
                    .keyword(keyword)
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .build();
            CompletableFuture<List<ErrorScopeBo>> f1 = CompletableFuture.supplyAsync(
                    () -> this.errorScopeService.listScopes(reqBody)
            );
            CompletableFuture<Long> f2 = CompletableFuture.supplyAsync(
                    () -> this.errorScopeService.countScopes(reqBody)
            );
            CompletableFuture<Void> cf = CompletableFuture.allOf(f1, f2);
            cf.get(DEFAULT_RPC_TIMEOUT_MS, TimeUnit.MILLISECONDS);

            dict.put("scopes", f1.get());
            dict.put("count", f2.get());
        }
        return Result.success(dict);
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Result<Object> saveScopes(HttpServletRequest request,
                                     @RequestBody ErrorScopeBo reqBody) {
        if (StringUtils.isBlank(reqBody.getUpdateBy())) {
            reqBody.setUpdateBy(this.currentUserName());
        }
        this.errorScopeService.saveScope(reqBody);
        return Result.success("");
    }

    @RequestMapping(value = "/reviewers", method = RequestMethod.GET)
    public Result<Object> reviewers(HttpServletRequest request,
                                    @RequestParam(required = false) String appName,
                                    @RequestParam(required = false) Integer projectId) {
        List<String> reviewers = this.scopeReviewerService.getScopeReviewers();
        List<String> list = new ArrayList<>();

        if (projectId != null) {
            String pidStr = String.valueOf(projectId);
            Set<String> adminSet = this.userService.getAdminSet();
            list = reviewers.stream().filter(obj -> !adminSet.contains(obj)).collect(Collectors.toList());

            Map<String, Set<String>> reviewerMap = this.scopeReviewerService.scopeReviewerAppNames(list);
            list.clear();

            for (String username : reviewers) {
                if (adminSet.contains(username) || (reviewerMap.containsKey(username) && reviewerMap.get(username).contains(pidStr))) {
                    list.add(username);
                }
            }
        }
        Map<String, Object> dict = new HashMap<>();
        dict.put("reviewers", projectId != null ? list : reviewers);
        return Result.success(dict);
    }

    @RequestMapping(value = "/bind", method = RequestMethod.POST)
    public Result<Object> bind(HttpServletRequest request,
                               @Valid @RequestBody BindScopeReq reqBody) {
        checkArgument(PrivilegeEnum.READ.getValue() == reqBody.getAction() ||
                        PrivilegeEnum.NONE.getValue() == reqBody.getAction(),
                "scope bind invalid action value " + reqBody.getAction());

        if (PrivilegeEnum.READ.getValue() == reqBody.getAction()) {
            List<String> sids = this.policyService.searchResources(
                    reqBody.getProjectIds().stream().map(String::valueOf).collect(Collectors.toList()),
                    ResourceTypeEnum.SCOPE, PrivilegeEnum.READ.getValue()
            );
            checkArgument(sids.isEmpty(), "applications already bound to the following scopes " + sids +
                    ", please unbind from previous scopes before rebind");
        }
        return Result.success(
                this.policyService.changePermissions(ChangePermissionReq.builder()
                        .updateBy(this.currentUserName())
                        .privilege(reqBody.getAction())
                        .resourceValue(String.valueOf(reqBody.getScopeId()))
                        .resourceType(ResourceTypeEnum.SCOPE)
                        .assignees(reqBody.getProjectIds().stream().map(String::valueOf).collect(Collectors.toList()))
                        .build())
        );
    }

    @RequestMapping(value = "/app/list", method = RequestMethod.GET)
    public Result<Object> listApp(HttpServletRequest request,
                                  @RequestParam Integer sid,
                                  @RequestParam(required = false) Integer pageNo,
                                  @RequestParam(required = false) Integer pageSize)
            throws ExecutionException, InterruptedException, TimeoutException {
        CompletableFuture<List<ProjectBo>> f1 = CompletableFuture.supplyAsync(
                () -> this.policyService.searchAssignees(
                        ResourceTypeEnum.SCOPE,
                        String.valueOf(sid),
                        PrivilegeEnum.READ.getValue(),
                        pageNo,
                        pageSize
                )).thenApplyAsync((pids) -> {
            List<Integer> projectIds = pids.stream().map(Integer::valueOf).collect(Collectors.toList());
            return this.projectService.getProjectsByIds(projectIds);
        });
        CompletableFuture<Integer> f2 = CompletableFuture.supplyAsync(
                () -> this.policyService.countAssignees(
                        ResourceTypeEnum.SCOPE,
                        String.valueOf(sid),
                        PrivilegeEnum.READ.getValue()));
        CompletableFuture<Void> cf = CompletableFuture.allOf(f1, f2);
        cf.get(DEFAULT_RPC_TIMEOUT_MS, TimeUnit.MILLISECONDS);

        Set<String> appNameSet = this.accessibleAppNameSet();
        List<ObjectNode> projectResult = new ArrayList<>();

        f1.get().forEach(bo -> {
            ObjectNode node = JsonNodeFactory.instance.objectNode();
            node.put("id", bo.getId());
            node.put("name", bo.getName());
            node.put("access", appNameSet.contains(bo.getName()) ? 1 : 0);
            node.put("desc", bo.getDesc());
            projectResult.add(node);
        });
        Map<String, Object> dict = new HashMap<>();
        dict.put("apps", projectResult);
        dict.put("count", f2.get());
        return Result.success(dict);
    }

    private String currentUserName() {
        return UserInfoThreadLocal.getUserInfo().getUserName();
    }

    private Set<String> accessibleAppNameSet() {
        return UserInfoThreadLocal.getUserInfo().getApplicationMap().keySet();
    }
}
