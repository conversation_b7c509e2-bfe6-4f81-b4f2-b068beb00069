package com.xiaomi.dayu.controller.schedule;

import com.xiaomi.data.push.common.ConcurrencyStrategyEnum;
import com.xiaomi.dayu.rpc.MoonTaskDubboServiceRpc;
import com.xiaomi.mone.http.docs.annotations.HttpApiDoc;
import com.xiaomi.mone.http.docs.annotations.MiApiRequestMethod;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.mischedule.api.service.bo.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import run.mone.moon.api.bo.task.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/moonTask")
public class MoonTaskController {


    @Autowired
    private MoonMoneTpcContextUtil moonMoneTpcContextUtil;
    @Autowired
    private MoonTaskDubboServiceRpc moonTaskDubboServiceRpc;

    @PostMapping(value = "/list")
    public Result<TaskList> list(HttpServletRequest request,@RequestBody ReadTaskReq readTaskReq) {
        String moonEnv = request.getHeader("moonEnv");
        log.info("moonEnv={}",moonEnv);
        TaskList taskList = moonTaskDubboServiceRpc.list(moonMoneTpcContextUtil.getMoonMoneTpcContext(), readTaskReq);
        return Result.success(taskList);
    }
    @PostMapping(value = "/create")
    public Result<Long> create(@RequestBody TaskReq taskReq) {
        Long result = moonTaskDubboServiceRpc.create(moonMoneTpcContextUtil.getMoonMoneTpcContext(),taskReq);
        return Result.success(result);
    }


    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Result<Integer> update(HttpServletRequest request, @RequestBody TaskReq req) {
        return Result.success(moonTaskDubboServiceRpc.update(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req));
    }

    @HttpApiDoc(apiName = "task删除", value = "/api/moon/task/delete", method = MiApiRequestMethod.POST, description = "task删除")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public Result<Integer> delete(HttpServletRequest request, @RequestBody List<Long> req) {
        return Result.success(moonTaskDubboServiceRpc.delete(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req));
    }

    @HttpApiDoc(apiName = "task获取", value = "/api/moon/task/get", method = MiApiRequestMethod.POST, description = "task获取")
    @PostMapping(value = "/get")
    public Result<TaskReq> get(HttpServletRequest request, @RequestBody TaskReq req) {
       return Result.success(moonTaskDubboServiceRpc.get(moonMoneTpcContextUtil.getMoonMoneTpcContext(),req.getId()));
    }

    @HttpApiDoc(apiName = "task启用", value = "/api/moon/task/enable", method = MiApiRequestMethod.POST, description = "task启用")
    @RequestMapping(value = "/enable", method = RequestMethod.POST)
    public Result<Integer> enable(HttpServletRequest request, @RequestBody List<Long> req) {
        return Result.success(moonTaskDubboServiceRpc.enable(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req,true));
    }

    @HttpApiDoc(apiName = "task禁用", value = "/api/moon/task/disable", method = MiApiRequestMethod.POST, description = "task禁用")
    @RequestMapping(value = "/disable", method = RequestMethod.POST)
    public Result<Integer> disable(HttpServletRequest request, @RequestBody List<Long> req) {
        return  Result.success(moonTaskDubboServiceRpc.enable(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req,false));
    }

    @HttpApiDoc(apiName = "手动运行", value = "/api/moon/task/run", method = MiApiRequestMethod.POST, description = "手动运行")
    @RequestMapping(value = "/run", method = RequestMethod.POST)
    public Result<Boolean> run(HttpServletRequest request, @RequestParam Long id) {
        return Result.success(moonTaskDubboServiceRpc.run(moonMoneTpcContextUtil.getMoonMoneTpcContext(), id));
    }

    @HttpApiDoc(apiName = "task运行历史获取", value = "/api/moon/task/history/get", method = MiApiRequestMethod.POST, description = "task运行历史获取")
    @RequestMapping(value = "/history/get", method = RequestMethod.GET)
    public Result<TaskHistoryRes> historyGet(HttpServletRequest request, ReadTaskHistoryReq req) {
        TaskHistoryRes TaskHistoryRes = moonTaskDubboServiceRpc.historyGet(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req);
        return Result.success(TaskHistoryRes);
    }

    @HttpApiDoc(apiName = "task运行历史列表", value = "/api/moon/task/history/list", method = MiApiRequestMethod.POST, description = "task运行历史列表")
    @RequestMapping(value = "/history/list", method = RequestMethod.POST)
    public Result<PageInfo<TaskHistoryRes>> historyList(HttpServletRequest request, @RequestBody ReadTaskHistoryReq req) {

        PageInfo<TaskHistoryRes> taskHistoryResPageInfo = moonTaskDubboServiceRpc.historyList(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req);
        return Result.success(taskHistoryResPageInfo);
    }

    @HttpApiDoc(apiName = "task运行历史总结", value = "/api/moon/task/history/summary", method = MiApiRequestMethod.POST, description = "task运行历史总结")
    @RequestMapping(value = "/history/summary", method = RequestMethod.POST)
    public Result<TaskHistorySum> historySummary(HttpServletRequest request, @RequestBody ReadTaskSummaryReq req) {
        TaskHistorySum taskHistorySum = moonTaskDubboServiceRpc.historySummary(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req);
        return Result.success(taskHistorySum);
    }

    @HttpApiDoc(apiName = "task运行中任务停止", value = "/api/moon/task/history/stop", method = MiApiRequestMethod.POST, description = "task运行中任务停止")
    @RequestMapping(value = "/history/stop", method = RequestMethod.POST)
    public Result<Boolean> historyStop(HttpServletRequest request, @RequestBody ReadTaskHistoryReq req) {
        Boolean aBoolean = moonTaskDubboServiceRpc.historyStop(moonMoneTpcContextUtil.getMoonMoneTpcContext(), req);
        return Result.success(aBoolean);
    }

    @HttpApiDoc(apiName = "task运行状态枚举", value = "/api/moon/task/status_enum", method = MiApiRequestMethod.GET, description = "task运行状态枚举")
    @RequestMapping(value = "/status_enum", method = RequestMethod.GET)
    public Result<List<TaskStatusEnumRes>> statusEnum(HttpServletRequest request) {
        List<TaskStatusEnumRes> res = new ArrayList<>();
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Init));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Success));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Failure));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Retry));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Running));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Pause));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Timeout));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Cancel));
        return Result.success(res);
    }

    @HttpApiDoc(apiName = "task历史状态枚举", value = "/api/moon/task/history/status_enum", method = MiApiRequestMethod.GET, description = "task历史状态枚举")
    @RequestMapping(value = "/history/status_enum", method = RequestMethod.GET)
    public Result<List<TaskStatusEnumRes>> historyEnum(HttpServletRequest request) {
        List<TaskStatusEnumRes> res = new ArrayList<>();
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Success));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Failure));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Running));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Pause));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Timeout));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Cancel));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.NoMachine));
        res.add(new TaskStatusEnumRes(TaskStatusEnum.Recovered));
        return Result.success(res);
    }

    @HttpApiDoc(apiName = "task调度冲突方案枚举", value = "/api/moon/task/concurrency_strategy_enum", method = MiApiRequestMethod.GET, description = "task调度冲突方案枚举")
    @RequestMapping(value = "/concurrency_strategy_enum", method = RequestMethod.GET)
    public Result<List<ConcurrencyStrategyEnumRes>> concurrencyStrategy(HttpServletRequest request) {
        List<ConcurrencyStrategyEnumRes> res = new ArrayList<>();
        res.add(new ConcurrencyStrategyEnumRes(ConcurrencyStrategyEnum.Cancel.mode, "取消新任务"));
        res.add(new ConcurrencyStrategyEnumRes(ConcurrencyStrategyEnum.Concurrency.mode, "并发执行新任务"));
        return Result.success(res);
    }
    @HttpApiDoc(apiName = "校验cron", value = "/api/moon/task/checkCron", method = MiApiRequestMethod.GET, description = "校验cron")
    @RequestMapping(value = "/checkCron", method = RequestMethod.GET)
    public Result<List<String>> checkCron(@RequestParam String cron) {
        List<String> data = moonTaskDubboServiceRpc.checkCron(cron);
        return Result.success(data);
    }



}
