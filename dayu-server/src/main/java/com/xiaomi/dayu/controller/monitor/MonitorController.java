package com.xiaomi.dayu.controller.monitor;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.dto.ThreadpoolDTO;
import com.xiaomi.dayu.model.monitor.SentinelMonitorReq;
import com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig;
import com.xiaomi.dayu.mybatis.entity.ThreadpoolConfigHistory;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Type MonitorController.java
 * @Desc
 * @date 2025/4/8 14:10
 */
@SentinelResource
@RestController
@RequestMapping("/api/monitor")
public class MonitorController {
	@NacosValue("${grafana-mione.sentinel.url}")
	private String grafanaMioneSentinelUrl;

	@RequestMapping(value = "/sentinel/url", method = RequestMethod.POST)
	public ResultResponse<String> getSentinelMonitorUrl(@RequestBody(required = false) SentinelMonitorReq sentinelMonitorReq) {
		//https://grafana-mione.test.mi.com/d/customer/mione-sentinel-customer-dashboard?orgId=1&refresh=30s&var-app=All&theme=light&kiosk=tv
		StringBuffer stringbuffer = new StringBuffer();
		stringbuffer.append(grafanaMioneSentinelUrl);
		stringbuffer.append("?orgId=1&refresh=30s&from=now-15m&to=now");
		if(sentinelMonitorReq!= null && !CollectionUtils.isEmpty(sentinelMonitorReq.getAppNames())){
			for (String appName : sentinelMonitorReq.getAppNames()) {
				stringbuffer.append("&var-app=").append(appName);
			}
			stringbuffer.append("&theme=light&kiosk=tv");
			stringbuffer.append("&var-resource=All&var-type=All");
			return ResultResponse.success(stringbuffer.toString());
		}
		if(UserInfoThreadLocal.getUserInfo().isAdmin()){
			stringbuffer.append("&var-app=All");
		}else{
			List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
			for (String appName : applicationNames) {
				stringbuffer.append("&var-app=").append(appName);
			}
		}
		stringbuffer.append("&theme=light&kiosk=tv");
		stringbuffer.append("&var-resource=All&var-type=All");
		return ResultResponse.success(stringbuffer.toString());
	}
}
