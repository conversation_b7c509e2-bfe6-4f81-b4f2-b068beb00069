package com.xiaomi.dayu.controller.iauth;

import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
@Deprecated
@Api("方法级别列表")
@RestController
@RequestMapping("/api/iauth/api/scope")
public class ScopeListController {

    /**
     * 搜索
     *
     */
    @GetMapping("/query")
    public ResultResponse<String> getQueryList(HttpServletRequest request,
                                               @RequestParam("serviceId") String sid,
                                               @RequestParam("subServiceId") String ssid,
                                               @RequestParam(value = "scopeId",required = false) String scope,
                                               @RequestParam(value = "platform",required = false) String platform) {
        return HttpClient.iauthProxyHttp(request);
    }

    /**
     *
     * 获取所有的Sid和Scope列表
     */
    @GetMapping("/all")
    public ResultResponse<String> getAllSidAndScope(HttpServletRequest request)  {
        return HttpClient.iauthProxyHttp(request);
    }
}
