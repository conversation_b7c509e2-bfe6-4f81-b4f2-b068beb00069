package com.xiaomi.dayu.controller.threadpool;

import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.Gson;
import com.xiaomi.data.push.nacos.NacosConfig;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.dao.ThreadpoolConfigHistoryMapper;
import com.xiaomi.dayu.dao.ThreadpoolConfigMapper;
import com.xiaomi.dayu.model.dto.ThreadpoolDTO;
import com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig;
import com.xiaomi.dayu.mybatis.entity.ThreadpoolConfigHistory;
import com.xiaomi.dayu.rpc.OpenApiServiceRpc;
import com.xiaomi.dayu.service.ConfigParams;
import com.xiaomi.youpin.gwdash.service.OpenApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/threadpool")
public class ThreadPoolController {
    @Autowired
    private ThreadpoolConfigMapper threadpoolConfigMapper;
    @Autowired
    private ThreadpoolConfigHistoryMapper threadpoolConfigHistoryMapper;

//    @DubboReference(check = false, interfaceClass = OpenApiService.class, group = "${ref.gwdash.service.group}", timeout = 10000)
//    private OpenApiService gwdashOpenApiService;
    @Autowired
    private OpenApiServiceRpc openApiServiceRpc;

    @Autowired
    NacosConfig nacosConfig;

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultResponse<Boolean> create(@RequestBody ThreadpoolDTO threadpoolDTO) {
        CheckPermissUtil.checkPermissByAppName(threadpoolDTO.getAppName());
        List<ThreadpoolConfig> li = threadpoolConfigMapper.queryConfigsByAppAndPool(threadpoolDTO.getAppName(), threadpoolDTO.getPoolName());
        if (li.size() != 0) {
            return ResultResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "pool已存在");
        }
        ThreadpoolConfig conf = convertData(threadpoolDTO);
        threadpoolConfigMapper.insertConfig(conf);
        String dataId = threadpoolDTO.getAppName() + "_" + threadpoolDTO.getPoolName();
        Gson gson = new Gson();
        String content = gson.toJson(threadpoolDTO);
        try {
            nacosConfig.publishConfig(dataId, "DEFAULT_GROUP", content);
            System.out.println(content);
        } catch (NacosException e) {
            e.printStackTrace();
            return ResultResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }

        threadpoolConfigHistoryMapper.insertHistory(
                new ThreadpoolConfigHistory(
                        conf.getId(),
                        UserInfoThreadLocal.getUserInfo().getUserName(),
                        "create",
                        content,
                        dataId
                )
        );
        return ResultResponse.success(true);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultResponse<Boolean> update(@RequestBody ThreadpoolDTO threadpoolDTO) {
        CheckPermissUtil.checkPermissByAppName(threadpoolDTO.getAppName());
        List<ThreadpoolConfig> li = threadpoolConfigMapper.queryConfigs(threadpoolDTO.getId());
        if (li.size() == 0) {
            return ResultResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "pool不存在");
        }
        ThreadpoolConfig last=li.get(li.size() - 1);
        if (!last.getAppName().equals(threadpoolDTO.getAppName())) {
            return ResultResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "应用名异常");
        }
        if (!last.getPoolName().equals(threadpoolDTO.getPoolName())) {
            li = threadpoolConfigMapper.queryConfigsByAppAndPool(threadpoolDTO.getAppName(), threadpoolDTO.getPoolName());
            if (li.size() != 0) {
                return ResultResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "pool已存在");
            }
        }
        ThreadpoolConfig conf = convertData(threadpoolDTO);
        threadpoolConfigMapper.updateConfig(conf);
        String dataId = threadpoolDTO.getAppName() + "_" + threadpoolDTO.getPoolName();
        Gson gson = new Gson();
        String content = gson.toJson(threadpoolDTO);
        try {
            if (!last.getPoolName().equals(threadpoolDTO.getPoolName())) {
                String oldID = last.getAppName() + "_" + last.getPoolName();
                nacosConfig.deleteConfig(oldID, "DEFAULT_GROUP");
            }
            nacosConfig.publishConfig(dataId, "DEFAULT_GROUP", content);
            System.out.println(content);
        } catch (NacosException e) {
            e.printStackTrace();
            return ResultResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
        threadpoolConfigHistoryMapper.insertHistory(
                new ThreadpoolConfigHistory(
                        conf.getId(),
                        UserInfoThreadLocal.getUserInfo().getUserName(),
                        "update",
                        content,
                        dataId
                )
        );
        return ResultResponse.success(true);
    }

    private ThreadpoolConfig convertData(ThreadpoolDTO threadpoolDTO) {
        ThreadpoolConfig conf = new ThreadpoolConfig();
        conf.setId(threadpoolDTO.getId());
        conf.setCorePoolSize(threadpoolDTO.getCorePoolSize());
        conf.setAppName(threadpoolDTO.getAppName());
        conf.setCapacity(threadpoolDTO.getCapacity());
        conf.setPoolName(threadpoolDTO.getPoolName());
        conf.setMaximumPoolSize(threadpoolDTO.getMaximumPoolSize());
        conf.setReject(threadpoolDTO.getReject());
        conf.setKeepAliveTime(threadpoolDTO.getKeepAliveTime());
        return conf;
    }

    @RequestMapping(value = "/configs", method = RequestMethod.GET)
    public ResultResponse<List<ThreadpoolConfig>> configs(@RequestParam String appName) {
        CheckPermissUtil.checkPermissByAppName(appName);
        List<ThreadpoolConfig> li = threadpoolConfigMapper.queryConfigsOfApp(appName);
        return ResultResponse.success(li);
    }

    @RequestMapping(value = "/instances", method = RequestMethod.GET)
    public ResultResponse<List> getApplicationInstances(@RequestParam String appName) {
        CheckPermissUtil.checkPermissByAppName(appName);
        Map<String, List<String>> envIpList = openApiServiceRpc.envMachinesByAppName(appName, ConfigParams.getMilineEnv().getName());
        List<String> list = new ArrayList<>();
        if (envIpList != null) {
            envIpList.entrySet().forEach(entry -> {
                if (!CollectionUtils.isEmpty(entry.getValue())) {
                    list.addAll(entry.getValue());
                }
            });
        }
        return ResultResponse.success(list);
    }
}
