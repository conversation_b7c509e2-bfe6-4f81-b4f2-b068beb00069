
package com.xiaomi.dayu.controller.teslaauth;


import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.api.constants.SideEnum;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.util.CheckPermissUtil;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.dto.ServiceDTO;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.AuthDubboV2ServiceRpc;
import com.xiaomi.dayu.service.NamingInstanceService;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import com.xiaomi.youpin.tesla.auth.api.newbo.AuthApprovalDto;
import com.xiaomi.youpin.tesla.auth.api.newbo.AuthPageReqDto;
import com.xiaomi.youpin.tesla.auth.api.newbo.AuthProviderDto;
import com.xiaomi.youpin.tesla.auth.api.newbo.PageData;
import com.xiaomi.youpin.tesla.auth.common.AuthStatusEnum;
import com.xiaomi.youpin.tesla.auth.common.AuthTypeEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/api/auth")
public class AuthDubboController {

    @Autowired
    private NamingInstanceService namingInstanceService;
    @Autowired
    private AccountServiceRpc accountServiceRpc;
    @Autowired
    private ProviderService providerService;
    @Resource
    private AuthDubboV2ServiceRpc authDubboV2ServiceRpc;

    @ApiOperation(value = "配置鉴权服务时使用，查询已配置和未配置服务")
    @RequestMapping(value = "/configAuthList", method = {RequestMethod.POST})
    public ResultResponse<List<AuthProviderVo>> configAuthList(@RequestBody AuthProviderVo authProviderVo){
        try{
            if(StringUtils.isBlank(authProviderVo.getAppName())){
                return ResultResponse.fail(500,"providerAppName不能为空");
            }

            List<AuthProviderVo> result = new ArrayList<>();

            AuthPageReqDto authPageReqDto = new AuthPageReqDto();
            authPageReqDto.setProviderAppName(authProviderVo.getAppName());
            authPageReqDto.setPageSize(100);
            PageData<AuthProviderDto> authProviderDtoPageData = authDubboV2ServiceRpc.queryPageAuthProvider(authPageReqDto);
            if(authProviderVo.isApplied()){
                if(CollectionUtils.isNotEmpty(authProviderDtoPageData.getData())){
                    List<AuthProviderVo> collect = authProviderDtoPageData.getData().stream().map(dto -> new AuthProviderVo(dto, true)).collect(Collectors.toList());
                    Collections.sort(collect);
                    result.addAll(collect);

                }
                return ResultResponse.success(result);
            }
            Collection<ServiceDTO> serviceDTOS = null;
            if(namingInstanceService.getIsQueryNaming4DB()){
                ResultResponse<PageResult<ServiceDTO>> providerServiceDTOS = namingInstanceService.getProviderServiceDTOS(Constants.APPLICATION,"*",  authPageReqDto.getProviderAppName(),true,0, 1, 100);
                serviceDTOS = providerServiceDTOS.getData().getContent();
            }else{
                serviceDTOS = providerService.getServiceDTOS(Constants.APPLICATION,authPageReqDto.getProviderAppName() ,true);
            }
           // result.put("authList",authProviderDtoPageData.getData().stream().map(dto->new AuthProviderVo(dto,true)).collect(Collectors.toList()));
            if(CollectionUtils.isEmpty(serviceDTOS)){
               // result.addAll(authProviderDtoPageData.getData().stream().map(dto->new AuthProviderVo(dto,true)).collect(Collectors.toList()));
                return ResultResponse.success(result);
            }
            Map<String, AuthProviderDto> authMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(authProviderDtoPageData.getData())){
                authMap = authProviderDtoPageData.getData().stream().collect(Collectors.toMap(AuthProviderDto::getUnitKey, Function.identity(), (v1, v2) -> v1));
            }
            List<AuthProviderVo> noAuthList = new ArrayList<>();
            for (ServiceDTO serviceDTO : serviceDTOS) {
                String group = StringUtils.isBlank(serviceDTO.getGroup()) ? "":serviceDTO.getGroup();
                String version = StringUtils.isBlank(serviceDTO.getVersion()) ? "":serviceDTO.getVersion();
                StringBuilder stringBuilder = new StringBuilder(serviceDTO.getAppName());
                stringBuilder.append(":").append(group)
                        .append(":").append(serviceDTO.getService())
                        .append(":").append(version);
                String key = stringBuilder.toString();
                if(!authMap.containsKey(key)){
                    //noAuthList.put(stringBuilder.toString(),covertoAuthProviderDto(serviceDTO));
                    noAuthList.add(covertoAuthProviderVo(serviceDTO));
                }
            }
            if(!authMap.containsKey(authPageReqDto.getProviderAppName())){
                AuthProviderVo AuthProviderVo = new AuthProviderVo();
                AuthProviderVo.setAppName(authPageReqDto.getProviderAppName());
                AuthProviderVo.setAuthType(AuthTypeEnum.APPLICATION.getCode());
                noAuthList.add(AuthProviderVo);
            }
//            result.put("noAuthList",noAuthList);
            Collections.sort(noAuthList);
            result.addAll(noAuthList);
            /*if(CollectionUtils.isNotEmpty(authProviderDtoPageData.getData())){
                result.addAll(authProviderDtoPageData.getData().stream().map(dto->new AuthProviderVo(dto,true)).collect(Collectors.toList()));
            }*/
            return ResultResponse.success(result);
        }catch (Exception e){
            log.error("closeAuthProvider 调用异常，authProviderVo={}", JSON.toJSONString(authProviderVo),e);
            throw e;
        }
    }

    private AuthProviderVo covertoAuthProviderVo(ServiceDTO serviceDTO) {
        AuthProviderVo authProviderVo = new AuthProviderVo();
        authProviderVo.setAppName(serviceDTO.getAppName());
        authProviderVo.setDubboService(serviceDTO.getService());
        authProviderVo.setDubboGroup(serviceDTO.getGroup());
        authProviderVo.setDubboVersion(serviceDTO.getVersion());
        authProviderVo.setAuthType(AuthTypeEnum.INTERFACE.getCode());
        return authProviderVo;
    }


    @ApiOperation(value = "申请鉴权时使用，provider 分页查询，page 和 pageSize 不为空")
    @RequestMapping(value = "/queryPageAuthProvider", method = {RequestMethod.POST})
    public ResultResponse<PageData<AuthProviderVo>> queryPageAuthProvider(@RequestBody AuthPageReqDto authPageReqDto){
        try{
            PageData<AuthProviderDto> pageData = authDubboV2ServiceRpc.queryPageAuthProvider(authPageReqDto);
            PageData<AuthProviderVo> data = new PageData<>();
            data.setTotal(pageData.getTotal());
            data.setPage(pageData.getPage());
            data.setPageSize(pageData.getPageSize());
            List<AuthProviderVo> list = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(pageData.getData())){
                for (AuthProviderDto datum : pageData.getData()) {
                    List<UserInfoResult> userInfoResults = accountServiceRpc.queryUsersByAppName(datum.getAppName());
                    String developers = "";
                    if(CollectionUtils.isNotEmpty(userInfoResults)){
                        developers = userInfoResults.stream().map(UserInfoResult::getName).collect(Collectors.joining(","));
                    }
                    list.add(new AuthProviderVo(datum,developers));
                }
              //  Collections.sort(list);
            }
            data.setData(list);
            return ResultResponse.success(data);
        }catch (Exception e){
            log.error("queryPageAuthProvider 调用异常，authPageReqDto={}", JSON.toJSONString(authPageReqDto),e);
            throw e;
        }
    }
    @ApiOperation(value = "provider 创建可鉴权配置，appId 、appName、authType（1：应用级别，2：接口级别，3：方法级别）不为空。" +
            "authType=2时，dubboService、dubboGroup、dubboVersion不为空。exactMatch（精确匹配true、false）" +
            "authType=3时，dubboService、dubboGroup、dubboVersion、methd不为空。exactMatch（精确匹配true、false）")
    @RequestMapping(value = "/createAuthProvider", method = {RequestMethod.POST})
    public ResultResponse<Boolean> createAuthProvider(@RequestBody AuthProviderDto authProviderDto){
        try{
            CheckPermissUtil.checkPermissByAppName(authProviderDto.getAppName());
            authProviderDto.setOperator(UserInfoThreadLocal.getUserInfo().getUserName());
            return ResultResponse.success(authDubboV2ServiceRpc.createAuthProvider(authProviderDto));
        }catch (Exception e){
            log.error("createAuthProvider 调用异常，authProviderDto={}", JSON.toJSONString(authProviderDto),e);
            throw e;
        }
    }
/*    @RequestMapping(value = "/updateAuthProvider", method = {RequestMethod.POST})
    public ResultResponse<Boolean> updateAuthProvider(@RequestBody AuthProviderDto authProviderDto){
        try{
            CheckPermissUtil.checkPermissByAppName(authProviderDto.getAppName());
            return ResultResponse.success(authDubboV2ServiceRpc.updateAuthProvider(authProviderDto));
        }catch (Exception e){
            log.error("updateAuthProvider 调用异常，authProviderDto={}", JSON.toJSONString(authProviderDto),e);
            throw e;
        }
    }*/
    @ApiOperation(value = "provider 关闭鉴权，id、closed不为空")
    @RequestMapping(value = "/closeAuthProvider", method = {RequestMethod.POST})
    public ResultResponse<Boolean> closeAuthProvider(@RequestBody AuthProviderDto authProviderDto){
        try{
            AuthProviderDto dto = authDubboV2ServiceRpc.getAuthProvider(authProviderDto);
            CheckPermissUtil.checkPermissByAppName(dto.getAppName());
            authProviderDto.setCloseOperator(UserInfoThreadLocal.getUserInfo().getUserName());
            return ResultResponse.success(authDubboV2ServiceRpc.closeAuthProvider(authProviderDto));
        }catch (Exception e){
            log.error("closeAuthProvider 调用异常，authProviderDto={}", JSON.toJSONString(authProviderDto),e);
            throw e;
        }
    }
    @ApiOperation(value = "查询授权记录，分页查询，page 和 pageSize、side（provider或consumer）不为空,providerAppName和consumerAppName不能同时为空")
    @RequestMapping(value = "/queryPageAuthApproval", method = {RequestMethod.POST})
    public ResultResponse<PageData<AuthApprovalVo>> queryPageAuthApproval(@RequestBody AuthPageReqDto authPageReqDto){
        try{
            if(StringUtils.isBlank(authPageReqDto.getProviderAppName()) && StringUtils.isBlank(authPageReqDto.getConsumerAppName())){
                return ResultResponse.fail(500,"providerAppName和consumerAppName不能同时为空");
            }
            if(StringUtils.isBlank(authPageReqDto.getSide())){
                return ResultResponse.fail(500,"side不能为空，为provider或consumer");
            }
            if(SideEnum.provider.name().equals(authPageReqDto.getSide())){
                CheckPermissUtil.checkPermissByAppName(authPageReqDto.getProviderAppName());
            }else {
                CheckPermissUtil.checkPermissByAppName(authPageReqDto.getConsumerAppName());

            }
            PageData<AuthApprovalDto> authApprovalDtoPageData = authDubboV2ServiceRpc.queryPageAuthApproval(authPageReqDto);
            PageData<AuthApprovalVo> pageData = new PageData<>();
            pageData.setPage(authApprovalDtoPageData.getPage());
            pageData.setPageSize(authApprovalDtoPageData.getPageSize());
            pageData.setTotal(authApprovalDtoPageData.getTotal());
            if(CollectionUtils.isNotEmpty(authApprovalDtoPageData.getData())){

                List<AuthApprovalVo> list = new ArrayList<>();
                for (AuthApprovalDto dto : authApprovalDtoPageData.getData()) {
                    AuthApprovalVo authApprovalVo ;
                    if(SideEnum.consumer.name().equals(authPageReqDto.getSide())){
                        List<UserInfoResult> userInfoResults = accountServiceRpc.queryUsersByAppName(dto.getProviderAppName());
                        String developers = CollectionUtils.isEmpty(userInfoResults) ? null : accountServiceRpc.queryUsersByAppName(dto.getProviderAppName()).stream().map(UserInfoResult::getName).collect(Collectors.joining(","));
                        authApprovalVo = new AuthApprovalVo(dto, developers);
                    }else{
                        authApprovalVo = new AuthApprovalVo(dto,null);
                    }
                    list.add(authApprovalVo);
                }
                pageData.setData(list);
            }
            return ResultResponse.success(pageData);
        }catch (Exception e){
            log.error("queryPageAuthApproval 调用异常，authPageReqDto={}", JSON.toJSONString(authPageReqDto),e);
            throw e;
        }
    }
    @ApiOperation(value = "申请授权记录，创建可鉴权配置，consumerAppId、consumerAppName、providerAppId、providerAppName、applicant、authType（1：应用级别，2：接口级别，3：方法级别）不为空。" +
            "authType=2时，dubboService、dubboGroup、dubboVersion不为空。exactMatch（精确匹配true、false）" +
            "authType=3时，dubboService、dubboGroup、dubboVersion、methd不为空。exactMatch（精确匹配true、false）")
    @RequestMapping(value = "/createAuthApproval", method = {RequestMethod.POST})
    public ResultResponse<Boolean> createAuthApproval(@RequestBody AuthApprovalDto authApprovalDto){
        try{
            CheckPermissUtil.checkPermissByAppName(authApprovalDto.getConsumerAppName());
            authApprovalDto.setApplicant(UserInfoThreadLocal.getUserInfo().getUserName());
            return ResultResponse.success(authDubboV2ServiceRpc.createAuthApproval(authApprovalDto));
        }catch (Exception e){
            log.error("createAuthApproval 调用异常，authApprovalDto={}", JSON.toJSONString(authApprovalDto),e);
            throw e;
        }
    }
/*    @RequestMapping(value = "/updateAuthApproval", method = {RequestMethod.POST})
    public ResultResponse<Boolean> updateAuthApproval(@RequestBody AuthApprovalDto authApprovalDto){
        try{
            AuthApprovalDto authApproval = authDubboV2ServiceRpc.getAuthApproval(authApprovalDto);
            CheckPermissUtil.checkPermissByAppName(authApproval.getConsumerAppName());
            return ResultResponse.success(authDubboV2ServiceRpc.updateAuthApproval(authApprovalDto));
        }catch (Exception e){
            log.error("updateAuthApproval 调用异常，authApprovalDto={}", JSON.toJSONString(authApprovalDto),e);
            throw e;
        }
    }*/
    @ApiOperation(value = "变更状态，id、status（1：进行中，2：通过，3：拒绝，4：取消）不为空")
    @RequestMapping(value = "/changeStatusAuthApproval", method = {RequestMethod.POST})
    public ResultResponse<Boolean> changeStatusAuthApproval(@RequestBody AuthApprovalDto authApprovalDto){
        try{
            AuthApprovalDto authApproval = authDubboV2ServiceRpc.getAuthApproval(authApprovalDto);
            boolean canChange = false;
            if(authApproval.getStatus() == AuthStatusEnum.PROGRESS.getCode()){
                if(authApprovalDto.getStatus() == AuthStatusEnum.PASS.getCode() || authApprovalDto.getStatus()== AuthStatusEnum.REJECT.getCode()){
                    CheckPermissUtil.checkPermissByAppName(authApproval.getProviderAppName());
                    authApprovalDto.setApprover(UserInfoThreadLocal.getUserInfo().getUserName());
                    canChange = true;
                }
                if(authApprovalDto.getStatus() == AuthStatusEnum.CANCEL.getCode()){
                    CheckPermissUtil.checkPermissByAppName(authApproval.getConsumerAppName());
                    if(!UserInfoThreadLocal.getUserInfo().getUserName().equals(authApproval.getApplicant())){
                        return ResultResponse.fail(500,"只能由申请人"+authApproval.getApplicant()+"取消");
                    }
                    canChange = true;
                }
            }

            if(authApproval.getStatus() == AuthStatusEnum.REJECT.getCode() || authApprovalDto.getStatus() == AuthStatusEnum.CANCEL.getCode()){
                if(authApprovalDto.getStatus() == AuthStatusEnum.PROGRESS.getCode()){
                    CheckPermissUtil.checkPermissByAppName(authApproval.getConsumerAppName());
                    authApprovalDto.setApplicant(UserInfoThreadLocal.getUserInfo().getUserName());
                    canChange = true;
                }
            }
            if(canChange){
                return ResultResponse.success(authDubboV2ServiceRpc.changeStatusAuthApproval(authApprovalDto));
            }else{
                return ResultResponse.fail(500,"当前状态为"+AuthStatusEnum.getByCode(authApproval.getStatus()).getDesc()+"不允许要修改成"+AuthStatusEnum.getByCode(authApprovalDto.getStatus()).getDesc());
            }
        }catch (Exception e){
            log.error("changeStatusAuthApproval 调用异常，authApprovalDto={}", JSON.toJSONString(authApprovalDto),e);
            throw e;
        }
    }
    @ApiOperation(value = "关闭鉴权，id、closed不为空")
    @RequestMapping(value = "/closeAuthApproval", method = {RequestMethod.POST})
    public ResultResponse<Boolean> closeAuthApproval(@RequestBody AuthApprovalDto authApprovalDto){
        try{
            AuthApprovalDto authApproval = authDubboV2ServiceRpc.getAuthApproval(authApprovalDto);
            CheckPermissUtil.checkPermissByAppName(authApproval.getConsumerAppName());
            if(authApproval.getStatus() != AuthStatusEnum.PASS.getCode() ){
                return ResultResponse.fail(500,"当前不是通过状态，不允许关闭");
            }
            authApprovalDto.setCloseOperator(UserInfoThreadLocal.getUserInfo().getUserName());
            return ResultResponse.success(authDubboV2ServiceRpc.closeAuthApproval(authApprovalDto));
        }catch (Exception e){
            log.error("closeAuthApproval 调用异常，authApprovalDto={}", JSON.toJSONString(authApprovalDto),e);
            throw e;
        }
    }

}
class AuthProviderVo extends AuthProviderDto implements Comparable{
    public AuthProviderVo() {
    }

    public AuthProviderVo(AuthProviderDto dto, boolean applied) {
        BeanUtils.copyProperties(dto,this);
        this.applied = applied;
    }
    public AuthProviderVo(AuthProviderDto dto, String developers) {
        BeanUtils.copyProperties(dto,this);
        this.developers = developers;
    }

    private boolean applied;

    private String developers;

    public boolean isApplied() {
        return applied;
    }

    public void setApplied(boolean applied) {
        this.applied = applied;
    }


    public String getDevelopers() {
        return developers;
    }

    public void setDevelopers(String developers) {
        this.developers = developers;
    }

    @Override
    public int compareTo(Object o) {
        AuthProviderVo other = (AuthProviderVo) o;
        if (this.getAuthType() == other.getAuthType()) {
            if (this.getAuthType() == 2) {
                return this.getDubboService().compareTo(other.getDubboService());
            }
            if (this.getAuthType() == 3) {
                if (this.getDubboService().compareTo(other.getDubboService()) == 0) {
                    return this.getMethod().compareTo(other.getMethod());
                } else {
                    return this.getDubboService().compareTo(other.getDubboService());
                }
            }
        }
        return this.getAuthType().compareTo(other.getAuthType());
    }
}
class AuthApprovalVo extends AuthApprovalDto {
    public AuthApprovalVo() {
    }

    public AuthApprovalVo(AuthApprovalDto dto, String developers) {
        BeanUtils.copyProperties(dto,this);
        this.developers = developers;
    }

    private String developers;

    public String getDevelopers() {
        return developers;
    }

    public void setDevelopers(String developers) {
        this.developers = developers;
    }
}
