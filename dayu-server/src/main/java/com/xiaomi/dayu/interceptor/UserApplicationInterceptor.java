package com.xiaomi.dayu.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.aegis.config.AegisConfig;
import com.xiaomi.aegis.utils.AegisSignUtil;
import com.xiaomi.aegis.vo.UserInfoVO;
import com.xiaomi.dayu.common.CurrentOperatorThreadLocal;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.service.StatisticsService;
import com.xiaomi.dayu.service.UserService;
import com.xiaomi.mone.umami.Umami;
import com.xiaomi.youpin.hermes.bo.ApplicationListBo;
import com.xiaomi.youpin.hermes.entity.Project;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jasig.cas.client.authentication.AttributePrincipalImpl;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;
import org.jasig.cas.client.validation.AssertionImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UserApplicationInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private UserService userService;

    @Autowired
    private AccountServiceRpc accountServiceRpc;

    @Autowired
    private StatisticsService statisticsService;

    @Value("${skip.midun:false}")
    private boolean skipMiDun;
    @Value("${umami.url}")
    private String umamiUrl;
    @Value("${dayu.url:http://dayu.test.mi.com/}")
    private String dayuUrl;
    @Value("${umami.website}")
    private String umamiWebsite;

    @Value("${swimlane.url:http://swimlane.test.mi.com/}")
    private String swimlaneUrl;
    @Value("${umami.swimlane.website}")
    private String swimlaneUmamiWebsite;

    @Value("${server.cas.ignoreUrl}")
    private String ignoreUrl;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String profile = System.getProperty("spring.profiles.active");
        if("dev".equals(profile)){
            dev(request);
            return true;
        }
        if (request.getRequestURI().equals(ignoreUrl)) {
            return true;
        }
        UserInfoVO userInfoVO = (UserInfoVO)request.getAttribute("user-info");
        //Assertion assertion = (Assertion) request.getSession().getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        String head_depart = request.getHeader("depart");
        if(userInfoVO == null){
            return false;
        }
        String username =userInfoVO.getUser();

        String signAndUserSignData = request.getHeader("x-proxy-userdetail");

        String[] publicKeys = AegisConfig.publicKeys;
        int len = publicKeys.length;
        String userJson = "";
        for(int i = 0; i < len; ++i) {
            String key = publicKeys[i];
            String verifyIdentityData = AegisSignUtil.verifySignGetInfo(signAndUserSignData, key);
            if (StringUtils.isNotEmpty(verifyIdentityData)) {
                userJson = verifyIdentityData;
                break;
            }
        }
        JSONObject jsonObject = JSON.parseObject(userJson);
        String uid = jsonObject.getString("cas:uid");
        if(StringUtils.isNotBlank(username)) {
            sendToUmami(username, request);
            statisticsService.addUserView(username);
            Pair<String, Boolean> pair = null;
            try{
                pair = userService.queryDepAndAdminByUid(uid, username);
            }catch (Exception e){
                e.printStackTrace();
            }
            UserInfo userInfovo = new UserInfo();
            String deptId = pair.getLeft();
            Boolean isAdmin = pair.getRight();
            //TODO 信息部判断暂时去掉
            /*if (Integer.parseInt(deptId) == UserService.DeptEnum.MIT.getDetpID()) {
                ApplicationListBo applicationListBo = accountServiceRpc.queryAppNamesByUser(username);
                applicationListBo.setAdmin(applicationListBo.isAdmin());
                userInfovo.setApplicationNames(applicationListBo.getApplicationList().stream().map(Project::getName).collect(Collectors.toList()));

                userInfovo.setApplicationMap(applicationListBo.getApplicationList().stream().collect(Collectors.toMap(Project::getName, Function.identity(), (oldValue, newValue) -> newValue)));
                return true;
            } else {*/
                ApplicationListBo applicationListBo = accountServiceRpc.queryApplicationNamesByUsername(username);
                if (CollectionUtils.isNotEmpty(applicationListBo.getApplicationList())) {
                    userInfovo.setApplicationMap(applicationListBo.getApplicationList().stream().collect(Collectors.toMap(Project::getName, Function.identity(), (oldValue, newValue) -> newValue)));
                    userInfovo.setApplicationNames(applicationListBo.getApplicationList().stream().map(Project::getName).collect(Collectors.toList()));
                } else {
                    userInfovo.setApplicationMap(new HashMap<>());
                    userInfovo.setApplicationNames(new ArrayList<>());
                }
                /*}*/

                userInfovo.setUserName(username);
                userInfovo.setDeptId(Integer.parseInt(deptId));
                userInfovo.setUserId(uid);
                userInfovo.setDeptName(userInfoVO.getDepartmentName());
                userInfovo.setAdmin(isAdmin);
                 String currEnv = request.getHeader("currEnv");
                 if(StringUtils.isBlank(currEnv)){
                     currEnv="china";
                }
                userInfovo.setCurrEnv(currEnv);
                UserInfoThreadLocal.setUserInfo(userInfovo);
                CurrentOperatorThreadLocal.setCurrentOperator(request.getMethod() + request.getRequestURI());
                response.addHeader("depart", deptId);//1：全部，2：中国区，3：有品，
//                response.addHeader("Access-Control-Allow-Origin", "*");
//                response.setHeader("Access-Control-Allow-Headers", "Content-Type, Token, platform");

            return true;
           // }

        }
        return false;
    }

    private void dev(HttpServletRequest request) {
        UserInfo userInfovo = new UserInfo();
        userInfovo.setUserName("liuchuankang");
        userInfovo.setAdmin(true);
        sendToUmami(userInfovo.getUserName(),request );
        ApplicationListBo applicationListBo = accountServiceRpc.queryApplicationNamesByUsername(userInfovo.getUserName());
        if(CollectionUtils.isNotEmpty(applicationListBo.getApplicationList())){
            userInfovo.setApplicationMap(applicationListBo.getApplicationList().stream().collect(Collectors.toMap(Project::getName,Function.identity(), (oldValue,newValue)->newValue)));
            userInfovo.setApplicationNames(applicationListBo.getApplicationList().stream().map(Project::getName).collect(Collectors.toList()));
        }

        UserInfoThreadLocal.setUserInfo(userInfovo);
        HashMap<String, Object> map = new HashMap<>();
        Assertion assertion = new AssertionImpl(new AttributePrincipalImpl("dev"), map);
        request.getSession().setAttribute(AbstractCasFilter.CONST_CAS_ASSERTION,assertion);

    }
    private void sendToUmami(String userName, HttpServletRequest request){
        String requestURI = request.getRequestURI();
        if(requestURI.contains("/api/swimlane")){
            swimlaneUmami(userName);
        }else{
            dayuUmami(userName);
        }
    }

    private void swimlaneUmami(String userName) {
        try {
            Umami.setUrl(umamiUrl);
            Umami.setWebsite(swimlaneUmamiWebsite);
            // 替换成自己的域名就行
            Umami.setReferer(this.swimlaneUrl);
            Umami.setOrigin(this.swimlaneUrl);
            // 第一个参数设置未那个平台, 第二个设置未名字
            String result = Umami.sendEvent("swimlane", userName);
        }catch (Exception e){
            log.error("sendToUmami，调用异常，",e);
            e.printStackTrace();
        }
    }

    private void dayuUmami(String userName) {
        try {
            Umami.setUrl(umamiUrl);
            Umami.setWebsite(umamiWebsite);
            // 替换成自己的域名就行
            Umami.setReferer(this.dayuUrl);
            Umami.setOrigin(this.dayuUrl);
            // 第一个参数设置未那个平台, 第二个设置未名字
            String result = Umami.sendEvent("dayu", userName);
        }catch (Exception e){
            log.error("sendToUmami，调用异常，",e);
            e.printStackTrace();
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserInfoThreadLocal.removeUserInfo();
        CurrentOperatorThreadLocal.removeCurrentOperator();
    }
/*    public static void clearUserInfoCache(){
        userInfoCache.invalidateAll();
    }*/
}
