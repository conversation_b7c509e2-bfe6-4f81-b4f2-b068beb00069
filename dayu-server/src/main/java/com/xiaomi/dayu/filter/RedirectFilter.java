package com.xiaomi.dayu.filter;

import com.xiaomi.dayu.wrapper.DubboAdminHttpServletRequestWrapper;
import org.apache.catalina.connector.Response;
import org.apache.catalina.connector.ResponseFacade;
import org.springframework.http.HttpStatus;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;

public class RedirectFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        if(req.getRequestURI().contains("/nacos/v1")){
            DubboAdminHttpServletRequestWrapper requestWrapper = new DubboAdminHttpServletRequestWrapper(req);
            if("public".equals(request.getParameter("tenant"))){
                requestWrapper.addParameter("tenant","");
            }
            chain.doFilter(requestWrapper, response);
        }else{
            chain.doFilter(request, response);
        }

        HttpServletResponse httpServletResponse= (HttpServletResponse)response;
        String platform = httpServletResponse.getHeader("platform");
        if("miline".equals(platform)){
            if(httpServletResponse.getStatus() == HttpStatus.MOVED_TEMPORARILY.value()){
                try {
                    Class<ResponseFacade>  clz= ResponseFacade.class;
                    Field responseField = clz.getDeclaredField("response");
                    responseField.setAccessible(true);
                    Response response2 = (Response)responseField.get(response);
                    response2.setStatus(HttpStatus.UNAUTHORIZED.value());
                } catch (NoSuchFieldException |IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
