//package com.xiaomi.dayu.filter;
//
//import com.alibaba.fastjson.JSON;
//import com.xiaomi.dayu.common.util.AESEncryptUtils;
//import com.xiaomi.dayu.common.util.CoderUtil;
//import com.xiaomi.dayu.common.util.Constants;
//import org.apache.commons.lang3.StringUtils;
//import org.jasig.cas.client.validation.AssertionImpl;
//
//import javax.servlet.*;
//import javax.servlet.http.Cookie;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpSession;
//import java.io.IOException;
//
//import static org.jasig.cas.client.util.AbstractCasFilter.CONST_CAS_ASSERTION;
//
//public class CookiesParseFilter implements Filter {
//    @Override
//    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws IOException, ServletException {
//        HttpServletRequest httpServletRequest= (HttpServletRequest)request;
//        HttpSession session = httpServletRequest.getSession();
//        if(session.getAttribute(CONST_CAS_ASSERTION) == null && httpServletRequest.getCookies() != null){
//            for (Cookie cookie : httpServletRequest.getCookies()) {
//                if(Constants.COOKIE_USER.equals(cookie.getName()) && StringUtils.isNotBlank(cookie.getValue())){
//                    String decode = CoderUtil.decodeBase64(cookie.getValue());
//                    String decrypt = AESEncryptUtils.decrypt(decode);
//                    AssertionImpl assertion = JSON.parseObject(decrypt, AssertionImpl.class);
//                    httpServletRequest.getSession().setAttribute(CONST_CAS_ASSERTION, assertion);
//                }
//            }
//        }
//
//        chain.doFilter(request, response);
//    }
//
//    public static void main(String[] args) {
//        String ss ="dWxGdXE4NDh4djZDQkdieTZwbTFBN1MxWEV1UGNFY2p5VEloMTlnZjFLZm16akhpenNLai9pUGpFeXhsMlhGS2pnclM3NUlGcUgyUgpwcE4zOWFBNkRFbDhrNm9oREVnWDd4UVJXWm9nY014Yi9wV0xtdE80YUNPSE9Wbk5QeUtKVEhKdFhSRXppQU5GZ0svRklPdy9SeGZ5CmRUbFRjZFJLOVk5cHdCNmtyVGhkQVlwMkNWQW5xeXlJeFRYdGV3STBDdkxyMDBLMFk2ZFRPVTViUVJPSDkwelY3S1J0Tm9UV25jRFkKUkN6bFExRDBXSWdRWTRPQXBZOWFFUkJqdW5KV3A5Z1ZsemkrMDdnVE5QNmtvcEl4UE1McnVTcjVmTFRYaE5JUTYwV3Fqa0JDYmFnQQpwcGVGLzQ1VkRobzNFNlNlQURpOS9meXB2VkFBeTFERE5BdlFOSDd1VHloMEdocUhTR3JUOUFGUWE4anUxWkk1NlM3L2JpVjU3bktOCkVRQlIxKzZndURKOEVhd202b2NWTjFORzNhYjFmUjB6bzVNa2IxZ0h5Y1d3YXMrNmtYZjNpc2RPUGtmNVJ0WTRYTVV0dFZoQ3B4UHEKTkpHSEk1SnRIUnZlR0I1bzhKZjRUdmtZMUptNHc0dWVMdVJUWTFzaGJqUjFmVGdCdEg5SGJLd0x4Zy9mVXVUUWp4RFdTaUFWS2JuMAoxcStKUm5LdzZYdkxBT245S3hEei82Kzh3cGwzT1UwTVRXSUhkNzcwOVIyMnBDenZZeWdjYzJKK2R0M28vQUpPOWx2RjNCM0ZRZUJPClo1VWJFV1I3QWNQeGxwbE5YL1RuTks1N2pIWlFjb3RqQThjelFwMExhVlE5djhPNFN5eDZ0Zz09";
//        String decode = CoderUtil.decodeBase64(ss);
//        String decrypt = AESEncryptUtils.decrypt(decode);
//        System.out.println(decrypt);
//    }
//
//}
