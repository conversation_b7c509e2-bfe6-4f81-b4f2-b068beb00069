package com.xiaomi.dayu.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/1/5
 */
@Slf4j
public class ReqAndRespLogFilter extends OncePerRequestFilter {
    private static final List<MediaType> VISIBLE_TYPES = Arrays.asList(
            MediaType.valueOf("text/*"),
            MediaType.APPLICATION_FORM_URLENCODED,
            MediaType.APPLICATION_JSON,
            MediaType.APPLICATION_XML,
            MediaType.valueOf("application/*+json"),
            MediaType.valueOf("application/*+xml"),
            MediaType.MULTIPART_FORM_DATA
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (isAsyncDispatch(request)) {
            filterChain.doFilter(request, response);
        } else {
            doFilterWrapped(wrapRequest(request), wrapResponse(response), filterChain);
        }
    }

    protected void doFilterWrapped(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response, FilterChain filterChain) throws ServletException, IOException {
        try {
            beforeRequest(request, response);
            filterChain.doFilter(request, response);
        } finally {
            afterRequest(request, response);
            response.copyBodyToResponse();
        }
    }

    protected void beforeRequest(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response) {
        if (log.isInfoEnabled()) {
            String prefix = request.getRemoteAddr() + "|> " + request.getMethod() + " " + request.getRequestURI();
            logRequestHeader(request, prefix);
            logRequestBody(request, prefix);
        }
    }

    protected void afterRequest(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response) {
        if (log.isInfoEnabled()) {
            logResponse(response, request.getRemoteAddr() + "|< " + request.getMethod() + " " + request.getRequestURI());
        }
    }

    private static void logRequestHeader(ContentCachingRequestWrapper request, String prefix) {
        val queryString = request.getQueryString();
        List<String> list = Collections.list(request.getHeaderNames());
        Map<String, String> dict = new HashMap<>();

        list.forEach(headerName ->
                Collections.list(request.getHeaders(headerName)).forEach(headerValue ->
                        dict.put(headerName, headerValue)
                ));
        try {
            log.info("{}{}\n{}", prefix, queryString == null ? "" : "?" + queryString,
                    new ObjectMapper().writeValueAsString(dict));
        } catch (JsonProcessingException e) {
            log.error(prefix + (queryString == null ? "" : "?" + queryString), e);
        }
    }

    private static void logRequestBody(ContentCachingRequestWrapper request, String prefix) {
        byte[] content = request.getContentAsByteArray();

        if (content.length > 0) {
            logContent(content, request.getContentType(), request.getCharacterEncoding(), prefix);
            return;
        }
        StringBuilder sb = new StringBuilder();

        Collections.list(request.getParameterNames()).forEach(key -> {
            String value = request.getParameter(key);

            if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                sb.append(key).append(": ").append(value).append(", ");
            }
        });
        if (sb.length() > 0) {
            logContentString(sb.toString(), request.getContentType(), prefix);
        }
    }

    private static void logResponse(ContentCachingResponseWrapper response, String prefix) {
        int status = response.getStatus();
        String reason;

        try {
            reason = HttpStatus.valueOf(status).getReasonPhrase();
        } catch (Exception e) {
            reason = "error from special http status " + status;
        }
        if (status < 400) {
            log.info("{} {} {}", prefix, status, reason);
        } else if (status < 500) {
            log.warn("{} {} {}", prefix, status, reason);
        } else {
            log.error("{} {} {}", prefix, status, reason);
        }
        val content = response.getContentAsByteArray();
        if (content.length > 0) {
            logContent(content, response.getContentType(), response.getCharacterEncoding(), prefix);
        }
    }

    private static void logContent(byte[] content, String contentType, String contentEncoding, String prefix) {
        boolean visible = contentType == null || VISIBLE_TYPES.stream().anyMatch(
                visibleType -> visibleType.includes(MediaType.valueOf(contentType))
        );

        if (visible) {
            String contentString = null;
            contentEncoding = "ISO-8859-1".equals(contentEncoding) ? "UTF-8":contentEncoding;
            try {
                contentString = new String(content, contentEncoding);
            } catch (UnsupportedEncodingException ignored) {
            } finally {
                log.info("{} [{} bytes content]\n{}", prefix, content.length, contentString == null ? content : contentString);
            }
        } else {
            log.info("{} [{} bytes content]\n", prefix, content.length);
        }
    }

    private static void logContentString(String content, String contentType, String prefix) {
        boolean visible = contentType == null || VISIBLE_TYPES.stream().anyMatch(
                visibleType -> visibleType.includes(MediaType.valueOf(contentType))
        );
        log.info("{} [{} bytes content]\n" + (visible ? content : ""), prefix, content.length());
    }

    private static ContentCachingRequestWrapper wrapRequest(HttpServletRequest request) {
        if (request instanceof ContentCachingRequestWrapper) {
            return (ContentCachingRequestWrapper) request;
        } else {
            return new ContentCachingRequestWrapper(request);
        }
    }

    private static ContentCachingResponseWrapper wrapResponse(HttpServletResponse response) {
        if (response instanceof ContentCachingResponseWrapper) {
            return (ContentCachingResponseWrapper) response;
        } else {
            return new ContentCachingResponseWrapper(response);
        }
    }
}
