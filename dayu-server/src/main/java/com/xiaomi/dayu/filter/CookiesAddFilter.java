//package com.xiaomi.dayu.filter;
//
//import com.alibaba.fastjson.JSON;
//import com.xiaomi.dayu.common.util.AESEncryptUtils;
//import com.xiaomi.dayu.common.util.CoderUtil;
//import com.xiaomi.dayu.common.util.Constants;
//import org.apache.commons.lang3.StringUtils;
//import org.jasig.cas.client.validation.Assertion;
//
//import javax.servlet.*;
//import javax.servlet.http.Cookie;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
//import static org.jasig.cas.client.util.AbstractCasFilter.CONST_CAS_ASSERTION;
//
//public class CookiesAddFilter implements Filter {
//    @Override
//    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws IOException, ServletException {
//
//
//        HttpServletRequest httpServletRequest= (HttpServletRequest)request;
//        HttpServletResponse httpServletResponse= (HttpServletResponse)response;
//        boolean haveCAS = false;
//        if(httpServletRequest.getCookies() != null){
//            for (Cookie cookie : httpServletRequest.getCookies()) {
//                if(Constants.COOKIE_USER.equals(cookie.getName()) && StringUtils.isNotBlank(cookie.getValue())){
//                    haveCAS = true;
//                    continue;
//                }
//            }
//            if(!haveCAS){
//                Assertion assertion = (Assertion)httpServletRequest.getSession().getAttribute(CONST_CAS_ASSERTION);
//                if(assertion != null){
//                    String userInfoStr = JSON.toJSONString(assertion);
//                    String encrypt = AESEncryptUtils.encrypt(userInfoStr);
//                    httpServletResponse.addCookie(new Cookie(Constants.COOKIE_USER, CoderUtil.encodeBase64(encrypt)));
//                }
//            }
//        }
//        chain.doFilter(request, response);
//    }
//
//}
