package com.xiaomi.dayu.constants;

/**
 * created by MIT
 *
 * <AUTHOR>
 * @date 2021/12/3
 */
public class Constants {
    /**
     * 不使用米盾而使用header中的userName获取账号信息的参数
     */
    public static final String SKIP_MI_DUN_USER_NAME = "mone-skip-mi-dun-username";

    public static final long DEFAULT_RPC_TIMEOUT_MS = 6000;

    public static final String API_PREFIX = "/api";

    public static final String NACOS_PREFIX = "/nacos/v1/cs";

    public static final String NACOS_CONSOLE_PREFIX = "/nacos/v1/console";

    public static final String NACOS_BASE_PATH = API_PREFIX + NACOS_PREFIX;

    public static final String CONFIG_CONTROLLER_PATH = NACOS_BASE_PATH + "/configs";

    public static final String HISTORY_CONTROLLER_PATH = NACOS_BASE_PATH + "/history";

    public static final String NACOS_NAMING_CONTEXT = API_PREFIX+"/nacos/v1/ns/catalog";

    public static final String NAMESPACE_CONTROLLER_PATH = API_PREFIX + NACOS_CONSOLE_PREFIX + "/namespaces";
}
