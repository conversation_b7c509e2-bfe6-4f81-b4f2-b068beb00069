package com.xiaomi.dayu.utils;

import java.util.concurrent.TimeUnit;

public enum STaskDef {
    HttpTask("httpTask", 1, 100, TimeUnit.SECONDS.toMillis(10L)),
    DubboTask("dubboTask", 1, 100, TimeUnit.SECONDS.toMillis(10L)),
    SqlTask("sqlTask", 1, 1, TimeUnit.SECONDS.toMillis(10L)),
    ShellTask("shellTask", 1, 1, TimeUnit.SECONDS.toMillis(10L)),
    ScriptTask("scriptTask", 1, 1, TimeUnit.SECONDS.toMillis(10L));

    public String type;
    public int retryNum;
    public int errorRetryNum;
    public long timeOut;

    private STaskDef(String type, int retryNum, int errorRetryNum, long timeOut) {
        this.type = type;
        this.retryNum = retryNum;
        this.errorRetryNum = errorRetryNum;
        this.timeOut = timeOut;
    }

}
