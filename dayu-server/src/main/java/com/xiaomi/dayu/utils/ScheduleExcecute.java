package com.xiaomi.dayu.utils;

import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.schedule.task.TaskDefBean;
import com.xiaomi.data.push.schedule.task.TaskParam;
import com.xiaomi.dayu.bo.request.DubboParams;
import com.xiaomi.dayu.bo.request.HttpParams;
import com.xiaomi.dayu.bo.request.HttpTaskParam;
import com.xiaomi.dayu.bo.request.Task;
import com.xiaomi.youpin.mischedule.MethodInfo;
import com.xiaomi.youpin.mischedule.STaskDef;
import com.xiaomi.youpin.mischedule.api.service.ScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ScheduleExcecute {

    @Reference(check = false, group = "${rpc.mischedule.dubbo.group}", interfaceClass = ScheduleService.class, retries = 0,timeout = 6000)
    private ScheduleService scheduleService;

    private static final String DAYU_MC_FLAG = "-dayu";
//    @Autowired
//    private DubboTask dubboTask;

    /**
     * path: /tmp/
     * cmd: ls
     */
//    public void shellExcecute(ShellParams shellParams, String cron, int gid) {
//        TaskParam taskParam = new TaskParam();
//        taskParam.setGid(gid);
//        taskParam.setCron(cron);
//        taskParam.setTaskDef(new TaskDefBean(STaskDef.ShellTask));
//        taskParam.put("path", "/tmp/");
//        taskParam.put("cmd", "ls");
//
//        scheduleService.submitTask(taskParam);
//    }

    /**
     * sql: select version()
     * username: shop_x
     * password: grmtsH4jjSBceLPfLUkmL7eqvmOWGzwq
     * url: *******************************************************************************
     * driveclassname: com.mysql.jdbc.Driver
     * type: query
     */
//    public void sqlExcecute(SqlParams sqlParams, String cron, int gid) {
//        TaskParam taskParam = new TaskParam();
//        taskParam.setGid(gid);
//        taskParam.setTaskDef(new TaskDefBean(STaskDef.SqlTask));
//        taskParam.setCron(cron);
//
//        SqlTaskParam sqlTaskParam = new SqlTaskParam();
//        sqlTaskParam.setSql(sqlParams.getSql());
//        sqlTaskParam.setUsername(sqlParams.getUsername());
//        sqlTaskParam.setPasswd(sqlParams.getPassword());
//        sqlTaskParam.setUrl(sqlParams.getUrl());
//        sqlTaskParam.setDriverClassName(sqlParams.getDriverClassName());
//        // sqlTaskParam.setParams("");
//        sqlTaskParam.setType(sqlParams.getType());
//        taskParam.put("param", new Gson().toJson(sqlTaskParam));
//
//        scheduleService.submitTask(taskParam);
//    }

    /**
     * url: http://www.baidu.com
     * method: get | post
     * headers
     */
    public void httpExcecute(String name, HttpParams httpParams, int dErrorRetryNum, String cron, long excuteTime, Task task,String username) {
        log.info("httpParams: [{}]", httpParams);
        TaskParam taskParam = new TaskParam();

        HttpTaskParam httpTaskParam = new HttpTaskParam();
        httpTaskParam.setMethodType(httpParams.getMethod());
        httpTaskParam.setUrl(httpParams.getUrl());
        httpTaskParam.setHeaders(Maps.newHashMap());
        httpTaskParam.setBody(httpParams.getBody());

        TaskDefBean taskDefBean = new TaskDefBean(STaskDef.HttpTask);
        taskDefBean.setName(name);
        taskDefBean.setType(STaskDef.HttpTask.type);
        taskDefBean.setErrorRetryNum(dErrorRetryNum);

        taskParam.setAlarmUsername(task.getAlarmUsername());
        taskParam.setAlarmGroup(task.getAlarmGroup());
        taskParam.setAlarmLevel(task.getAlarmLevel());

        //所属应用
        taskParam.setBizId(task.getApplicationName() + DAYU_MC_FLAG);
        taskParam.setCron(cron);
        taskParam.setExecuteTime(excuteTime);
        taskParam.setTaskDef(taskDefBean);
        taskParam.setUrl(httpParams.getUrl());
        taskParam.setCreator(username);
        if (StringUtils.isNotBlank(task.getTimeout())) {
            taskParam.setTimeout(Integer.parseInt(task.getTimeout()));
        }
        HashMap<String, String> param = new HashMap<>();
        String interceptor = Optional.ofNullable(httpParams.getInterceptorName()).orElse("");
        param.put("interceptor", interceptor);

        // 新增四个属性http
        param.put("email", task.getHttpParams().getEmail());
        param.put("feishu", task.getHttpParams().getFeishu());
        param.put("history", task.getHttpParams().getHistory());
        taskParam.setParam(param);

        taskParam.put("param", new Gson().toJson(httpTaskParam));
        //提交 http 任务
        scheduleService.submitTask(taskParam);
    }


    /**
     * script: def concat(a,b){a+b}
     * function concat
     * params a,b
     */
//    public void scriptExcecute(ScriptParams scriptParams, String cron, int gid) {
//        TaskParam taskParam = new TaskParam();
//        taskParam.setGid(gid);
//        taskParam.setCron(cron);
//        taskParam.setTaskDef(new TaskDefBean(STaskDef.ScriptTask));
//        taskParam.put("script", scriptParams.getScript());
//        taskParam.put("function", scriptParams.getFunction());
//        taskParam.put("params", scriptParams.getParams());
//
//        scheduleService.submitTask(taskParam);
//    }
    public void dubboExcecute(String name, DubboParams dubboParams, int errorRetryNum, String cron, Task task,String username) {
        TaskParam taskParam = new TaskParam();

        TaskDefBean taskDefBean = new TaskDefBean(STaskDef.DubboTask);
        taskDefBean.setName(name);
        taskDefBean.setType(STaskDef.DubboTask.type);
        taskDefBean.setErrorRetryNum(errorRetryNum);
        taskParam.setCron(cron);
        taskParam.setTaskDef(taskDefBean);
        taskParam.setCreator(username);
        //所属应用
        taskParam.setBizId(task.getApplicationName() + DAYU_MC_FLAG);

        MethodInfo methodInfo = new MethodInfo();
        methodInfo.setServiceName(dubboParams.getServiceName());
        methodInfo.setMethodName(dubboParams.getMethodName());
        methodInfo.setGroup(dubboParams.getGroup());
        methodInfo.setRetries(dubboParams.getRetries());
        methodInfo.setIsOneway(dubboParams.getIsOneway());
        methodInfo.setShardBroadcast(dubboParams.getShardBroadcast());

        taskParam.setAlarmUsername(task.getAlarmUsername());
        taskParam.setAlarmGroup(task.getAlarmGroup());
        taskParam.setAlarmLevel(task.getAlarmLevel());

        if (StringUtils.isNotEmpty(dubboParams.getVersion())) {
            methodInfo.setVersion(dubboParams.getVersion());
        }
        if (StringUtils.isEmpty(dubboParams.getParameterTypes())) {
            methodInfo.setParameterTypes(new String[]{});
        } else {
            String[] types = new Gson().fromJson(dubboParams.getParameterTypes(), new TypeToken<String[]>() {
            }.getType());
            methodInfo.setParameterTypes(types);
        }
        if (StringUtils.isNotEmpty(dubboParams.getArgs())) {
            Object[] params = new Gson().fromJson(dubboParams.getArgs(), new TypeToken<Object[]>() {
            }.getType());
            if (params != null) {
                methodInfo.setArgs(params);
            } else {
                methodInfo.setArgs(new Object[]{});
            }
        }

        if (StringUtils.isNotBlank(task.getTimeout())) {
            methodInfo.setTimeout(Integer.parseInt(task.getTimeout()));
        }
        HashMap<String, String> param = new HashMap<>();

        String interceptor = Optional.ofNullable(dubboParams.getInterceptorName()).orElse("");
        param.put("interceptor", interceptor);
        // 新增三个属性 dubbo
        param.put("email", task.getDubboParams().getEmail());
        param.put("feishu", task.getDubboParams().getFeishu());
        param.put("history", task.getDubboParams().getHistory());
        taskParam.setParam(param);
        taskParam.put("param", new Gson().toJson(methodInfo));
        log.info("taskParam: {}", taskParam);
        scheduleService.submitTask(taskParam);
    }
}
