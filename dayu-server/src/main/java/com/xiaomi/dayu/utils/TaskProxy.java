package com.xiaomi.dayu.utils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.dayu.bo.request.Cron;
import com.xiaomi.dayu.bo.response.TaskBo;
import com.xiaomi.youpin.cron.CronExpression;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import com.xiaomi.youpin.mischedule.api.service.ScheduleService;
import com.xiaomi.youpin.mischedule.api.service.TaskHistoryService;
import com.xiaomi.youpin.mischedule.api.service.bo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class TaskProxy {

    @Reference(check = false, group = "${rpc.mischedule.dubbo.group}", interfaceClass = ScheduleService.class,timeout = 60000,retries = 0)
    private ScheduleService scheduleService;

    @Reference(check = false,interfaceClass = TaskHistoryService.class,group = "${rpc.mischedule.dubbo.group}",timeout = 6000)
    private TaskHistoryService taskHistoryService;

    public static final Gson gson = new Gson();



    public Pair<List<TaskBo>, Integer> queryList(int pageIndex, int pageSize,TaskQryParam taskQryParam){
        List<TaskBo> taskBos = new ArrayList<>();
        Pair<List<TaskDo>, Integer> rt = scheduleService.getTaskList(pageIndex, pageSize, taskQryParam).getData();
//        Pair<List<TaskDo>, Integer> rt = scheduleService.getTaskListByBizId(pageIndex,pageSize,bid+DAYU_MC_FLAG).getData();
        List<TaskDo> taskDos = rt.getLeft();
        taskDos.forEach(taskDo -> {
            TaskBo taskBo = new TaskBo();
            BeanUtils.copyProperties(taskDo,taskBo);
            Cron cron = gson.fromJson(taskDo.getParams(), new TypeToken<Cron>() {
            }.getType());
            try {
                taskBo.setCronList(this.parse(cron.getCron()));
            } catch (ParseException e ) {
                log.error("parse cron error id={},con={}",taskDo.getId(),cron.getCron());
            }catch (Exception e){
                log.error("parse cron exception id={},con={}",taskDo.getId(),cron.getCron());

            }
            taskBos.add(taskBo);
        });
        return Pair.of(taskBos, rt.getRight());
    }

    public Result<Boolean> taskStart(Integer taskId) {
        return scheduleService.start(taskId);
    }

    public Result<Boolean> taskPause(Integer taskId) {
        return scheduleService.pause(taskId);
    }
    public Result<Boolean> delTask(Integer taskId) {
        return scheduleService.delTask(taskId);
    }

    public Result<String> manualExecute(Integer taskId) {
        try{
            return scheduleService.manualExecute(taskId);
        }catch (RpcException e){
            log.error("手动执行任务异常，taskid={}，异常信息={}",taskId,e.getMessage());
            return  Result.fail(GeneralCodes.InternalError,e.getCode()== RpcException.TIMEOUT_EXCEPTION ? "请去历史任务查看执行结果" : "服务端异常");
        }
    }

    public Task getTaskById(int taskId) {
        return scheduleService.getTask(taskId).getData();
    }

    public TaskDo getTaskById2(int taskId) {
        return scheduleService.getTask2(taskId).getData();
    }

    public Result<Boolean> modifyTaskName(int id, String taskName) {
        return scheduleService.editTaskName(id,taskName);
    }

    public Result<Boolean> modifyTask(int id, String taskParam) {
        return scheduleService.modify(id,taskParam);
    }


    public PageInfo<TaskHistoryBo> taskHistory(Integer page, Integer pageSize, Integer taskId) {
        return taskHistoryService.pageTaskHistory(pageSize, page, taskId);
    }

    public List<Date> parse(String cron) throws ParseException {
        CronExpression cronExpression = new CronExpression(cron);

        Date t1 = cronExpression.getNextValidTimeAfter(new Date());
        Date t2 = cronExpression.getNextValidTimeAfter(t1);
        Date t3 = cronExpression.getNextValidTimeAfter(t2);
        Date t4 = cronExpression.getNextValidTimeAfter(t3);
        Date t5 = cronExpression.getNextValidTimeAfter(t4);

        return Arrays.asList(t1, t2, t3, t4, t5);
    }
}
