package com.xiaomi.dayu.bo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/4/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChangeArthasMemberReq implements Serializable {

    @NotBlank
    private String appName;

    @NotNull
    private Integer privilege;

    @NotEmpty
    private List<String> assignees;
}
