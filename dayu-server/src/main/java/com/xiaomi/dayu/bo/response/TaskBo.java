package com.xiaomi.dayu.bo.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TaskBo {
    private int id;
    private String name;
    private long created;
    private String creator;
    private long updated;
    private String params;
    String result;
    private int retryNum;
    private int success_num;
    private int failure_num;
    private long nextRetryTime;
    private int errorRetryNum;
    private int status;
    private String context;
    private String type;
    private Integer gid;
    private List<Date> cronList;
}
