package com.xiaomi.dayu.bo.request;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DubboParams {
    private  String version;
    private String serviceName;
    private String methodName;
    private String group;
    private Integer retries;
    private Boolean isOneway;
    /**
     * 分片广播
     */
    private Boolean shardBroadcast;

    private String parameterTypes;
    private String args;

    private String email;
    private String feishu;
    private String history;

    /**
     * 拦截器
     */
    private String interceptorName;
}
