package com.xiaomi.dayu.bo.request;

import lombok.Data;

@Data
public class Task {
    private Integer id;

    private String name;

    /**
     * dubboTask;
     * httpTask;
     */
    private String type;

    private String result;

    private String creator;

    String applicationName;

    /**
     * taskDef 的errorRetryNum
     */
    private int defineErrorRetryNum;

    private Boolean needEmail;
    private long executeTime;

    private String timeout;
    private String cron;
    private HttpParams httpParams;
    private DubboParams dubboParams;

    private String alarmUsername;
    private String alarmGroup;
    private Integer alarmLevel;
}
