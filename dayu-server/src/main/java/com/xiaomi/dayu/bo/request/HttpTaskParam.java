package com.xiaomi.dayu.bo.request;

import lombok.Data;

import java.util.Map;
import java.util.Objects;

@Data
public class HttpTaskParam {
    private String url;
    private String methodType;
    private String body;
    private Map<String, String> headers;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HttpTaskParam that = (HttpTaskParam) o;
        return Objects.equals(url, that.url) && Objects.equals(methodType, that.methodType) && Objects.equals(body, that.body) && Objects.equals(headers, that.headers);
    }

    @Override
    public int hashCode() {
        return Objects.hash(url, methodType, body, headers);
    }
}
