package com.xiaomi.dayu.bo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/5/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BindScopeReq implements Serializable {

    @NotNull
    private Integer scopeId;

    @NotEmpty
    private List<Integer> projectIds;

    @NotNull
    private Integer action;
}
