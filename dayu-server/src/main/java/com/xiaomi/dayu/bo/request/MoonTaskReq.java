package com.xiaomi.dayu.bo.request;

import lombok.Data;

@Data
public class MoonTaskReq {
    private String name; // 任务名
    private String description; // 任务描述
    private long projectID; // 绑定的任务ID
    private String type; // 任务类型 见 TaskTypeEnum 
    private String execMode; // 执行模式 见 ExecModeEnum
    private String priority; // 0-4 四个级别
    private String execParam; // 执行任务时传给任务的参数
    private int retryWait; // 重试间隔，仅faas类型有效
    private int concurrency; // 并发数，仅faas类型有效
    private String machine; // 指定ip调用，仅dubbo类型有效
    private String scheduleMode; // 调度类型 cron fix-rate second-delay singel-shot等 见 ScheduleModeEnum
    private String scheduleParam; // 调度参数 scheduleMode类型的设置参数
    private String concurrencyStrategy; // 调度并发冲突处理策略 见 ConcurrencyStrategyEnum

    // 报警信息
    private Boolean alertTimeout; // 是否开启超时报警
    private String alertTimeoutLevel; // 超时报警级别
    private Long timeout; // 超时报警的超时时间(s) 默认7200
    private Boolean timeoutHalt; // 超时终止调度 
    private Boolean alertSuccess; // 是否开启成功通知
    private String alertSuccessLevel; 
    private Boolean alertFail; // 是否开启失败通知
    private String alertFailLevel; 
    private Boolean alertStop; // 失败停止报警
    private String alertStopLevel; 
    private int maxRetry; // 任务最大连续失败次数,小于0时不限制
    private Boolean alertNoMachine; // 无可用机器报警
    private String alertNoMachineLevel; 
    private String alertConfig; // 绑定的通知组id
    private Integer historyKeep; // 运行历史保留天数，默认7天
    
    // 各类型配置，根据需求填写，参数描述在后面
    private MoonFaasParam faasParam;
    private MoonHttpParam httpParam;
    private MoonDubboParam dubboParam;
    
    // 其他参数
    private Long id; // 任务id，创建时留空，更新等操作需要传
    private Long mischeduleID; // 内部变量，创建时留空，更新等操作需要传
    
    private String userName; // 内部变量不用管
}