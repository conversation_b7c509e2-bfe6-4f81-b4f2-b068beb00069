package com.xiaomi.dayu.bo.request;

import lombok.Data;

import java.io.Serializable;
@Data
public class MoonDubboParam implements Serializable {
    private String version; // dubbo服务版本
    private String serviceName; // dubbo服务名
    private String methodName; // dubbo方法名
    private String group; // dubbo服务的组名
    private Integer retries; // 单次失败重试次数
    private Boolean isOneway; // 异步调用，不返回调用结果
    private String parameterTypes; // dubbo调用参数类型
    private String responseParseMode; // 结果解析模式 见ResultParseEnum
    private String responseParseParams; // 结果解析的参数
}