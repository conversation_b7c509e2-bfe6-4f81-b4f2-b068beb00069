/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xiaomi.dayu.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

@Configuration
public class SwaggerConfiguration {
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("servicemanage")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.servicemanage"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket gatewayRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("iauth")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.iauth"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket nacosRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("nacos")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.nacos"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket sentinelRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("sentinel")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.sentinel"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket dumpRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("dump")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.dump"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket approvalRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("approval")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.approval"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket scheduleRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("schedule")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.schedule"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket teslaAuthRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("teslaauth")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.teslaauth"))
                .paths(PathSelectors.any())
                .build();
    }
    @Bean
    public Docket slaApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("sla")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xiaomi.dayu.controller.sla"))
                .paths(PathSelectors.any())
                .build();
    }




    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("dayu swagger api")
                .version("1.0")
                .build();
    }

}
