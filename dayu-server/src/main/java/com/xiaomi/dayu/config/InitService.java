package com.xiaomi.dayu.config;

import com.xiaomi.data.push.nacos.NacosNaming;
import com.xiaomi.dayu.registry.config.GovernanceConfiguration;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.utils.NetUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class InitService {

    @Value("${server.port}")
    private String httpPort;
    @Value("${dubbo.application.name}")
    private String appName;
    @Autowired
    private NacosNaming nacosNaming;
    @Autowired
    private GovernanceConfiguration governanceConfiguration;

    @PostConstruct
    public void init() {
        String host = System.getenv("host.ip") == null ? NetUtils.getLocalHost() : System.getenv("host.ip");
        try {
            String profile = System.getProperty("spring.profiles.active");
            URL url = governanceConfiguration.getUrl();
            String group = url.getParameter("group");
            String instanceName = appName+"_"+profile;
            nacosNaming.registerInstance(instanceName, host, Integer.valueOf(httpPort), group);

            Runtime.getRuntime().addShutdownHook(new Thread(()->{
                try {
                    System.out.println("stop");
                    nacosNaming.deregisterInstance(instanceName, host, Integer.valueOf(httpPort), group);
                } catch (Exception e) {
                    //ignore
                }
            }));
        } catch (Exception e) {
        }
    }
}