package com.xiaomi.dayu.config;

import com.xiaomi.data.push.nacos.NacosConfig;
import com.xiaomi.data.push.nacos.NacosNaming;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class NacosConfiguration {
    @Value("${dubbo.registry.address}")
    private String nacosAddress;

//    @Bean
//    public NacosNaming nacosNaming() {
//        NacosNaming nacosNaming = new NacosNaming();
//        String[] address = nacosAddress.split("//");
//        nacosNaming.setUsername("nacos");
//        nacosNaming.setPassword("nacos");
//
//        nacosNaming.setServerAddr(address[1]);
//        nacosNaming.init();
//        return nacosNaming;
//    }
    @Bean
    public NacosConfig nacosConfig() {
        NacosConfig nacosConfig = new NacosConfig();
        String[] address = nacosAddress.split("//");
        nacosConfig.setServerAddr(address[1]);
        nacosConfig.init();
        return nacosConfig;
    }

}
