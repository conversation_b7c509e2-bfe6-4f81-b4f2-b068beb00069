package com.xiaomi.dayu.config;

import com.xiaomi.mone.current.threadpool.MoneThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Type ThreadPoolConfig.java
 * @Desc
 * @date 2024/6/17 15:19
 */
@Configuration
public class ThreadPoolConfig {

    @Bean(destroyMethod = "destroy")
    public MoneThreadPoolExecutor moneThreadPoolExecutor(){
        return new MoneThreadPoolExecutor("dayu", "common-threadpool");
    }
}
