/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xiaomi.dayu.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.api.naming.pojo.ListView;
import com.google.common.base.Stopwatch;
import com.xiaomi.data.push.nacos.NacosNaming;
import com.xiaomi.dayu.common.exception.ConfigurationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.registry.config.GovernanceConfiguration;
import com.xiaomi.dayu.registry.metadata.MetaDataCollector;
import com.xiaomi.dayu.registry.metadata.impl.NoOpMetadataCollector;
import com.xiaomi.dayu.rpc.OperateLogServiceRpc;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.extension.ExtensionLoader;
import org.apache.dubbo.common.logger.Logger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.apache.dubbo.registry.Registry;
import org.apache.dubbo.registry.RegistryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.apache.dubbo.common.constants.CommonConstants.CLUSTER_KEY;
@Configuration
public class ConfigCenter {



    //centers in dubbo 2.7
    @Value("${admin.config-center:}")
    private String configCenter;

    @Value("${admin.registry.address:}")
    private String registryAddress;

    @Value("${admin.metadata-report.address:}")
    private String metadataAddress;

    @Value("${admin.metadata-report.cluster:false}")
    private boolean cluster;

    @Value("${admin.registry.group:dubbo}")
    private String registryGroup;

    @Value("${admin.config-center.group:dubbo}")
    private String configCenterGroup;

    @Value("${admin.metadata-report.group:}")
    private String metadataGroup;

    @Value("${admin.registry.namespace:public}")
    private String registryNameSpace;

    @Value("${admin.config-center.namespace:dubbo}")
    private String configCenterGroupNameSpace;

    @Value("${admin.metadata-report.namespace:dubbo}")
    private String metadataGroupNameSpace;

    @Value("${admin.config-center.username:}")
    private String username;
    @Value("${admin.config-center.password:}")
    private String password;

    @NacosValue(value = "${nacos.login.url}",autoRefreshed = true)
    private String nacosLoginUrl;

    private static final Logger logger = LoggerFactory.getLogger(ConfigCenter.class);

    private URL configCenterUrl;
    private URL registryUrl;
    private URL metadataUrl;

    @Autowired
    private OperateLogServiceRpc operateLogServiceRpc;

    /*
     * generate dynamic configuration client
     */
    @Bean("governanceConfiguration")
    GovernanceConfiguration getDynamicConfiguration() {
        GovernanceConfiguration dynamicConfiguration = null;

        if (StringUtils.isNotEmpty(configCenter)) {
            configCenterUrl = formUrl(configCenter, configCenterGroup, configCenterGroupNameSpace, username, password);
            dynamicConfiguration = ExtensionLoader.getExtensionLoader(GovernanceConfiguration.class).getExtension(configCenterUrl.getProtocol());
            dynamicConfiguration.setUrl(configCenterUrl);
            dynamicConfiguration.init();
            String config = dynamicConfiguration.getConfig(Constants.GLOBAL_CONFIG_PATH);

            if (StringUtils.isNotEmpty(config)) {
                Arrays.stream(config.split("\n")).forEach( s -> {
                    if(s.startsWith(Constants.REGISTRY_ADDRESS)) {
                        String registryAddress = s.split("=")[1].trim();
                        registryUrl = formUrl(registryAddress, registryGroup, registryNameSpace, username, password);
                    } else if (s.startsWith(Constants.METADATA_ADDRESS)) {
                        metadataUrl = formUrl(s.split("=")[1].trim(), metadataGroup, metadataGroupNameSpace, username, password);
                    }
                });
            }
        }
        if (dynamicConfiguration == null) {
            if (StringUtils.isNotEmpty(registryAddress)) {
                registryUrl = formUrl(registryAddress, registryGroup, registryNameSpace, username, password);
                dynamicConfiguration = ExtensionLoader.getExtensionLoader(GovernanceConfiguration.class).getExtension(registryUrl.getProtocol());
                dynamicConfiguration.setUrl(registryUrl);
                dynamicConfiguration.init();
                logger.warn("you are using dubbo.registry.address, which is not recommend, please refer to: https://github.com/apache/incubator-dubbo-admin/wiki/Dubbo-Admin-configuration");
            } else {
                throw new ConfigurationException("Either config center or registry address is needed, please refer to https://github.com/apache/incubator-dubbo-admin/wiki/Dubbo-Admin-configuration");
                //throw exception
            }
        }
        return dynamicConfiguration;
    }

    /*
     * generate registry client
     */
    @Bean
    @Order(-10000)
    @DependsOn("governanceConfiguration")
    Registry getRegistry() {
        Registry registry = null;
        if (registryUrl == null) {
            if (StringUtils.isBlank(registryAddress)) {
                throw new ConfigurationException("Either config center or registry address is needed, please refer to https://github.com/apache/incubator-dubbo-admin/wiki/Dubbo-Admin-configuration");
            }
            registryUrl = formUrl(registryAddress, registryGroup, registryNameSpace, username, password);
        }
        RegistryFactory registryFactory = ExtensionLoader.getExtensionLoader(RegistryFactory.class).getAdaptiveExtension();
        registry = registryFactory.getRegistry(registryUrl);
        return registry;
    }

    /*
     * generate metadata client
     */
    @Order(-1000)
    @Bean
    @DependsOn("governanceConfiguration")
    MetaDataCollector getMetadataCollector() {
        MetaDataCollector metaDataCollector = new NoOpMetadataCollector();
        if (metadataUrl == null) {
            if (StringUtils.isNotEmpty(metadataAddress)) {
                metadataUrl = formUrl(metadataAddress, metadataGroup, metadataGroupNameSpace, username, password);
                metadataUrl = metadataUrl.addParameter(CLUSTER_KEY, cluster);
            }
        }
        if (metadataUrl != null) {
            metaDataCollector = ExtensionLoader.getExtensionLoader(MetaDataCollector.class).getExtension(metadataUrl.getProtocol());
            metaDataCollector.setUrl(metadataUrl);
            metaDataCollector.init();
        } else {
            logger.warn("you are using dubbo.registry.address, which is not recommend, please refer to: https://github.com/apache/incubator-dubbo-admin/wiki/Dubbo-Admin-configuration");
        }
        return metaDataCollector;
    }

    private URL formUrl(String config, String group, String nameSpace, String username, String password) {
        URL url = URL.valueOf(config);
        if (StringUtils.isEmpty(url.getParameter(Constants.GROUP_KEY)) && StringUtils.isNotEmpty(group)) {
            url = url.addParameter(Constants.GROUP_KEY, group);
        }
        if (StringUtils.isEmpty(url.getParameter(Constants.NAMESPACE_KEY)) && StringUtils.isNotEmpty(nameSpace)) {
            url = url.addParameter(Constants.NAMESPACE_KEY, nameSpace);
        }
        if (StringUtils.isNotEmpty(username)) {
            url = url.addParameter(Constants.USERNAME_KEY, username);
            url = url.setUsername(username);
        }
        if (StringUtils.isNotEmpty(password)) {
            url = url.addParameter(Constants.PASSWORD_KEY, password);
            url = url.setPassword(password);
        }
        return url;
    }
    @Bean
    @DependsOn("governanceConfiguration")
    public NacosNaming nacosNaming() {
        NacosNaming nacosNaming = new NacosNaming();
        String address = nacosLoginUrl;
        nacosNaming.setServerAddr(address);
        nacosNaming.setUsername(username);
        nacosNaming.setPassword(password);
        nacosNaming.init();
        return nacosNaming;
    }

    public void checkData() {
        NacosNaming nacosNaming = new NacosNaming();
        String address = "nacos-normal.systech.b2c.srv:80";
        nacosNaming.setServerAddr(address);
        nacosNaming.init();
        try {
            TimeUnit.MILLISECONDS.sleep(500);
            List<String> tmpServiceNames = new ArrayList<>();
            int pageIndex = 1;
            ListView<String> listView;
            int count =Integer.MAX_VALUE;
            Stopwatch started = Stopwatch.createStarted();
            do {
                int countPage = count%100>0 ? count/100+1: count/100;
                if(pageIndex < countPage){
                    listView = nacosNaming.getServicesOfServer(pageIndex, 100);
                }else {
                    listView = nacosNaming.getServicesOfServer(pageIndex, count%100);
                }
                if (listView != null && CollectionUtils.isNotEmpty(listView.getData())) {
                    count = listView.getCount();
                    tmpServiceNames.addAll(listView.getData());
                    if(listView.getData().size()<100){
                        break;
                    }
                }
                pageIndex++;
                TimeUnit.MILLISECONDS.sleep(50);
            } while (listView != null && CollectionUtils.isNotEmpty(listView.getData()) && pageIndex < 10000);
            started.stop();
            System.out.println(started.elapsed(TimeUnit.MILLISECONDS));
            System.out.println(tmpServiceNames.size());
            System.out.println(count);
            System.out.println("tmpServiceNames");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Bean("busCommonThreadPool")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor(){
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setMaxPoolSize(Runtime.getRuntime().availableProcessors()*2);
        threadPoolTaskExecutor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        threadPoolTaskExecutor.setQueueCapacity(50);
        threadPoolTaskExecutor.setThreadNamePrefix("common-threadPoolTaskExecutor");
        return threadPoolTaskExecutor;
    }
}
