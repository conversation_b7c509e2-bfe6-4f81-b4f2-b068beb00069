/*
 *  Copyright 2020 Xiaomi
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

package com.xiaomi.dayu.config;

import com.xiaomi.aegis.filter.AegisFilter;
import com.xiaomi.dayu.filter.RedirectFilter;
import com.xiaomi.dayu.filter.ReqAndRespLogFilter;
import com.xiaomi.youpin.hermes.filter.HermesFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.Arrays;
import java.util.List;

@Configuration
public class FilterConfiguration {

    @Value("${dayu.aegis.sdk.public.key}")
    private String dayuAegisSdk;
    @Value("${dayu.online.aegis.sdk.public.key}")
    private String dayuOnlineAegisSdk;
    @Value("${mone.aegis.sdk.public.key:}")
    private String moneAegisSdk;
    @Value("${mone.online.aegis.sdk.public.key:}")
    private String moneOnlineAegisSdk;
    @Value("${miline.staging.aegis.sdk.public.key:}")
    private String milneStagingAegisSdk;
    @Value("${miline.online.aegis.sdk.public.key:}")
    private String milneOnlineAegisSdk;


    @Value("${server.cas.ignoreUrl}")
    private String ignoreUrl;
    @Bean
    public FilterRegistrationBean filterCasBean() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(new AegisFilter());
        String profile = System.getProperty("spring.profiles.active");
        if("dev".equals(profile)){
            registrationBean.addUrlPatterns("/*test");
        }else{
            registrationBean.addUrlPatterns("/*");
        }
        registrationBean.addInitParameter("AEGIS_SDK_PUBLIC_KEY", moneAegisSdk+","+moneOnlineAegisSdk+","+dayuAegisSdk+","+dayuOnlineAegisSdk+","+milneStagingAegisSdk+","+milneOnlineAegisSdk);
        registrationBean.addInitParameter("IGNORE_URL", ignoreUrl);
        registrationBean.setOrder(4);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<HermesFilter> hermes() {
        FilterRegistrationBean<HermesFilter>
                registration = new FilterRegistrationBean<>();
        registration.setFilter(new HermesFilter());

        List<String> patterns = Arrays.asList("/api");
        registration.setUrlPatterns(patterns);
        registration.setOrder(5);
        return registration;
    }
    @Bean
    public FilterRegistrationBean<Filter> loggingFilter() {
        FilterRegistrationBean<Filter> registrationBean
                = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ReqAndRespLogFilter());
        registrationBean.setOrder(-10);

        registrationBean.addUrlPatterns("/api/*");
        return registrationBean;
    }
    @Bean
    public FilterRegistrationBean filterRegistrationBean() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new RedirectFilter());
        filterRegistrationBean.addUrlPatterns("/api/*");
        filterRegistrationBean.setName("redirectFilter");
        filterRegistrationBean.setOrder(-2);
        return filterRegistrationBean;
    }
/*    @Bean
    public FilterRegistrationBean filterCORSFilterBean() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new CORSFilter());
        filterRegistrationBean.addUrlPatterns("/api/*");
        filterRegistrationBean.setName("CORSFilter");
        filterRegistrationBean.setOrder(-6);
        return filterRegistrationBean;
    }*/
}
