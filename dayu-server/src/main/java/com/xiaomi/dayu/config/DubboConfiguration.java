package com.xiaomi.dayu.config;

import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.config.ApplicationConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import static org.apache.dubbo.common.constants.CommonConstants.REMOTE_METADATA_STORAGE_TYPE;

/**
 *
 * @description
 * @version 1.0
 * <AUTHOR>
 * @date 2025/8/12 17:09
 *
 */
@Configuration
public class DubboConfiguration {

    @Value("${spring.application.name:dayu}")
    private String appName;

    @Bean
    public ApplicationConfig applicationConfig() {
        ApplicationConfig applicationConfig = new ApplicationConfig();
        applicationConfig.setQosEnable(false);
        applicationConfig.setName(appName + "-dubbo3");
        //注册方式，默认：接口和应用维度
        applicationConfig.setRegisterMode(CommonConstants.DEFAULT_REGISTER_MODE);
        //不使用文件缓存,meta的信息也不缓存了
        applicationConfig.setEnableFileCache(false);
        applicationConfig.setMetadataType(REMOTE_METADATA_STORAGE_TYPE);
        Map<String, String> m = new HashMap<>();
        //必需设置兼容老版本订阅
        m.put("nacos.subscribe.legacy-name", "true");
        applicationConfig.setParameters(m);
        return applicationConfig;
    }
}
