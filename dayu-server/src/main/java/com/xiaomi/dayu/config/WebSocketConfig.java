package com.xiaomi.dayu.config;

import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.MoonTaskDubboServiceRpc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    @Autowired
    private MoonTaskDubboServiceRpc moonTaskDubboServiceRpc;
    @Autowired
    private AccountServiceRpc userService;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry webSocketHandlerRegistry) {
        webSocketHandlerRegistry
                .addHandler(new WebsocketHandler(moonTaskDubboServiceRpc,userService), "/api/moon/task/ws/history/get")
               // .addInterceptors(webSocketHandshakeInterceptor())
                .setAllowedOrigins("*")
                .withSockJS()
                .setClientLibraryUrl("");
    }
}
