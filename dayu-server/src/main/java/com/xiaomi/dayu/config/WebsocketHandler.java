package com.xiaomi.dayu.config;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.MoonTaskDubboServiceRpc;
import com.xiaomi.youpin.hermes.bo.RoleBo;
import com.xiaomi.youpin.hermes.bo.request.QueryRoleRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import run.mone.moon.api.bo.common.Constants;
import run.mone.moon.api.bo.task.ReadTaskHistoryReq;
import run.mone.moon.api.bo.task.TaskHistoryRes;
import run.mone.moon.api.bo.task.TaskStatusEnum;
import run.mone.moon.api.bo.user.MoonMoneTpcContext;

import java.util.List;

@Slf4j
//@Component
public class WebsocketHandler extends TextWebSocketHandler {
    private MoonTaskDubboServiceRpc moonTaskDubboServiceRpc;
    private AccountServiceRpc userService;

    public WebsocketHandler(MoonTaskDubboServiceRpc moonTaskDubboServiceRpc, AccountServiceRpc userService) {
        this.moonTaskDubboServiceRpc = moonTaskDubboServiceRpc;
        this.userService = userService;
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            ReadTaskHistoryReq readTaskHistoryReq = JSON.parseObject(message.getPayload(), ReadTaskHistoryReq.class);
            TaskHistoryRes taskHistoryRes = moonTaskDubboServiceRpc.historyGet( getMoonMoneTpcContext(), readTaskHistoryReq);
            session.sendMessage(new TextMessage(JSON.toJSONString(buildTaskHistory(taskHistoryRes))));
        } catch (Exception e) {
            session.sendMessage(new TextMessage(e.getMessage()));
        }
    }



    //TODO
    public MoonMoneTpcContext getMoonMoneTpcContext() {
        String userName = "liuchuankang";
        MoonMoneTpcContext moneTpcContext = new MoonMoneTpcContext();
        moneTpcContext.setAccount(userName);
        moneTpcContext.setUserType(0);
        moneTpcContext.setTenant("1");
        moneTpcContext.setRole(Constants.ROLE_WORK);
        return moneTpcContext;
    }
/*    private int getRole(String userName) {
        QueryRoleRequest queryRoleRequest = new QueryRoleRequest();
        queryRoleRequest.setProjectName(Constants.PROJECT_NAME);
        queryRoleRequest.setUserName(userName);
        List<RoleBo> roles = userService.getRoleByProjectName(queryRoleRequest);
        int role;
        if (roles != null && roles.size() > 0 && roles.parallelStream().filter(e -> e.getName().contains("admin")).findAny().orElse(null) != null) {
            role = Constants.ROLE_ADMIN;
        } else if (roles != null && roles.size() > 0 && roles.parallelStream().filter(e -> e.getName().contains("work")).findAny().orElse(null) != null) {
            role = Constants.ROLE_WORK;
        } else {
            role = Constants.ROLE_WORK;
        }
        return role;
    }*/
    public void user(WebSocketSession session){
        List<String> cookie = session.getHandshakeHeaders().get("cookie");
    }
    public TaskHistoryRes buildTaskHistory(TaskHistoryRes  f) {
        TaskHistoryRes d = new TaskHistoryRes();
        d.setHistoryID(f.getId());
        d.setIp(f.getIp());
        d.setDetails(f.getDetails());
        d.setStatus(scheduleStatusMapping(String.valueOf(f.getStatus())));
        if (f.getStartTime() != null && f.getStopTime() != null) {
            d.setDuration(f.getStopTime() - f.getStartTime());
        }
        d.setProjectID(f.getProjectID());
        d.setTenant(f.getTenant());
        d.setMischeduleId(f.getMischeduleId());
        d.setExecResult(f.getExecResult());
        d.setStartTime(f.getStartTime());
        d.setStopTime(f.getStopTime());
        d.setExecParam(f.getExecParam());
        d.setExecutor(f.getExecutor());
        d.setTraceID(f.getTraceID());
        if (StringUtils.isEmpty(f.getExecutor())) {
            d.setExecutor("schedule");
        }
        /*Result<Task> taskRes = taskService.readByScheduleID(f.getMischeduleId());
        if (taskRes.getCode() != 0) {
            return d;
        }
        d.setTaskName(taskRes.getData().getName());
        d.setTaskType(taskRes.getData().getType());
        d.setId(taskRes.getData().getId());
        MoonTaskHistoryDetail detail;
        try {
            detail = gson.fromJson(d.getDetails(), MoonTaskHistoryDetail.class);
        } catch (Exception ignore) {
            detail = null;
        }
        if (detail != null && StringUtils.isNotEmpty(detail.getMessage())) {
            MoneStatus status;
            try {
                status = gson.fromJson(detail.getMessage(), MoneStatus.class);
            } catch (Exception e) {
                status = null;
            }
            if (status != null && status.getInstanceStatuses() != null) {
                for (InstanceStatus is : status.getInstanceStatuses()) {
                    TaskInstance instance = new TaskInstance();
                    instance.setIp(is.getPodIP());
                    instance.setCpu(is.getCpu());
                    instance.setMem(is.getMemory());
                    instance.setPercent(is.getPercentage());
                    instance.setProgress(is.getProgress());
                    d.getInstances().add(instance);
                }
            }
            d.setDetails(detail.getMessage());
        }*/
        return d;
    }

    private String scheduleStatusMapping(String str) {
        Integer x = null;
        try {
            x = Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return TaskStatusEnum.Unknown.name;
        }
        TaskStatusEnum ts = TaskStatusEnum.fromCode(x);
        return ts.name;
    }
}
