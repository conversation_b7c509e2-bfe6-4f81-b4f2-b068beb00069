package com.xiaomi.dayu.aop;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.registry.config.GovernanceConfiguration;
import com.xiaomi.dayu.registry.config.impl.NacosConfiguration;
import com.xiaomi.dayu.rpc.OperateLogServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@Aspect
public class OperateLogAspject {
    @Autowired
    private OperateLogServiceRpc operateLogServiceRpc;
    @Autowired
    protected GovernanceConfiguration dynamicConfiguration;
//    @Around("execution(public * com.xiaomi.dayu.registry.config.impl.NacosConfiguration.setConfig(String,String))")
    public Object recoderUpdateOperate(ProceedingJoinPoint pjp){
        //1:创建，2：删除，3：更改
        return recoderOperate(pjp,3);
    }
//    @Around("execution(public * com.xiaomi.dayu.registry.config.impl.NacosConfiguration.deleteConfig(String))")
    public Object recoderDeleteOperate(ProceedingJoinPoint pjp){
        return recoderOperate(pjp,2);
    }

//    @Around("@annotation(com.xiaomi.dayu.annotation.RecordOperation)")
    public ResultResponse recoderControllerOperate(ProceedingJoinPoint pjp) throws Throwable {
        HttpServletRequest request = (HttpServletRequest)pjp.getArgs()[0];
        String dataId = (String)pjp.getArgs()[1];
        String group = (String)pjp.getArgs()[2];
        String tenant = (String)pjp.getArgs()[3];

        String beforeData = HttpClient.getNacosConfig(tenant, group, dataId);
        ResultResponse<Object> result = (ResultResponse<Object>) pjp.proceed();

        String logPrefix = String.format("OperateLogAspject recoderControllerOperate dataId=%s, group=%s, tenant=%s: ",
                dataId, group, tenant);
        log.info(logPrefix + " before, result={}", JSON.toJSONString(result));

        if(!Boolean.parseBoolean((String)result.getData())){
            return result;
        }
        UserInfo userinfo = UserInfoThreadLocal.getUserInfo();

        CompletableFuture.runAsync(() -> {
            UserInfoThreadLocal.setUserInfo(userinfo);
            String afterData = HttpClient.getNacosConfig(tenant, group, dataId);
            String method = request.getMethod();
            int operate = RequestMethod.DELETE.name().equals(method) ? Enums.OperateType.DELETE.getValue() :
                    (StringUtils.isBlank(beforeData) ? Enums.OperateType.CREATE.getValue() : Enums.OperateType.UPDATE.getValue());

            try {
                operateLogServiceRpc.recoderOperateLog(operate, Joiner.on(Constants.PATH_SEPARATOR).join(Arrays.asList(tenant,group,dataId)),beforeData,afterData);
            }catch (Throwable e){
                log.error(logPrefix + " after, operateType={}", operate, e);
            }
        });
        return result;
    }
    private Object recoderOperate(ProceedingJoinPoint pjp,int operate) {

        Object[] args = pjp.getArgs();
        String key = (String)args[0];
        String afterData = "";
        if(operate == 2){
            afterData = "delete";
        }else if(args.length>1){
            afterData = (String)args[1];
        }else{
            afterData = "";
        }
        NacosConfiguration target = (NacosConfiguration) pjp.getTarget();
        String beforeData = dynamicConfiguration.getConfig(key);
        if(StringUtils.isBlank(beforeData)){
            operate = 1;
        }
        log.info("执行OperateLogAspject，入参args={}", JSON.toJSONString(args));
        Object result = null;
        try{
            result = pjp.proceed();
            boolean flag = false;
            if(result instanceof String){
                flag = Boolean.valueOf((String)result);
            }else if(result instanceof Boolean){
                flag = (Boolean)result;
            }
            log.info("执行OperateLogAspject，返回result={}", JSON.toJSONString(result));
            if(flag){
                String[] params = target.parseGroupAndDataId(key, target.getGroup());
                operateLogServiceRpc.recoderOperateLog(operate, Joiner.on(Constants.PATH_SEPARATOR).join(params),beforeData,afterData);
            }
        }catch (Throwable e){
            log.error("执行OperateLogAspject，调用异常，e={}",e.getMessage());
        }
        return result;
    }


    

}
