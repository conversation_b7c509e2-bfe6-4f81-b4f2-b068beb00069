//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.alibaba.dubbo.registry.nacos;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.registry.Registry;
import org.apache.dubbo.registry.nacos.NacosRegistry;
import org.apache.dubbo.registry.nacos.util.NacosNamingServiceUtils;
import org.apache.dubbo.registry.support.AbstractRegistryFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class NacosRegistryFactory extends AbstractRegistryFactory {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public NacosRegistryFactory() {
    }

    @Override
    protected Registry createRegistry(URL url) {
        return new NacosRegistry(url, NacosNamingServiceUtils.createNamingService(url));
    }

    private NamingService buildNamingService(URL url) {
        Properties nacosProperties = this.buildNacosProperties(url);
        NamingService namingService = null;

        try {
            namingService = NacosFactory.createNamingService(nacosProperties);
            return namingService;
        } catch (NacosException var5) {
            if (this.logger.isErrorEnabled()) {
                this.logger.error(var5.getErrMsg(), var5);
            }

            throw new IllegalStateException(var5);
        }
    }

    private Properties buildNacosProperties(URL url) {
        Properties properties = new Properties();
        this.setServerAddr(url, properties);
        this.setProperties(url, properties);
        this.setLoginInfo(url, properties);
        return properties;
    }

    private void setServerAddr(URL url, Properties properties) {
        StringBuilder serverAddrBuilder = (new StringBuilder(url.getHost())).append(":").append(url.getPort());
        String backup = url.getParameter("backup");
        if (backup != null) {
            serverAddrBuilder.append(",").append(backup);
        }

        String serverAddr = serverAddrBuilder.toString();
        properties.put("serverAddr", serverAddr);
    }

    private void setProperties(URL url, Properties properties) {
        this.putPropertyIfAbsent(url, properties, "namespace");
        this.putPropertyIfAbsent(url, properties, "com.alibaba.nacos.naming.log.filename");
        this.putPropertyIfAbsent(url, properties, "endpoint");
        this.putPropertyIfAbsent(url, properties, "namespace");
        this.putPropertyIfAbsent(url, properties, "accessKey");
        this.putPropertyIfAbsent(url, properties, "secretKey");
        this.putPropertyIfAbsent(url, properties, "clusterName");
        this.putPropertyIfAbsent(url, properties, "namingLoadCacheAtStart");
        this.putPropertyIfAbsent(url, properties, "application");
    }

    private void putPropertyIfAbsent(URL url, Properties properties, String propertyName) {
        String propertyValue = url.getParameter(propertyName);
        if (isNotEmpty(propertyValue)) {
            properties.setProperty(propertyName, propertyValue);
        }

    }

    private void setLoginInfo(URL url, Properties properties) {
        String username = url.getUsername();
        if (isNotEmpty(username)) {
            properties.setProperty("username", username);
        }
        String password = url.getPassword();
        if (isNotEmpty(password)) {
            properties.setProperty("password", password);
        }

    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean isEmpty(String str) {
        return str == null || str.length() == 0;
    }
}
