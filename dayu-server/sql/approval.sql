/*
 Navicat Premium Data Transfer

 Source Server         : nacos-staging
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : ksc.test.mysql01.b2c.srv:3206
 Source Schema         : nacos_standalone

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 11/01/2023 15:54:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for approval
-- ----------------------------
DROP TABLE IF EXISTS `approval`;
CREATE TABLE `approval`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `app_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '应用名',
  `approve_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审批类型（10：nacos配置）',
  `operate_type` tinyint(3) NOT NULL DEFAULT 0 COMMENT '操作类型（1：新增，2：修改，3：删除）',
  `relate_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联key',
  `relate_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联信息',
  `status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '状态（10：提交，20：通过，30：驳回）',
  `applicant` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '申请人',
  `apply_remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '申请备注',
  `approver` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '审批人',
  `new_data` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修改后数据',
  `old_data` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修改前数据',
  `operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '处理人',
  `operate_remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '处理备注',
  `approve_time` datetime(0) NOT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '审批时间',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL COMMENT '更新时间',
  `del` tinyint(3) NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 222 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '审批' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
