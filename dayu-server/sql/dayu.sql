CREATE TABLE `config_info_extend`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `config_info_id` bigint(20) UNSIGNED ZEROFILL NOT NULL COMMENT 'config_info表主键',
    `data_id`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'dataId',
    `env_id`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '环境evn id',
    `env_name`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '环境名称',
    `config_type`    int(10) NULL COMMENT '配置类型',
    `create_time`    timestamp(0)                                            NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_idex_configinfoid`(`config_info_id`) USING BTREE,
    INDEX            `idx_configinfoextend_configtype`(`config_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '扩展表' ROW_FORMAT = Dynamic


CREATE TABLE `swim_lane`
(
    `id`                 int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `name`               varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '泳道名',
    `app_env_json`       text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '应用链',
    `flow_control_tag`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '流控标签',
    `status`             tinyint(1) ZEROFILL NOT NULL COMMENT '状态',
    `condition_json`     text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '条件',
    `swim_lane_group_id` int(10) NOT NULL COMMENT ' 所属永道组id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '泳道' ROW_FORMAT = Dynamic

CREATE TABLE `swim_lane_group`
(
    `id`            int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `name`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '泳道组名',
    `app_list`      varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关联应用名列表',
    `descp`         varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '泳道描述',
    `creator`       varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time`   datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP (0) ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '创建时间',
    `type`          int(10) UNSIGNED ZEROFILL NOT NULL COMMENT '泳道组类型',
    `entrance_app`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'tesla' COMMENT '泳道入口应用',
    `prefix_header` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '泳道前缀标志',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '泳道组名' ROW_FORMAT = Dynamic

CREATE TABLE `threadpool_config`
(
    `id`                int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_name`          varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '应用名',
    `pool_name`         varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '线程池名',
    `core_pool_size`    int(11) NULL DEFAULT NULL COMMENT '核心线程数',
    `maximum_pool_size` int(11) NULL DEFAULT NULL COMMENT '最大线程数',
    `keep_alive_time`   int(11) NULL DEFAULT NULL COMMENT '空闲时间',
    `capacity`          int(11) NULL DEFAULT NULL COMMENT '容量',
    `reject`            int(11) NULL DEFAULT NULL COMMENT '拒绝策略',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_config_id_uindex`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '线程池' ROW_FORMAT = Dynamic


CREATE TABLE `threadpool_config_history`
(
    `id`        int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `config_id` int(10) NOT NULL COMMENT '配置id',
    `user`      varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户',
    `action`    varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '行为',
    `content`   varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '内容',
    `data_id`   varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'dataid',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_threadpool_config_history_id_uindex`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '历史记录' ROW_FORMAT = Dynamic
