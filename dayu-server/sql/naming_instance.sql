/*
 Navicat Premium Data Transfer

 Source Server         : nacos-staging
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : ksc.test.mysql01.b2c.srv:3206
 Source Schema         : nacos_standalone

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 27/06/2022 10:25:52
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for naming_instance
-- ----------------------------
DROP TABLE IF EXISTS `naming_instance`;
CREATE TABLE `naming_instance`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `instance_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '实例ID',
  `service_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务名',
  `dubbo_service` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `dubbo_group` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'dubbo group',
  `dubbo_version` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `full_service` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `application` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '应用名',
  `side` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'consumer,provider',
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '地址',
  `port` int(11) UNSIGNED NULL DEFAULT NULL COMMENT 'port',
  `weight` double(16, 2) UNSIGNED NULL DEFAULT NULL COMMENT '权重',
  `healthy` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否健康',
  `enabled` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可用',
  `ephemeral` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否临时',
  `cluster_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '集群名称',
  `namespace_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'nacos namespace',
  `group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'nacos group',
  `metadata` varchar(2096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '元数据',
  `md5` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `last_beat_time` datetime(0) NULL DEFAULT NULL COMMENT '最后心跳时间',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
  `del` bit(1) NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_idex_instanceId`(`instance_id`) USING BTREE,
  INDEX `idex_service_name`(`service_name`) USING BTREE,
  INDEX `idx_side_application`(`side`, `application`) USING BTREE,
  INDEX `idx_side_service`(`side`, `dubbo_service`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic STATS_AUTO_RECALC = 0;

SET FOREIGN_KEY_CHECKS = 1;
