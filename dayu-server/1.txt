[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.xiaomi:dayu-server:jar:0.3.2-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.springframework.boot:spring-boot-starter-websocket:jar -> duplicate declaration of version (?) @ com.xiaomi:dayu-server:${revision}, D:\code\xiaomi\dayu\dayu-server\pom.xml, line 344, column 15
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] 
[INFO] -----------------------< com.xiaomi:dayu-server >-----------------------
[INFO] Building dayu-server 0.3.2-SNAPSHOT
[INFO] --------------------------------[ jar ]---------------------------------
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/dayu/0.3.2-SNAPSHOT/dayu-0.3.2-20250812.093818-2.pom
Progress (1): 4.1/10 kB
Progress (1): 7.3/10 kB
Progress (1): 10 kB    
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/dayu/0.3.2-SNAPSHOT/dayu-0.3.2-20250812.093818-2.pom (10 kB at 8.8 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/feishu/1.4-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/feishu/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 982 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/feishu/1.4-SNAPSHOT/maven-metadata.xml (982 B at 7.2 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/mone-threadpool/2.0.0-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mone-threadpool/2.0.0-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 790 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mone-threadpool/2.0.0-mone-SNAPSHOT/maven-metadata.xml (790 B at 10 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus-starter/0.0.6-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/prometheus-starter/0.0.6-SNAPSHOT/maven-metadata.xml
Progress (1): 999 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus-starter/0.0.6-SNAPSHOT/maven-metadata.xml (999 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus/0.0.2-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/prometheus/0.0.2-SNAPSHOT/maven-metadata.xml
Progress (1): 606 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus/0.0.2-SNAPSHOT/maven-metadata.xml (606 B at 2.4 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus-metrics/0.0.5-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/prometheus-metrics/0.0.5-SNAPSHOT/maven-metadata.xml
Progress (1): 999 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus-metrics/0.0.5-SNAPSHOT/maven-metadata.xml (999 B at 9.3 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus-client/0.0.5-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/prometheus-client/0.0.5-SNAPSHOT/maven-metadata.xml
Progress (1): 998 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/prometheus-client/0.0.5-SNAPSHOT/maven-metadata.xml (998 B at 8.7 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/youpin-infra-rpc/1.12-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/youpin-infra-rpc/1.12-SNAPSHOT/maven-metadata.xml
Progress (1): 783 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/youpin-infra-rpc/1.12-SNAPSHOT/maven-metadata.xml (783 B at 7.6 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/youpin-infra-java/1.12-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/youpin-infra-java/1.12-SNAPSHOT/maven-metadata.xml
Progress (1): 611 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/youpin-infra-java/1.12-SNAPSHOT/maven-metadata.xml (611 B at 7.5 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/drizzle-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/drizzle-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 779 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/drizzle-api/1.0.0-SNAPSHOT/maven-metadata.xml (779 B at 9.0 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/drizzle/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/drizzle/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 599 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/drizzle/1.0.0-SNAPSHOT/maven-metadata.xml (599 B at 3.6 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs-core/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/http-docs-core/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Progress (1): 801 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs-core/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml (801 B at 14 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/http-docs/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Progress (1): 613 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml (613 B at 9.6 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/http-docs-annotations/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs-annotations/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Progress (1): 808 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs-annotations/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml (808 B at 9.9 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/nacos/1.4-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/nacos/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 982 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/nacos/1.4-SNAPSHOT/maven-metadata.xml (982 B at 7.0 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/mischedule-api/1.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/mischedule-api/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 781 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/mischedule-api/1.0-SNAPSHOT/maven-metadata.xml (781 B at 3.0 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/mischedule/1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/mischedule/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 604 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/mischedule/1.0-SNAPSHOT/maven-metadata.xml (604 B at 2.0 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/struct/1.4.1-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/struct/1.4.1-SNAPSHOT/maven-metadata.xml
Progress (1): 978 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/struct/1.4.1-SNAPSHOT/maven-metadata.xml (978 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/nutz/nutz/1.r.68-youpin-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/nutz/nutz/1.r.68-youpin-SNAPSHOT/maven-metadata.xml
Progress (1): 782 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/nutz/nutz/1.r.68-youpin-SNAPSHOT/maven-metadata.xml (782 B at 7.4 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-auth-all/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/dubbo-auth-all/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml
Progress (1): 795 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-auth-all/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml (795 B at 5.0 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/dubbo-mone/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-mone/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml
Progress (1): 610 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-mone/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml (610 B at 8.4 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-mone/3.3.4-mone-v1-SNAPSHOT/dubbo-mone-3.3.4-mone-v1-20250812.031345-1.pom
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-mone/3.3.4-mone-v1-SNAPSHOT/dubbo-mone-3.3.4-mone-v1-20250812.031345-1.pom (0 B at 0 B/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/dubbo/dubbo/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/dubbo/dubbo/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml
Progress (1): 1.0 kB
                    
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/dubbo/dubbo/3.3.4-mone-v1-SNAPSHOT/maven-metadata.xml (1.0 kB at 4.7 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/tesla-auth-api/1.1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla-auth-api/1.1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 784 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla-auth-api/1.1.0-SNAPSHOT/maven-metadata.xml (784 B at 10 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/tesla/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 603 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla/1.0.0-SNAPSHOT/maven-metadata.xml (603 B at 5.4 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/mi-tpc-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpc-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 997 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpc-api/1.0.0-SNAPSHOT/maven-metadata.xml (997 B at 2.6 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/mi-tpc/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpc/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 600 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpc/1.0.0-SNAPSHOT/maven-metadata.xml (600 B at 5.5 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/mi-tpc-common/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpc-common/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 1.0 kB
                    
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpc-common/1.0.0-SNAPSHOT/maven-metadata.xml (1.0 kB at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/application-manager-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/application-manager-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 791 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/application-manager-api/1.0.0-SNAPSHOT/maven-metadata.xml (791 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/application-manager/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/application-manager/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 611 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/application-manager/1.0.0-SNAPSHOT/maven-metadata.xml (611 B at 6.2 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/gitlab/1.5-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/gitlab/1.5-SNAPSHOT/maven-metadata.xml
Progress (1): 974 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/gitlab/1.5-SNAPSHOT/maven-metadata.xml (974 B at 9.5 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-starter-alibaba-sentinel/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/cloud/spring-cloud-starter-alibaba-sentinel/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 1.3 kB
                    
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-starter-alibaba-sentinel/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml (1.3 kB at 15 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/cloud/spring-cloud-alibaba-starters/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-starters/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 632 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-starters/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml (632 B at 2.0 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/cloud/spring-cloud-alibaba/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 623 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml (623 B at 3.1 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/cloud/spring-cloud-alibaba-dependencies/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-dependencies/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 636 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-dependencies/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml (636 B at 9.9 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-transport-simple-http/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-transport-simple-http/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 811 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-transport-simple-http/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (811 B at 13 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-transport/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-transport/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 619 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-transport/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (619 B at 6.3 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-parent/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-parent/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 616 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-parent/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (616 B at 5.3 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-transport-common/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-transport-common/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 806 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-transport-common/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (806 B at 14 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-datasource-extension/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-datasource-extension/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 810 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-datasource-extension/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (810 B at 12 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-extension/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-extension/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 619 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-extension/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (619 B at 9.8 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-core/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-core/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 794 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-core/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (794 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-datasource-nacos/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-datasource-nacos/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 806 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-datasource-nacos/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (806 B at 8.6 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-annotation-aspectj/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-annotation-aspectj/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 808 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-annotation-aspectj/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (808 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-dubbo-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-dubbo-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 803 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-dubbo-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (803 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 617 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (617 B at 10.0 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-spring-webflux-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-spring-webflux-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 812 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-spring-webflux-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (812 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-reactor-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-reactor-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 805 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-reactor-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (805 B at 10 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-spring-webmvc-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-spring-webmvc-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 811 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-spring-webmvc-adapter/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (811 B at 4.2 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-parameter-flow-control/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-parameter-flow-control/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 812 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-parameter-flow-control/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (812 B at 8.9 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-cluster-server-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster-server-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 812 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster-server-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (812 B at 7.7 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-cluster/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 617 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (617 B at 6.4 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-cluster-common-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster-common-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 812 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster-common-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (812 B at 11 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/csp/sentinel-cluster-client-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster-client-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 812 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/csp/sentinel-cluster-client-default/1.8.2.2-mone-SNAPSHOT/maven-metadata.xml (812 B at 13 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/cloud/spring-cloud-alibaba-sentinel-datasource/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-sentinel-datasource/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 1.3 kB
                    
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-sentinel-datasource/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml (1.3 kB at 13 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/alibaba/cloud/spring-cloud-alibaba-commons/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-commons/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 1.3 kB
                    
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/alibaba/cloud/spring-cloud-alibaba-commons/2.1.4.2-mone-SNAPSHOT/maven-metadata.xml (1.3 kB at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miline-api/1.0.1-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/miline-api/1.0.1-SNAPSHOT/maven-metadata.xml
Progress (1): 781 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miline-api/1.0.1-SNAPSHOT/maven-metadata.xml (781 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miline/1.0.1-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/miline/1.0.1-SNAPSHOT/maven-metadata.xml
Progress (1): 602 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miline/1.0.1-SNAPSHOT/maven-metadata.xml (602 B at 8.6 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/hermes-api/0.0.1-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/hermes-api/0.0.1-SNAPSHOT/maven-metadata.xml
Progress (1): 782 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/hermes-api/0.0.1-SNAPSHOT/maven-metadata.xml (782 B at 8.4 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla-k8s-proxy-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/tesla-k8s-proxy-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 789 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla-k8s-proxy-api/1.0.0-SNAPSHOT/maven-metadata.xml (789 B at 9.0 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/gwdash-api/1.0.1-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/gwdash-api/1.0.1-SNAPSHOT/maven-metadata.xml
Progress (1): 783 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/gwdash-api/1.0.1-SNAPSHOT/maven-metadata.xml (783 B at 10 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/gwdash/0.0.1-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/gwdash/0.0.1-SNAPSHOT/maven-metadata.xml
Progress (1): 604 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/gwdash/0.0.1-SNAPSHOT/maven-metadata.xml (604 B at 4.8 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/tesla-quota-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla-quota-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 787 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/tesla-quota-api/1.0.0-SNAPSHOT/maven-metadata.xml (787 B at 10 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/oracle-api/1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/oracle-api/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 774 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/oracle-api/1.0-SNAPSHOT/maven-metadata.xml (774 B at 8.5 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/oracle/1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/oracle/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 596 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/oracle/1.0-SNAPSHOT/maven-metadata.xml (596 B at 7.3 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/mone-tenant/mone-tenant-api/1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/mone-tenant/mone-tenant-api/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 773 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/mone-tenant/mone-tenant-api/1.0-SNAPSHOT/maven-metadata.xml (773 B at 5.3 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/gitlab/1.5-mone-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/gitlab/1.5-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 985 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/gitlab/1.5-mone-SNAPSHOT/maven-metadata.xml (985 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/mi/youpin/kc_register-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/mi/youpin/kc_register-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 778 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/mi/youpin/kc_register-api/1.0.0-SNAPSHOT/maven-metadata.xml (778 B at 12 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/mi/youpin/kc_register/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/mi/youpin/kc_register/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 601 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/mi/youpin/kc_register/1.0.0-SNAPSHOT/maven-metadata.xml (601 B at 7.2 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/api/1.4-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/api/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 980 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/api/1.4-SNAPSHOT/maven-metadata.xml (980 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/hera-monitor-api/1.1-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/hera-monitor-api/1.1-SNAPSHOT/maven-metadata.xml
Progress (1): 771 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/hera-monitor-api/1.1-SNAPSHOT/maven-metadata.xml (771 B at 12 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/hera-monitor/1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/hera-monitor/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 597 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/hera-monitor/1.0-SNAPSHOT/maven-metadata.xml (597 B at 9.9 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/api/1.4-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/api/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 971 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/api/1.4-SNAPSHOT/maven-metadata.xml (971 B at 15 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/health-check-api/1.0-inner-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/health-check-api/1.0-inner-SNAPSHOT/maven-metadata.xml
Progress (1): 780 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/health-check-api/1.0-inner-SNAPSHOT/maven-metadata.xml (780 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/health-check/1.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/health-check/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 593 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/health-check/1.0-SNAPSHOT/maven-metadata.xml (593 B at 7.1 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/health-check-common/1.0-inner-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/health-check-common/1.0-inner-SNAPSHOT/maven-metadata.xml
Progress (1): 783 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/health-check-common/1.0-inner-SNAPSHOT/maven-metadata.xml (783 B at 12 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/dubbo-docs-core/2.7.12-mone-v3-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-docs-core/2.7.12-mone-v3-SNAPSHOT/maven-metadata.xml
Progress (1): 802 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-docs-core/2.7.12-mone-v3-SNAPSHOT/maven-metadata.xml (802 B at 14 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/dubbo-docs/2.7.12-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-docs/2.7.12-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 611 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-docs/2.7.12-mone-SNAPSHOT/maven-metadata.xml (611 B at 11 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/dubbo-mone/2.7.12-mone-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-mone/2.7.12-mone-SNAPSHOT/maven-metadata.xml
Progress (1): 609 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-mone/2.7.12-mone-SNAPSHOT/maven-metadata.xml (609 B at 9.4 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/dubbo-docs-annotations/2.7.12-mone-v3-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-docs-annotations/2.7.12-mone-v3-SNAPSHOT/maven-metadata.xml
Progress (1): 809 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-docs-annotations/2.7.12-mone-v3-SNAPSHOT/maven-metadata.xml (809 B at 12 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/dayu-api/0.3.1-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/dayu-api/0.3.1-SNAPSHOT/maven-metadata.xml
Progress (1): 771 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/dayu-api/0.3.1-SNAPSHOT/maven-metadata.xml (771 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/dayu/0.3.1-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/dayu/0.3.1-SNAPSHOT/maven-metadata.xml
Progress (1): 593 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/dayu/0.3.1-SNAPSHOT/maven-metadata.xml (593 B at 8.6 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mit/parent-pom/1.0.2-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mit/parent-pom/1.0.2-SNAPSHOT/maven-metadata.xml
Progress (1): 601 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mit/parent-pom/1.0.2-SNAPSHOT/maven-metadata.xml (601 B at 8.3 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/mifaas-dash/mifaas-dash-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/mifaas-dash/mifaas-dash-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 779 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/mifaas-dash/mifaas-dash-api/1.0.0-SNAPSHOT/maven-metadata.xml (779 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/mifaas-dash/mifaas-dash/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/mifaas-dash/mifaas-dash/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 601 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/mifaas-dash/mifaas-dash/1.0.0-SNAPSHOT/maven-metadata.xml (601 B at 9.2 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/mi-tpclogin-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpclogin-sdk/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 998 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mi-tpclogin-sdk/1.0.0-SNAPSHOT/maven-metadata.xml (998 B at 6.9 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/io/github/tesla/tesla-api/1.0.4-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/io/github/tesla/tesla-api/1.0.4-SNAPSHOT/maven-metadata.xml
Progress (1): 996 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/io/github/tesla/tesla-api/1.0.4-SNAPSHOT/maven-metadata.xml (996 B at 12 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/io/github/tesla/tesla/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/io/github/tesla/tesla/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 597 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/io/github/tesla/tesla/1.0.0-SNAPSHOT/maven-metadata.xml (597 B at 7.3 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/common/1.7.2-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/common/1.7.2-SNAPSHOT/maven-metadata.xml
Progress (1): 978 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/common/1.7.2-SNAPSHOT/maven-metadata.xml (978 B at 12 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/crypto/1.4-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/crypto/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 970 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/crypto/1.4-SNAPSHOT/maven-metadata.xml (970 B at 16 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/antlr/1.4-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/antlr/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 982 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/antlr/1.4-SNAPSHOT/maven-metadata.xml (982 B at 14 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/gitlab/1.5-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/gitlab/1.5-SNAPSHOT/maven-metadata.xml
Progress (1): 983 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/gitlab/1.5-SNAPSHOT/maven-metadata.xml (983 B at 15 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mimonitor-api/1.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/mimonitor-api/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 987 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mimonitor-api/1.0-SNAPSHOT/maven-metadata.xml (987 B at 5.5 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/mimonitor/1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mimonitor/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 599 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mimonitor/1.0-SNAPSHOT/maven-metadata.xml (599 B at 8.7 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard-api/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/moon-schedule-dashboard-api/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 788 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard-api/1.0.0-SNAPSHOT/maven-metadata.xml (788 B at 12 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard/1.0.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/moon-schedule-dashboard/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 610 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard/1.0.0-SNAPSHOT/maven-metadata.xml (610 B at 11 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard/1.0.0-SNAPSHOT/moon-schedule-dashboard-1.0.0-20250716.055559-21.pom
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard/1.0.0-SNAPSHOT/moon-schedule-dashboard-1.0.0-20250716.055559-21.pom (0 B at 0 B/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs-annotations/2.7.12-mone-v2-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/http-docs-annotations/2.7.12-mone-v2-SNAPSHOT/maven-metadata.xml
Progress (1): 808 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs-annotations/2.7.12-mone-v2-SNAPSHOT/maven-metadata.xml (808 B at 12 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs/2.7.12-mone-v2-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/http-docs/2.7.12-mone-v2-SNAPSHOT/maven-metadata.xml
Progress (1): 613 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/http-docs/2.7.12-mone-v2-SNAPSHOT/maven-metadata.xml (613 B at 10 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/schedule/1.0-moon-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/schedule/1.0-moon-SNAPSHOT/maven-metadata.xml
Progress (1): 995 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/schedule/1.0-moon-SNAPSHOT/maven-metadata.xml (995 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/schedule-utils/1.0-moon-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/schedule-utils/1.0-moon-SNAPSHOT/maven-metadata.xml
Progress (1): 604 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/schedule-utils/1.0-moon-SNAPSHOT/maven-metadata.xml (604 B at 8.8 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/common/1.0-moon-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/common/1.0-moon-SNAPSHOT/maven-metadata.xml
Progress (1): 993 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/common/1.0-moon-SNAPSHOT/maven-metadata.xml (993 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/feishu/1.4-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/feishu/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 970 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/feishu/1.4-SNAPSHOT/maven-metadata.xml (970 B at 15 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/annotation/1.6-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/annotation/1.6-SNAPSHOT/maven-metadata.xml
Progress (1): 974 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/annotation/1.6-SNAPSHOT/maven-metadata.xml (974 B at 6.0 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/common/1.7-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/common/1.7-SNAPSHOT/maven-metadata.xml
Progress (1): 974 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/common/1.7-SNAPSHOT/maven-metadata.xml (974 B at 14 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/redis/1.8-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/redis/1.8-SNAPSHOT/maven-metadata.xml
Progress (1): 969 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/redis/1.8-SNAPSHOT/maven-metadata.xml (969 B at 13 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/catPlugin/1.4-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/catPlugin/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 973 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/catPlugin/1.4-SNAPSHOT/maven-metadata.xml (973 B at 8.6 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/prometheus-client/0.0.5-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/prometheus-client/0.0.5-SNAPSHOT/maven-metadata.xml
Progress (1): 989 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/prometheus-client/0.0.5-SNAPSHOT/maven-metadata.xml (989 B at 9.1 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/prometheus/0.0.5-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/prometheus/0.0.5-SNAPSHOT/maven-metadata.xml
Progress (1): 595 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/prometheus/0.0.5-SNAPSHOT/maven-metadata.xml (595 B at 9.0 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/rpc/1.7-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/rpc/1.7-SNAPSHOT/maven-metadata.xml
Progress (1): 971 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/rpc/1.7-SNAPSHOT/maven-metadata.xml (971 B at 4.3 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/nacos/1.4-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/nacos/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 969 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/nacos/1.4-SNAPSHOT/maven-metadata.xml (969 B at 12 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/easy/1.4-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/easy/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 972 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/easy/1.4-SNAPSHOT/maven-metadata.xml (972 B at 15 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/rpc-codes/1.4-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/rpc-codes/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 973 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/rpc-codes/1.4-SNAPSHOT/maven-metadata.xml (973 B at 14 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/redis/1.5-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/redis/1.5-SNAPSHOT/maven-metadata.xml
Progress (1): 769 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/redis/1.5-SNAPSHOT/maven-metadata.xml (769 B at 11 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/dianping/cat/cat-client/3.0.1-youpin-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/dianping/cat/cat-client/3.0.1-youpin-SNAPSHOT/maven-metadata.xml
Progress (1): 1.0 kB
                    
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/dianping/cat/cat-client/3.0.1-youpin-SNAPSHOT/maven-metadata.xml (1.0 kB at 6.4 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/dianping/cat/parent/3.0.0-youpin-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/dianping/cat/parent/3.0.0-youpin-SNAPSHOT/maven-metadata.xml
Progress (1): 608 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/dianping/cat/parent/3.0.0-youpin-SNAPSHOT/maven-metadata.xml (608 B at 8.3 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/struct/1.0-moon-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/struct/1.0-moon-SNAPSHOT/maven-metadata.xml
Progress (1): 993 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/struct/1.0-moon-SNAPSHOT/maven-metadata.xml (993 B at 12 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/youpin/cron/1.4-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/cron/1.4-SNAPSHOT/maven-metadata.xml
Progress (1): 981 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/youpin/cron/1.4-SNAPSHOT/maven-metadata.xml (981 B at 14 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/ai/z-api/1.0-SNAPSHOT/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/ai/z-api/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 763 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/ai/z-api/1.0-SNAPSHOT/maven-metadata.xml (763 B at 10 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/ai/z/1.0-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/run/mone/ai/z/1.0-SNAPSHOT/maven-metadata.xml
Progress (1): 587 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/ai/z/1.0-SNAPSHOT/maven-metadata.xml (587 B at 6.5 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miapi-doc-annos/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/mone/miapi-doc-annos/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml
Progress (1): 799 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miapi-doc-annos/2.7.12-mone-v5-SNAPSHOT/maven-metadata.xml (799 B at 3.4 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-auth-all/3.3.4-mone-v1-SNAPSHOT/dubbo-auth-all-3.3.4-mone-v1-20250812.080922-2.jar
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard-api/1.0.0-SNAPSHOT/moon-schedule-dashboard-api-1.0.0-20250716.055606-29.jar
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miline-api/1.0.1-SNAPSHOT/miline-api-1.0.1-20250811.061831-499.jar
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/dubbo/dubbo/3.3.4-mone-v1-SNAPSHOT/dubbo-3.3.4-mone-v1-20250812.004925-24.jar
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mone-threadpool/2.0.0-mone-SNAPSHOT/mone-threadpool-2.0.0-mone-20250409.065509-1.jar
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/dubbo-auth-all/3.3.4-mone-v1-SNAPSHOT/dubbo-auth-all-3.3.4-mone-v1-20250812.080922-2.jar (0 B at 0 B/s)
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/run/mone/moon-schedule-dashboard-api/1.0.0-SNAPSHOT/moon-schedule-dashboard-api-1.0.0-20250716.055606-29.jar (0 B at 0 B/s)
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/mone-threadpool/2.0.0-mone-SNAPSHOT/mone-threadpool-2.0.0-mone-20250409.065509-1.jar (0 B at 0 B/s)
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/mone/miline-api/1.0.1-SNAPSHOT/miline-api-1.0.1-20250811.061831-499.jar (0 B at 0 B/s)
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/dubbo/dubbo/3.3.4-mone-v1-SNAPSHOT/dubbo-3.3.4-mone-v1-20250812.004925-24.jar (0 B at 0 B/s)
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:tree (default-cli) @ dayu-server ---
[INFO] com.xiaomi:dayu-server:jar:0.3.2-SNAPSHOT
[INFO] +- com.alibaba:dubbo-registry-nacos:jar:1.2.1-mone-v1-SNAPSHOT:compile
[INFO] +- com.xiaomi.youpin:test0930-api:jar:2.0-SNAPSHOT:compile
[INFO] |  +- org.projectlombok:lombok:jar:1.18.20:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  +- com.xiaomi.youpin:youpin-infra-rpc:jar:1.28-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.youpin:hera-trace:jar:1.4-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.youpin:coloregg:jar:1.4-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.youpin:aop:jar:1.4-SNAPSHOT:compile
[INFO] |  |  \- org.aspectj:aspectjweaver:jar:1.9.6:compile
[INFO] |  +- com.xiaomi.youpin:common:jar:1.7-SNAPSHOT:compile
[INFO] |  |  +- cglib:cglib-nodep:jar:3.2.7:compile
[INFO] |  |  +- org.reflections:reflections:jar:0.9.11:compile
[INFO] |  |  +- com.caucho:hessian:jar:4.0.51:compile
[INFO] |  |  \- com.xiaomi.youpin:crypto:jar:1.4-SNAPSHOT:compile
[INFO] |  +- org.mi:thrift:jar:0.9.2-mi-v1.5-SNAPSHOT:compile
[INFO] |  \- com.xiaomi.mone:dubbo-docs-core:jar:2.7.12-mone-v5-SNAPSHOT:compile
[INFO] |     +- com.xiaomi.mone:dubbo-docs-annotations:jar:2.7.12-mone-v5-SNAPSHOT:compile
[INFO] |     +- ch.qos.logback:logback-classic:jar:1.2.3:compile
[INFO] |     |  \- ch.qos.logback:logback-core:jar:1.2.3:compile
[INFO] |     \- com.xiaomi.miapi:mi-api-api:jar:1.0.0-SNAPSHOT:compile
[INFO] +- com.xiaomi:dayu-service:jar:0.3.2-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.youpin:feishu:jar:1.4-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.mone:mone-threadpool:jar:2.0.0-mone-SNAPSHOT:compile
[INFO] |  |  \- com.xiaomi.youpin:prometheus-starter:jar:0.0.6-SNAPSHOT:compile
[INFO] |  |     +- com.xiaomi.youpin:prometheus-metrics:jar:0.0.5-SNAPSHOT:compile
[INFO] |  |     |  \- com.xiaomi.youpin:prometheus-client:jar:0.0.5-SNAPSHOT:compile
[INFO] |  |     |     \- io.micrometer:micrometer-registry-prometheus:jar:1.5.14:compile
[INFO] |  |     |        \- io.micrometer:micrometer-core:jar:1.5.14:compile
[INFO] |  |     |           +- org.hdrhistogram:HdrHistogram:jar:2.1.12:compile
[INFO] |  |     |           \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] |  |     +- io.prometheus:simpleclient:jar:0.10.0:compile
[INFO] |  |     +- io.prometheus:simpleclient_hotspot:jar:0.10.0:compile
[INFO] |  |     \- io.prometheus:simpleclient_common:jar:0.10.0:compile
[INFO] |  +- com.xiaomi:dayu-api:jar:0.3.2-SNAPSHOT:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.11.4:compile
[INFO] |  |  +- com.xiaomi.mone:drizzle-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  \- com.xiaomi.mone:http-docs-core:jar:2.7.12-mone-v5-SNAPSHOT:compile
[INFO] |  |     \- com.xiaomi.mone:http-docs-annotations:jar:2.7.12-mone-v5-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.youpin:mischedule-api:jar:1.0-SNAPSHOT:compile
[INFO] |  |  +- run.mone:struct:jar:1.4.1-SNAPSHOT:compile
[INFO] |  |  \- org.nutz:nutz:jar:1.r.68-youpin-SNAPSHOT:compile
[INFO] |  +- com.xiaomi:dayu-rpc:jar:0.3.2-SNAPSHOT:compile
[INFO] |  |  +- com.xiaomi.mone:dubbo-auth-all:jar:3.3.4-mone-v1-SNAPSHOT:compile
[INFO] |  |  |  \- org.apache.dubbo:dubbo:jar:3.3.4-mone-v1-SNAPSHOT:compile
[INFO] |  |  |     +- org.apache.dubbo:hessian-lite:jar:4.0.3:compile
[INFO] |  |  |     +- com.alibaba.fastjson2:fastjson2:jar:2.0.56:compile
[INFO] |  |  |     \- com.google.protobuf:protobuf-java:jar:3.25.7:compile
[INFO] |  |  +- com.xiaomi.youpin:tesla-auth-api:jar:1.1.0-SNAPSHOT:compile
[INFO] |  |  +- com.xiaomi.mone:mi-tpc-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  |  \- com.xiaomi.mone:mi-tpc-common:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  |     \- com.xiaomi.mone:application-manager-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  +- com.xiaomi:dayu-common:jar:0.3.2-SNAPSHOT:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.13.3:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.13.3:compile
[INFO] |  |  |  +- io.springfox:springfox-swagger2:jar:2.9.2:compile
[INFO] |  |  |  |  +- io.swagger:swagger-annotations:jar:1.5.20:compile
[INFO] |  |  |  |  +- io.swagger:swagger-models:jar:1.5.20:compile
[INFO] |  |  |  |  +- io.springfox:springfox-spi:jar:2.9.2:compile
[INFO] |  |  |  |  |  \- io.springfox:springfox-core:jar:2.9.2:compile
[INFO] |  |  |  |  +- io.springfox:springfox-schema:jar:2.9.2:compile
[INFO] |  |  |  |  +- io.springfox:springfox-swagger-common:jar:2.9.2:compile
[INFO] |  |  |  |  +- io.springfox:springfox-spring-web:jar:2.9.2:compile
[INFO] |  |  |  |  +- org.springframework.plugin:spring-plugin-core:jar:1.2.0.RELEASE:compile
[INFO] |  |  |  |  +- org.springframework.plugin:spring-plugin-metadata:jar:1.2.0.RELEASE:compile
[INFO] |  |  |  |  \- org.mapstruct:mapstruct:jar:1.2.0.Final:compile
[INFO] |  |  |  +- io.springfox:springfox-swagger-ui:jar:2.9.2:compile
[INFO] |  |  |  +- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel:jar:2.1.4.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-actuator:jar:2.3.12.RELEASE:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:2.3.12.RELEASE:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-transport-simple-http:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- com.alibaba.csp:sentinel-transport-common:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-annotation-aspectj:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- com.alibaba.csp:sentinel-core:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-dubbo-adapter:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-spring-webflux-adapter:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- com.alibaba.csp:sentinel-reactor-adapter:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-spring-webmvc-adapter:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-parameter-flow-control:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:jar:1.4.2:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-cluster-server-default:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- com.alibaba.csp:sentinel-cluster-common-default:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-cluster-client-default:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:jar:2.1.4.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- com.alibaba.cloud:spring-cloud-alibaba-commons:jar:2.1.4.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.alibaba.csp:sentinel-datasource-nacos:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- com.alibaba.csp:sentinel-datasource-extension:jar:1.8.2.2-mone-SNAPSHOT:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.11.4:compile
[INFO] |  |  |  |     +- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.11.4:compile
[INFO] |  |  |  |     +- org.codehaus.woodstox:stax2-api:jar:4.2.1:compile
[INFO] |  |  |  |     \- com.fasterxml.woodstox:woodstox-core:jar:6.2.3:compile
[INFO] |  |  |  +- com.xiaomi.mone:miline-api:jar:1.0.1-SNAPSHOT:compile
[INFO] |  |  |  |  +- run.mone:gitlab:jar:1.5-mone-SNAPSHOT:compile
[INFO] |  |  |  |  +- com.mi.youpin:kc_register-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  |  |  +- run.mone:hera-monitor-api:jar:1.1-SNAPSHOT:compile
[INFO] |  |  |  |  |  \- run.mone:api:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |  |  +- run.mone:health-check-api:jar:1.0-inner-SNAPSHOT:compile
[INFO] |  |  |  |  \- run.mone:health-check-common:jar:1.0-inner-SNAPSHOT:compile
[INFO] |  |  |  +- com.squareup.okhttp3:okhttp:jar:3.14.9:compile
[INFO] |  |  |  |  \- com.squareup.okio:okio:jar:1.17.2:compile
[INFO] |  |  |  \- com.xiaomi.youpin:antlr:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |     +- org.antlr:antlr4-runtime:jar:4.7.1:compile
[INFO] |  |  |     +- org.antlr:antlr4:jar:4.7.1:compile
[INFO] |  |  |     |  +- org.antlr:antlr-runtime:jar:3.5.2:compile
[INFO] |  |  |     |  +- org.antlr:ST4:jar:4.0.8:compile
[INFO] |  |  |     |  +- org.abego.treelayout:org.abego.treelayout.core:jar:1.0.3:compile
[INFO] |  |  |     |  +- org.glassfish:javax.json:jar:1.0.4:compile
[INFO] |  |  |     |  \- com.ibm.icu:icu4j:jar:58.2:compile
[INFO] |  |  |     \- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  |  +- com.xiaomi.youpin:gwdash-api:jar:1.0.1-SNAPSHOT:compile
[INFO] |  |  |  +- com.xiaomi.youpin:tesla-quota-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  |  +- com.xiaomi.youpin:oracle-api:jar:1.0-SNAPSHOT:compile
[INFO] |  |  |  +- mone-tenant:mone-tenant-api:jar:1.0-SNAPSHOT:compile
[INFO] |  |  |  \- com.xiaomi.youpin:gitlab:jar:1.5-SNAPSHOT:compile
[INFO] |  |  |     +- org.eclipse.jgit:org.eclipse.jgit:jar:5.1.3.201810200350-r:compile
[INFO] |  |  |     |  +- com.jcraft:jsch:jar:0.1.54:compile
[INFO] |  |  |     |  +- com.jcraft:jzlib:jar:1.1.1:compile
[INFO] |  |  |     |  \- com.googlecode.javaewah:JavaEWAH:jar:1.1.6:compile
[INFO] |  |  |     +- org.gitlab4j:gitlab4j-api:jar:4.12.2:compile
[INFO] |  |  |     |  +- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:jar:2.11.4:compile
[INFO] |  |  |     |  |  \- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:jar:2.11.4:compile
[INFO] |  |  |     |  +- org.glassfish.jersey.inject:jersey-hk2:jar:2.30.1:compile
[INFO] |  |  |     |  |  +- org.glassfish.jersey.core:jersey-common:jar:2.30.1:compile
[INFO] |  |  |     |  |  |  +- org.glassfish.hk2:osgi-resource-locator:jar:1.0.3:compile
[INFO] |  |  |     |  |  |  \- com.sun.activation:jakarta.activation:jar:1.2.2:compile
[INFO] |  |  |     |  |  \- org.glassfish.hk2:hk2-locator:jar:2.6.1:compile
[INFO] |  |  |     |  |     +- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.6.1:compile
[INFO] |  |  |     |  |     +- org.glassfish.hk2:hk2-api:jar:2.6.1:compile
[INFO] |  |  |     |  |     \- org.glassfish.hk2:hk2-utils:jar:2.6.1:compile
[INFO] |  |  |     |  +- org.glassfish.jersey.core:jersey-client:jar:2.30.1:compile
[INFO] |  |  |     |  |  +- jakarta.ws.rs:jakarta.ws.rs-api:jar:2.1.6:compile
[INFO] |  |  |     |  |  \- org.glassfish.hk2.external:jakarta.inject:jar:2.6.1:compile
[INFO] |  |  |     |  +- org.glassfish.jersey.connectors:jersey-apache-connector:jar:2.30.1:compile
[INFO] |  |  |     |  +- org.glassfish.jersey.media:jersey-media-multipart:jar:2.30.1:compile
[INFO] |  |  |     |  |  \- org.jvnet.mimepull:mimepull:jar:1.9.14:compile
[INFO] |  |  |     |  \- jakarta.servlet:jakarta.servlet-api:jar:4.0.4:compile
[INFO] |  |  |     \- commons-io:commons-io:jar:2.6:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.10.0:compile
[INFO] |  |  +- com.xiaomi.youpin:tesla-k8s-proxy-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  +- com.xiaomi.mone:mimonitor-api:jar:1.0-SNAPSHOT:compile
[INFO] |  |  |  \- com.xiaomi.youpin:api:jar:1.4-SNAPSHOT:compile
[INFO] |  |  +- run.mone:moon-schedule-dashboard-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  |  +- com.xiaomi.mone:mi-tpclogin-sdk:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  |  +- mifaas-dash:mifaas-dash-api:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  |  |  \- io.github.tesla:tesla-api:jar:1.0.4-SNAPSHOT:compile
[INFO] |  |  |  \- run.mone:schedule:jar:1.0-moon-SNAPSHOT:compile
[INFO] |  |  |     +- run.mone:common:jar:1.0-moon-SNAPSHOT:compile
[INFO] |  |  |     |  \- run.mone:crypto:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |     +- run.mone:feishu:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |     +- run.mone:annotation:jar:1.6-SNAPSHOT:compile
[INFO] |  |  |     |  +- run.mone:redis:jar:1.8-SNAPSHOT:compile
[INFO] |  |  |     |  |  +- run.mone:catPlugin:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |     |  |  \- run.mone:prometheus-client:jar:0.0.5-SNAPSHOT:compile
[INFO] |  |  |     |  \- com.xiaomi.common.perfcounter:xiaomi-common-perfcounter:jar:2.8.8:compile
[INFO] |  |  |     +- run.mone:rpc:jar:1.7-SNAPSHOT:compile
[INFO] |  |  |     |  +- run.mone:nacos:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |     |  +- org.msgpack:jackson-dataformat-msgpack:jar:0.8.21:compile
[INFO] |  |  |     |  |  \- org.msgpack:msgpack-core:jar:0.8.21:compile
[INFO] |  |  |     |  +- run.mone:easy:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |     |  \- run.mone:rpc-codes:jar:1.4-SNAPSHOT:compile
[INFO] |  |  |     |     +- io.protostuff:protostuff-core:jar:1.8.0:compile
[INFO] |  |  |     |     |  \- io.protostuff:protostuff-api:jar:1.8.0:compile
[INFO] |  |  |     |     \- io.protostuff:protostuff-runtime:jar:1.8.0:compile
[INFO] |  |  |     |        \- io.protostuff:protostuff-collectionschema:jar:1.8.0:compile
[INFO] |  |  |     \- com.xiaomi.youpin:redis:jar:1.5-SNAPSHOT:compile
[INFO] |  |  |        \- com.dianping.cat:cat-client:jar:3.0.1-youpin-SNAPSHOT:compile
[INFO] |  |  |           \- org.unidal.framework:foundation-service:jar:2.5.7:compile
[INFO] |  |  |              +- org.codehaus.plexus:plexus-container-default:jar:1.6:compile
[INFO] |  |  |              |  \- org.codehaus.plexus:plexus-classworlds:jar:2.5.1:compile
[INFO] |  |  |              +- org.codehaus.plexus:plexus-utils:jar:3.0.24:compile
[INFO] |  |  |              \- org.apache.xbean:xbean-reflect:jar:4.5:compile
[INFO] |  |  \- run.mone.ai:z-api:jar:1.0-SNAPSHOT:compile
[INFO] |  |     +- ai.djl:api:jar:0.21.0:compile
[INFO] |  |     |  +- net.java.dev.jna:jna:jar:5.13.0:compile
[INFO] |  |     |  \- org.apache.commons:commons-compress:jar:1.22:compile
[INFO] |  |     \- com.xiaomi.mone:miapi-doc-annos:jar:2.7.12-mone-v5-SNAPSHOT:compile
[INFO] |  +- com.xiaomi:dayu-dao:jar:0.3.2-SNAPSHOT:compile
[INFO] |  |  +- org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.2.0:compile
[INFO] |  |  |  +- org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.2.0:compile
[INFO] |  |  |  +- org.mybatis:mybatis:jar:3.5.7:compile
[INFO] |  |  |  \- org.mybatis:mybatis-spring:jar:2.0.6:compile
[INFO] |  |  \- com.github.pagehelper:pagehelper:jar:5.0.0:compile
[INFO] |  |     \- com.github.jsqlparser:jsqlparser:jar:0.9.5:compile
[INFO] |  +- com.xiaomi.youpin:nacos:jar:1.4-SNAPSHOT:compile
[INFO] |  +- com.xiaomi.youpin:cron:jar:1.4-SNAPSHOT:compile
[INFO] |  +- com.github.ben-manes.caffeine:caffeine:jar:2.8.8:compile
[INFO] |  +- org.springframework:spring-test:jar:5.2.15.RELEASE:compile
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:2.3.12.RELEASE:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:2.3.12.RELEASE:compile
[INFO] |  +- com.ecwid.consul:consul-api:jar:1.4.2:compile
[INFO] |  |  \- org.apache.httpcomponents:httpcore:jar:4.4.14:compile
[INFO] |  +- org.apache.zookeeper:zookeeper:jar:3.7.0:compile
[INFO] |  |  +- org.apache.zookeeper:zookeeper-jute:jar:3.7.0:compile
[INFO] |  |  +- org.apache.yetus:audience-annotations:jar:0.12.0:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.65.Final:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.65.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.65.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.65.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport:jar:4.1.65.Final:compile
[INFO] |  |  |  \- io.netty:netty-codec:jar:4.1.65.Final:compile
[INFO] |  |  +- io.netty:netty-transport-native-epoll:jar:4.1.65.Final:compile
[INFO] |  |  |  \- io.netty:netty-transport-native-unix-common:jar:4.1.65.Final:compile
[INFO] |  |  +- org.slf4j:slf4j-log4j12:jar:1.7.30:compile
[INFO] |  |  \- log4j:log4j:jar:1.2.17:compile
[INFO] |  +- com.xiaomi.infosec:aegis-java-sdk:jar:2.3.2:compile
[INFO] |  +- com.pszymczyk.consul:embedded-consul:jar:2.0.0:compile
[INFO] |  |  +- org.codehaus.groovy:groovy-all:jar:2.4.7:compile
[INFO] |  |  +- org.apache.ant:ant:jar:1.9.4:compile
[INFO] |  |  |  \- org.apache.ant:ant-launcher:jar:1.9.4:compile
[INFO] |  |  \- org.codehaus.groovy.modules.http-builder:http-builder:jar:0.7.1:compile
[INFO] |  |     +- net.sf.json-lib:json-lib:jar:jdk15:2.3:compile
[INFO] |  |     |  +- commons-beanutils:commons-beanutils:jar:1.8.0:compile
[INFO] |  |     |  +- commons-logging:commons-logging:jar:1.1.1:compile
[INFO] |  |     |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile
[INFO] |  |     +- net.sourceforge.nekohtml:nekohtml:jar:1.9.22:compile
[INFO] |  |     |  \- xerces:xercesImpl:jar:2.11.0:compile
[INFO] |  |     |     \- xml-apis:xml-apis:jar:1.4.01:compile
[INFO] |  |     \- xml-resolver:xml-resolver:jar:1.2:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.8.1:compile
[INFO] |  +- org.springframework.data:spring-data-commons:jar:2.3.9.RELEASE:compile
[INFO] |  |  \- org.springframework:spring-beans:jar:5.2.15.RELEASE:compile
[INFO] |  \- org.apache.rocketmq:rocketmq-spring-boot-starter:jar:2.2.0-mdh2.2.4-RELEASE:compile
[INFO] |     \- org.apache.rocketmq:rocketmq-spring-boot:jar:2.2.0-mdh2.2.4-RELEASE:compile
[INFO] |        +- org.apache.rocketmq:rocketmq-client:jar:4.8.0-mdh4.8.4.3-RELEASE:compile
[INFO] |        |  +- org.apache.rocketmq:rocketmq-common:jar:4.8.0-mdh4.8.4.3-RELEASE:compile
[INFO] |        |  +- io.dropwizard.metrics:metrics-core:jar:4.1.22:compile
[INFO] |        |  \- io.prometheus.jmx:jmx_prometheus_javaagent:jar:0.13.0:compile
[INFO] |        \- org.apache.rocketmq:rocketmq-acl:jar:4.8.0-mdh4.8.4.3-RELEASE:compile
[INFO] |           +- org.apache.rocketmq:rocketmq-remoting:jar:4.8.0-mdh4.8.4.3-RELEASE:compile
[INFO] |           |  \- io.netty:netty-tcnative-boringssl-static:jar:2.0.39.Final:compile
[INFO] |           +- org.apache.rocketmq:rocketmq-logging:jar:4.8.0-mdh4.8.4.3-RELEASE:compile
[INFO] |           +- org.apache.rocketmq:rocketmq-srvutil:jar:4.8.0-mdh4.8.4.3-RELEASE:compile
[INFO] |           |  \- commons-cli:commons-cli:jar:1.2:compile
[INFO] |           +- org.apache.rocketmq:rocketmq-metamanager:jar:4.8.0-mdh4.8.4.3-RELEASE:compile
[INFO] |           |  \- org.apache.commons:commons-dbcp2:jar:2.7.0:compile
[INFO] |           +- commons-validator:commons-validator:jar:1.6:compile
[INFO] |           |  \- commons-digester:commons-digester:jar:1.8.1:compile
[INFO] |           +- com.xiaomi.infra.galaxy:cloud-authentication:jar:1.3.9.5:compile
[INFO] |           |  \- com.xiaomi.infra.galaxy:cloud-authentication-core:jar:1.3.9.5:compile
[INFO] |           +- com.xiaomi:keycenter-agent-client:jar:2.1.12:compile
[INFO] |           |  +- org.json:json:jar:20090211:compile
[INFO] |           |  +- com.xiaomi:xiaomi-common-utils:jar:2.7.4:compile
[INFO] |           |  +- com.typesafe:config:jar:1.2.1:compile
[INFO] |           |  \- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |           \- com.xiaomi.infra.galaxy:galaxy-thrift-api:jar:1.3.5:compile
[INFO] |              \- org.xerial.snappy:snappy-java:jar:1.1.1.6:compile
[INFO] +- com.alibaba.spring:spring-context-support:jar:1.0.11:compile
[INFO] +- com.alibaba.boot:nacos-config-spring-boot-starter:jar:0.2.7:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:2.3.12.RELEASE:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.3.12.RELEASE:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO] |  |  \- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  +- com.alibaba.boot:nacos-config-spring-boot-autoconfigure:jar:0.2.7:compile
[INFO] |  \- com.alibaba.boot:nacos-spring-boot-base:jar:0.2.7:compile
[INFO] +- com.alibaba.nacos:nacos-spring-context:jar:1.1.1:compile
[INFO] |  +- org.springframework:spring-context:jar:5.2.15.RELEASE:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:5.2.15.RELEASE:compile
[INFO] |  |  \- org.springframework:spring-expression:jar:5.2.15.RELEASE:compile
[INFO] |  \- org.apache.commons:commons-lang3:jar:3.10:compile
[INFO] +- commons-collections:commons-collections:jar:3.2.1:compile
[INFO] +- org.hibernate:hibernate-validator:jar:6.1.6.Final:compile
[INFO] |  +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |  +- org.jboss.logging:jboss-logging:jar:3.4.2.Final:compile
[INFO] |  \- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] +- org.hibernate:hibernate-core:jar:5.1.0.Final:compile
[INFO] |  +- org.hibernate.javax.persistence:hibernate-jpa-2.1-api:jar:1.0.0.Final:compile
[INFO] |  +- org.javassist:javassist:jar:3.20.0-GA:compile
[INFO] |  +- antlr:antlr:jar:2.7.7:compile
[INFO] |  +- org.apache.geronimo.specs:geronimo-jta_1.1_spec:jar:1.1.1:compile
[INFO] |  +- org.jboss:jandex:jar:2.0.0.Final:compile
[INFO] |  +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  \- org.hibernate.common:hibernate-commons-annotations:jar:5.0.1.Final:compile
[INFO] +- org.hibernate:hibernate-entitymanager:jar:5.1.0.Final:compile
[INFO] +- com.alibaba:druid-spring-boot-starter:jar:1.2.1:compile
[INFO] |  +- com.alibaba:druid:jar:1.2.1:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:1.7.30:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:2.3.12.RELEASE:compile
[INFO] +- mysql:mysql-connector-java:jar:5.1.46:compile
[INFO] +- com.xiaomi.youpin:hermes-api:jar:0.0.3-SNAPSHOT:compile
[INFO] |  +- com.google.code.gson:gson:jar:2.8.7:compile
[INFO] |  \- com.xiaomi.youpin:http:jar:1.4-SNAPSHOT:compile
[INFO] |     +- io.netty:netty-all:jar:4.1.65.Final:compile
[INFO] |     \- com.xiaomi.youpin:catPlugin:jar:1.4-SNAPSHOT:compile
[INFO] +- org.springframework.boot:spring-boot-starter-web:jar:2.3.12.RELEASE:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-json:jar:2.3.12.RELEASE:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.11.4:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.11.4:compile
[INFO] |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.11.4:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-tomcat:jar:2.3.12.RELEASE:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.46:compile
[INFO] |  |  +- org.glassfish:jakarta.el:jar:3.0.3:compile
[INFO] |  |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.46:compile
[INFO] |  +- org.springframework:spring-web:jar:5.2.15.RELEASE:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:5.2.15.RELEASE:compile
[INFO] +- org.springframework.boot:spring-boot-starter-data-jpa:jar:2.6.5:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:2.3.12.RELEASE:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-*******************************
[INFO] |  |  +- com.zaxxer:HikariCP:jar:3.4.5:compile
[INFO] |  |  \- org.springframework:spring-*******************************
[INFO] |  +- jakarta.transaction:jakarta.transaction-api:jar:1.3.3:compile
[INFO] |  +- jakarta.persistence:jakarta.persistence-api:jar:2.2.3:compile
[INFO] |  +- org.springframework.data:spring-data-jpa:jar:2.3.9.RELEASE:compile
[INFO] |  |  +- org.springframework:spring-orm:jar:5.2.15.RELEASE:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.2.15.RELEASE:compile
[INFO] |  |  \- org.aspectj:aspectjrt:jar:1.9.6:compile
[INFO] |  \- org.springframework:spring-aspects:jar:5.2.15.RELEASE:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:2.3.12.RELEASE:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.3.12.RELEASE:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.4.0:test
[INFO] |  |  \- net.minidev:json-smart:jar:2.3.1:test
[INFO] |  |     \- net.minidev:accessors-smart:jar:2.3.1:test
[INFO] |  |        \- org.ow2.asm:asm:jar:5.0.4:test
[INFO] |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:test
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:test
[INFO] |  +- org.assertj:assertj-core:jar:3.16.1:compile
[INFO] |  +- org.hamcrest:hamcrest:jar:2.2:compile
[INFO] |  +- org.junit.jupiter:junit-jupiter:jar:5.6.3:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.6.3:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.6.3:test
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.6.3:test
[INFO] |  +- org.mockito:mockito-junit-jupiter:jar:3.3.3:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.0:test
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:test
[INFO] |  +- org.springframework:spring-core:jar:5.2.15.RELEASE:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.2.15.RELEASE:compile
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.7.0:test
[INFO] +- redis.clients:jedis:jar:2.9.0:compile
[INFO] +- com.ctrip.framework.apollo:apollo-openapi:jar:1.2.0:compile
[INFO] |  +- com.ctrip.framework.apollo:apollo-core:jar:1.2.0:compile
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.5.13:compile
[INFO] |  \- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] +- org.mybatis.generator:mybatis-generator-core:jar:1.3.6:compile
[INFO] +- run.mone:umami:jar:1.4-SNAPSHOT:compile
[INFO] |  \- run.mone:http:jar:1.4-SNAPSHOT:compile
[INFO] +- com.alibaba.nacos:nacos-client:jar:2.1.2-XIAOMI:compile
[INFO] |  +- com.alibaba.nacos:nacos-auth-plugin:jar:2.1.2-XIAOMI:compile
[INFO] |  +- com.alibaba.nacos:nacos-encryption-plugin:jar:2.1.2-XIAOMI:compile
[INFO] |  +- commons-codec:commons-codec:jar:1.14:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.11.4:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.11.4:compile
[INFO] |  \- org.apache.httpcomponents:httpasyncclient:jar:4.1.4:compile
[INFO] |     \- org.apache.httpcomponents:httpcore-nio:jar:4.4.14:compile
[INFO] +- org.yaml:snakeyaml:jar:1.24:compile
[INFO] +- org.apache.curator:curator-framework:jar:2.12.0:compile
[INFO] |  \- org.apache.curator:curator-client:jar:2.12.0:compile
[INFO] +- org.apache.curator:curator-recipes:jar:2.12.0:compile
[INFO] +- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.apache.curator:curator-test:jar:4.1.0:test
[INFO] +- org.mockito:mockito-core:jar:3.8.0:test
[INFO] |  +- net.bytebuddy:byte-buddy:jar:1.10.22:compile
[INFO] |  +- net.bytebuddy:byte-buddy-agent:jar:1.10.22:compile
[INFO] |  \- org.objenesis:objenesis:jar:3.1:test
[INFO] +- org.jasig.cas.client:cas-client-core:jar:3.5.0:compile
[INFO] +- com.google.guava:guava:jar:30.0-jre:compile
[INFO] |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  +- org.checkerframework:checker-qual:jar:3.5.0:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.3.4:compile
[INFO] |  \- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] +- io.projectreactor:reactor-core:jar:3.4.16:compile
[INFO] |  \- org.reactivestreams:reactive-streams:jar:1.0.3:compile
[INFO] +- org.junit.vintage:junit-vintage-engine:jar:5.8.2:test
[INFO] |  +- org.junit.platform:junit-platform-engine:jar:1.6.3:test
[INFO] |  |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  |  \- org.junit.platform:junit-platform-commons:jar:1.6.3:test
[INFO] |  +- junit:junit:jar:4.13.2:compile
[INFO] |  |  \- org.hamcrest:hamcrest-core:jar:2.2:compile
[INFO] |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] +- org.springframework.boot:spring-boot-starter-validation:jar:2.1.2.RELEASE:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.46:compile
[INFO] |  \- org.hibernate.validator:hibernate-validator:jar:6.1.7.Final:compile
[INFO] +- com.taobao.arthas:arthas-spring-boot-starter:jar:3.6.6-SNAPSHOT:compile
[INFO] |  +- com.taobao.arthas:arthas-agent-attach:jar:3.6.6-SNAPSHOT:compile
[INFO] |  |  \- org.zeroturnaround:zt-zip:jar:1.14:compile
[INFO] |  \- com.taobao.arthas:arthas-packaging:jar:3.6.6-SNAPSHOT:compile
[INFO] +- org.springframework.boot:spring-boot-starter-websocket:jar:2.3.12.RELEASE:compile
[INFO] |  +- org.springframework:spring-messaging:jar:5.2.15.RELEASE:compile
[INFO] |  \- org.springframework:spring-websocket:jar:5.2.15.RELEASE:compile
[INFO] \- javax.annotation:javax.annotation-api:jar:1.3.2:compile
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:25 min
[INFO] Finished at: 2025-08-12T17:41:39+08:00
[INFO] ------------------------------------------------------------------------
