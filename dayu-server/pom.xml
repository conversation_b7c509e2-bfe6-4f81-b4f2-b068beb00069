<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xiaomi</groupId>
		<artifactId>dayu</artifactId>
		<version>${revision}</version>
	</parent>
	<groupId>com.xiaomi</groupId>
	<artifactId>dayu-server</artifactId>
	<version>${revision}</version>
	<dependencies>
<!--		<dependency>-->
<!--			<groupId>com.alibaba</groupId>-->
<!--			<artifactId>dubbo-registry-nacos</artifactId>-->
<!--			<version>${dubbo-registry-nacos}</version>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>com.xiaomi</groupId>-->
<!--			<artifactId>xiaomi-iauth-java-sdk</artifactId>-->
<!--			<version>0.0.2-SNAPSHOT</version>-->
<!--			            <version>2.5.10.3-requirement-SNAPSHOT</version>-->
<!--			                        <version>2.5.10.3-china-area-requirement-SNAPSHOT</version>-->
<!--		</dependency>-->
<!--		<dependency>
			<groupId>com.xiaomi.iauth</groupId>
			<artifactId>xiaomi-iauth-platform-java-sdk</artifactId>
			<version>0.0.2-SNAPSHOT</version>
		</dependency>-->

		<dependency>
			<artifactId>test0930-api</artifactId>
			<groupId>com.xiaomi.youpin</groupId>
			<version>2.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.xiaomi</groupId>
			<artifactId>dayu-service</artifactId>
			<version>${revision}</version>
			<exclusions>
				<exclusion>
					<artifactId>dubbo-common</artifactId>
					<groupId>org.apache.dubbo</groupId>
				</exclusion>
				<exclusion>
					<artifactId>batch-deploy-operator-api</artifactId>
					<groupId>run.mone</groupId>
				</exclusion>
				<!--<exclusion>
					<artifactId>spring-context-support</artifactId>
					<groupId>com.alibaba.spring</groupId>
				</exclusion>-->
			</exclusions>

		</dependency>

		<dependency>
			<artifactId>spring-context-support</artifactId>
			<groupId>com.alibaba.spring</groupId>
			<version>1.0.11</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.alibaba.boot/nacos-config-spring-boot-starter -->
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>nacos-config-spring-boot-starter</artifactId>
			<version>0.2.7</version>
			<exclusions>
				<exclusion>
					<artifactId>nacos-spring-context</artifactId>
					<groupId>com.alibaba.nacos</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-to-slf4j</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.nacos</groupId>
			<artifactId>nacos-spring-context</artifactId>
<!--			<version>0.3.6-mone-SNAPSHOT</version>-->
			<version>1.1.1</version>
			<exclusions>
				<exclusion>
					<artifactId>spring-context-support</artifactId>
					<groupId>com.alibaba.spring</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>6.1.6.Final</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>5.1.0.Final</version>
			<exclusions>
				<exclusion>
					<artifactId>xml-apis</artifactId>
					<groupId>xml-apis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
			<version>5.1.0.Final</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.46</version>
		</dependency>



		<dependency>
			<groupId>com.xiaomi.youpin</groupId>
			<artifactId>hermes-api</artifactId>
			<version>0.0.3-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>youpin-infra-rpc</artifactId>
					<groupId>com.xiaomi.youpin</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<version> 2.6.5</version>
			<exclusions>
				<exclusion>
					<artifactId>hibernate-core</artifactId>
					<groupId>org.hibernate</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>

		<!--        <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <version> 2.1.6.RELEASE</version>
                </dependency>-->

		<dependency>
			<groupId>com.ctrip.framework.apollo</groupId>
			<artifactId>apollo-openapi</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.mybatis.generator/mybatis-generator-core -->
		<dependency>
			<groupId>org.mybatis.generator</groupId>
			<artifactId>mybatis-generator-core</artifactId>
			<version>1.3.6</version>
       </dependency>
		<dependency>
			<groupId>run.mone</groupId>
			<artifactId>umami</artifactId>
			<version>1.4-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.nacos</groupId>
			<artifactId>nacos-client</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>dubbo-registry-nacos</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
				<exclusion>
					<artifactId>simpleclient</artifactId>
					<groupId>io.prometheus</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>

			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-test</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
			</exclusions>
			<scope>test</scope>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>org.apache.dubbo</groupId>-->
<!--			<artifactId>dubbo-serialization-kryo</artifactId>-->
<!--			<version>2.7.15</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<artifactId>kryo</artifactId>-->
<!--					<groupId>com.esotericsoftware</groupId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<artifactId>log4j</artifactId>-->
<!--					<groupId>log4j</groupId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<artifactId>slf4j-log4j12</artifactId>-->
<!--					<groupId>org.slf4j</groupId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>${mockito-version}</version>
			<scope>test</scope>
		</dependency>

		<!--cas-->
		<dependency>
			<groupId>org.jasig.cas.client</groupId>
			<artifactId>cas-client-core</artifactId>
			<version>3.5.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>

		<!--<dependency>
			<groupId>com.xiaomi.mit</groupId>
			<artifactId>mit-starter</artifactId>
			<version>1.0.14</version>
		</dependency>-->

		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-core</artifactId>
			<version>3.4.16</version>
		</dependency>

		<!-- 用于兼容Junit4和junit3 -->
		<dependency>
			<groupId>org.junit.vintage</groupId>
			<artifactId>junit-vintage-engine</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
			<version>2.1.2.RELEASE</version>
		</dependency>


		<dependency>
			<groupId>com.taobao.arthas</groupId>
			<artifactId>arthas-spring-boot-starter</artifactId>
			<version>3.6.6-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter-websocket</artifactId>-->
<!--		</dependency>-->
	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.0.2.RELEASE</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.3.7</version>
				<configuration>
					<verbose>true</verbose>
					<overwrite>true</overwrite>
				</configuration>

				<dependencies>
					<dependency>
						<groupId>mysql</groupId>
						<artifactId>mysql-connector-java</artifactId>
						<version>8.0.20</version>
					</dependency>
					<dependency>
						<groupId>com.itfsw</groupId>
						<artifactId>mybatis-generator-plugin</artifactId>
						<version>1.3.8</version>
					</dependency>
					<dependency>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-starter-websocket</artifactId>
						<version>2.2.2.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
			<!--<plugin>
				<groupId>run.mone</groupId>
				<artifactId>codecheck-maven-plugin</artifactId>
				<version>1.4-SNAPSHOT</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>pmd</goal>
						</goals>
					</execution>
				</executions>
			</plugin>-->
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>javax.annotation</id>
			<activation>
				<jdk>[1.11,)</jdk>
			</activation>
			<dependencies>
				<dependency>
					<groupId>javax.annotation</groupId>
					<artifactId>javax.annotation-api</artifactId>
					<version>1.3.2</version>
				</dependency>
			</dependencies>
		</profile>

		<!--<profile>
			<id>release</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-jar-plugin</artifactId>
						<version>2.6</version>
						<configuration>
							<excludes>
								<exclude>**/application.properties</exclude>
								<exclude>**/application-mione-dev.properties</exclude>
							</excludes>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>-->
	</profiles>



</project>
