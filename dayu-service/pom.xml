<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xiaomi</groupId>
		<artifactId>dayu</artifactId>
		<version>${revision}</version>
	</parent>
	<groupId>com.xiaomi</groupId>
	<artifactId>dayu-service</artifactId>

        <dependencies>
			<dependency>
				<groupId>com.xiaomi.youpin</groupId>
				<artifactId>feishu</artifactId>
				<version>1.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.xiaomi.mone</groupId>
				<artifactId>mone-threadpool</artifactId>
				<version>${mone-threadpool-version}</version>
			</dependency>

			<dependency>
				<groupId>com.xiaomi</groupId>
				<artifactId>dayu-api</artifactId>
				<version>${revision}</version>
			</dependency>
			<dependency>
				<artifactId>mischedule-api</artifactId>
				<groupId>com.xiaomi.youpin</groupId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.xiaomi</groupId>
				<artifactId>dayu-rpc</artifactId>
				<version>${revision}</version>
			</dependency>
			<dependency>
				<groupId>com.xiaomi</groupId>
				<artifactId>dayu-api</artifactId>
				<version>${revision}</version>
			</dependency>
			<dependency>
				<groupId>com.xiaomi</groupId>
				<artifactId>dayu-dao</artifactId>
				<version>${revision}</version>
			</dependency>
			<dependency>
				<groupId>com.xiaomi.youpin</groupId>
				<artifactId>nacos</artifactId>
				<version>1.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.xiaomi.youpin</groupId>
				<artifactId>cron</artifactId>
				<version>1.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.github.ben-manes.caffeine</groupId>
				<artifactId>caffeine</artifactId>
				<version>2.9.3</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-test</artifactId>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-test</artifactId>
			</dependency>
			<dependency>
				<groupId>com.ecwid.consul</groupId>
				<artifactId>consul-api</artifactId>
			</dependency>
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
			</dependency>
			<dependency>
				<groupId>com.xiaomi.infosec</groupId>
				<artifactId>aegis-java-sdk</artifactId>
				<version>2.3.2</version>
			</dependency>
			<dependency>
				<groupId>com.pszymczyk.consul</groupId>
				<artifactId>embedded-consul</artifactId>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-pool2</artifactId>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
			</dependency>
			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-framework</artifactId>
			</dependency>
			<dependency>
				<groupId>com.pszymczyk.consul</groupId>
				<artifactId>embedded-consul</artifactId>
			</dependency>
			<dependency>
				<groupId>com.ctrip.framework.apollo</groupId>
				<artifactId>apollo-openapi</artifactId>
			</dependency>
			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-test</artifactId>
			</dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-commons</artifactId>
            </dependency>
			<dependency>
				<groupId>org.springframework.data</groupId>
				<artifactId>spring-data-commons</artifactId>
			</dependency>
			<dependency>
				<groupId>org.apache.rocketmq</groupId>
				<artifactId>rocketmq-spring-boot-starter</artifactId>
				<version>2.2.0-mdh2.2.4-RELEASE</version>
			</dependency>
		</dependencies>


	<build>
	</build>


</project>
