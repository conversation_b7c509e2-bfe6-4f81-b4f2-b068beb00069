/*
//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.alibaba.dubbo.registry.nacos;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.listener.Event;
import com.alibaba.nacos.api.naming.listener.EventListener;
import com.alibaba.nacos.api.naming.listener.NamingEvent;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.utils.NetUtils;
import org.apache.dubbo.common.utils.UrlUtils;
import org.apache.dubbo.registry.NotifyListener;
import org.apache.dubbo.registry.support.FailbackRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NacosRegistry extends FailbackRegistry {
    private static final String[] ALL_SUPPORTED_CATEGORIES = (String[])of("providers", "consumers", "routers", "configurators");
    private static final int CATEGORY_INDEX = 0;
    private static final int SERVICE_INTERFACE_INDEX = 1;
    private static final int SERVICE_VERSION_INDEX = 2;
    private static final int SERVICE_GROUP_INDEX = 3;
    private static final String WILDCARD = "*";
    private static final String SERVICE_NAME_SEPARATOR = System.getProperty("nacos.service.name.separator", ":");
    private static final int PAGINATION_SIZE = Integer.getInteger("nacos.service.names.pagination.size", 100);
    private static final long LOOKUP_INTERVAL = Long.getLong("nacos.service.names.lookup.interval", 30L);
    private volatile ScheduledExecutorService scheduledExecutorService;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final NamingService namingService;
    private final ConcurrentMap<String, EventListener> nacosListeners;

    public NacosRegistry(URL url, NamingService namingService) {
        super(url);
        this.namingService = namingService;
        this.nacosListeners = new ConcurrentHashMap();
    }

    public boolean isAvailable() {
        return "UP".equals(this.namingService.getServerStatus());
    }

    public List<URL> lookup(final URL url) {
        final List<URL> urls = new LinkedList();
        this.execute(new NamingServiceCallback() {
            public void callback(NamingService namingService) throws NacosException {
                List<String> serviceNames = NacosRegistry.this.getServiceNames(url, (NotifyListener)null);
                Iterator var3 = serviceNames.iterator();

                while(var3.hasNext()) {
                    String serviceName = (String)var3.next();
                    List<Instance> instances = namingService.getAllInstances(serviceName);
                    urls.addAll(NacosRegistry.this.buildURLs(url, instances));
                }

            }
        });
        return urls;
    }

    public void doRegister(URL url) {
        final String serviceName = this.getServiceName(url);
        final Instance instance = this.createInstance(url);
        this.execute(new NamingServiceCallback() {
            public void callback(NamingService namingService) throws NacosException {
                namingService.registerInstance(serviceName, instance);
            }
        });
    }

    public void doUnregister(final URL url) {
        this.execute(new NamingServiceCallback() {
            public void callback(NamingService namingService) throws NacosException {
                String serviceName = NacosRegistry.this.getServiceName(url);
                Instance instance = NacosRegistry.this.createInstance(url);
                namingService.deregisterInstance(serviceName, instance.getIp(), instance.getPort());
            }
        });
    }

    public void doSubscribe(URL url, NotifyListener listener) {
        List<String> serviceNames = this.getServiceNames(url, listener);
        this.doSubscribe(url, listener, serviceNames);
    }

    private void doSubscribe(final URL url, final NotifyListener listener, final List<String> serviceNames) {
        this.execute(new NamingServiceCallback() {
            public void callback(NamingService namingService) throws NacosException {
                Iterator var2 = serviceNames.iterator();

                while(var2.hasNext()) {
                    String serviceName = (String)var2.next();
                    List<Instance> instances = namingService.getAllInstances(serviceName);
                    NacosRegistry.this.notifySubscriber(url, listener, instances);
                    NacosRegistry.this.subscribeEventListener(serviceName, url, listener);
                }

            }
        });
    }

    public void doUnsubscribe(URL url, NotifyListener listener) {
        if (this.isAdminProtocol(url)) {
            this.shutdownServiceNamesLookup();
        }

    }

    private void shutdownServiceNamesLookup() {
        if (this.scheduledExecutorService != null) {
            this.scheduledExecutorService.shutdown();
        }

    }

    private List<String> getServiceNames(URL url, NotifyListener listener) {
        if (this.isAdminProtocol(url)) {
            this.scheduleServiceNamesLookup(url, listener);
            return this.getServiceNamesForOps(url);
        } else {
            return this.doGetServiceNames(url);
        }
    }

    private boolean isAdminProtocol(URL url) {
        return "admin".equals(url.getProtocol());
    }

    private void scheduleServiceNamesLookup(final URL url, final NotifyListener listener) {
        if (this.scheduledExecutorService == null) {
            this.scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
            this.scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
                public void run() {
                    List<String> serviceNames = NacosRegistry.this.getAllServiceNames();
                    NacosRegistry.this.filterData(serviceNames, new NacosDataFilter<String>() {
                        public boolean accept(String serviceName) {
                            boolean accepted = false;
                            String[] var3 = NacosRegistry.ALL_SUPPORTED_CATEGORIES;
                            int var4 = var3.length;

                            for(int var5 = 0; var5 < var4; ++var5) {
                                String category = var3[var5];
                                String prefix = category + NacosRegistry.SERVICE_NAME_SEPARATOR;
                                if (StringUtils.startsWith(serviceName, prefix)) {
                                    accepted = true;
                                    break;
                                }
                            }

                            return accepted;
                        }
                    });
                    NacosRegistry.this.doSubscribe(url, listener, serviceNames);
                }
            }, LOOKUP_INTERVAL, LOOKUP_INTERVAL, TimeUnit.SECONDS);
        }

    }

    private List<String> getServiceNamesForOps(URL url) {
        List<String> serviceNames = this.getAllServiceNames();
        this.filterServiceNames(serviceNames, url);
        return serviceNames;
    }

    private List<String> getAllServiceNames() {
        final List<String> serviceNames = new LinkedList();
        this.execute(new NamingServiceCallback() {
            public void callback(NamingService namingService) throws NacosException {
                int pageIndex = 1;
                ListView<String> listView = namingService.getServicesOfServer(pageIndex, NacosRegistry.PAGINATION_SIZE);
                List<String> firstPageData = listView.getData();
                serviceNames.addAll(firstPageData);
                int count = listView.getCount();
                int pageNumbers = count / NacosRegistry.PAGINATION_SIZE;
                int remainder = count % NacosRegistry.PAGINATION_SIZE;
                if (remainder > 0) {
                    ++pageNumbers;
                }

                while(pageIndex < pageNumbers) {
                    ++pageIndex;
                    listView = namingService.getServicesOfServer(pageIndex, NacosRegistry.PAGINATION_SIZE);
                    serviceNames.addAll(listView.getData());
                }

            }
        });
        return serviceNames;
    }

    private void filterServiceNames(List<String> serviceNames, URL url) {
        final String[] categories = this.getCategories(url);
        final String targetServiceInterface = url.getServiceInterface();
        final String targetVersion = url.getParameter("version");
        final String targetGroup = url.getParameter("group");
        this.filterData(serviceNames, new NacosDataFilter<String>() {
            public boolean accept(String serviceName) {
                String[] segments = StringUtils.split(serviceName, NacosRegistry.SERVICE_NAME_SEPARATOR);
                int length = segments.length;
                if (length < 3) {
                    return false;
                } else {
                    String category = segments[0];
                    if (!ArrayUtils.contains(categories, category)) {
                        return false;
                    } else {
                        String serviceInterface = segments[1];
                        if (!"*".equals(targetServiceInterface) && !StringUtils.equals(targetServiceInterface, serviceInterface)) {
                            return false;
                        } else {
                            String version = segments[2];
                            if (!"*".equals(targetVersion) && !StringUtils.equals(targetVersion, version)) {
                                return false;
                            } else {
                                String group = length > 3 ? segments[3] : null;
                                return group == null || "*".equals(targetGroup) || StringUtils.equals(targetGroup, group);
                            }
                        }
                    }
                }
            }
        });
    }

    private <T> void filterData(Collection<T> collection, NacosDataFilter<T> filter) {
        Iterator<T> iterator = collection.iterator();

        while(iterator.hasNext()) {
            T data = iterator.next();
            if (!filter.accept(data)) {
                iterator.remove();
            }
        }

    }

    private List<String> doGetServiceNames(URL url) {
        String[] categories = this.getCategories(url);
        List<String> serviceNames = new ArrayList(categories.length);
        String[] var4 = categories;
        int var5 = categories.length;

        for(int var6 = 0; var6 < var5; ++var6) {
            String category = var4[var6];
            String serviceName = this.getServiceName(url, category);
            serviceNames.add(serviceName);
        }

        return serviceNames;
    }

    private List<URL> buildURLs(URL consumerURL, Collection<Instance> instances) {
        if (instances.isEmpty()) {
            return Collections.emptyList();
        } else {
            List<URL> urls = new LinkedList();
            Iterator var4 = instances.iterator();

            while(var4.hasNext()) {
                Instance instance = (Instance)var4.next();
                URL url = this.buildURL(instance);
                if (UrlUtils.isMatch(consumerURL, url)) {
                    urls.add(url);
                }
            }

            return urls;
        }
    }

    private void subscribeEventListener(String serviceName, final URL url, final NotifyListener listener) throws NacosException {
        if (this.nacosListeners.containsKey(serviceName)) {
            this.logger.info("contains serverName:{} {}", serviceName, url);
        }

        EventListener eventListener = new EventListener() {
            public void onEvent(Event event) {
                if (event instanceof NamingEvent) {
                    NamingEvent e = (NamingEvent)event;
                    NacosRegistry.this.notifySubscriber(url, listener, e.getInstances());
                }

            }
        };
        this.namingService.subscribe(serviceName, eventListener);
        this.nacosListeners.put(serviceName, eventListener);
    }

    private void notifySubscriber(URL url, NotifyListener listener, Collection<Instance> instances) {
        logger.info("notifySubscriber url:{}  instances:{}", url,JSON.toJSONString(instances));
        List<Instance> healthyInstances = new LinkedList(instances);
        this.filterHealthyInstances(healthyInstances);
        List<URL> urls = this.buildURLs(url, healthyInstances);
        this.notify(url, listener, urls);
    }

    private String[] getCategories(URL url) {
        return "*".equals(url.getServiceInterface()) ? ALL_SUPPORTED_CATEGORIES : (String[])of("providers");
    }

    private URL buildURL(Instance instance) {
        URL url = new URL((String)instance.getMetadata().get("protocol"), instance.getIp(), instance.getPort(), instance.getMetadata());
        return url;
    }

    private Instance createInstance(URL url) {
        String category = url.getParameter("category", "providers");
        URL newURL = url.addParameter("category", category);
        newURL = newURL.addParameter("protocol", url.getProtocol());
        String ip = NetUtils.getLocalHost();
        ip = url.getHost();
        int port = newURL.getParameter("bind.port", url.getPort());
        Instance instance = new Instance();
        instance.setIp(ip);
        instance.setPort(port);
        instance.setMetadata(new HashMap(newURL.getParameters()));
        return instance;
    }

    private String getServiceName(URL url) {
        String category = url.getParameter("category", "providers");
        return this.getServiceName(url, category);
    }

    private String getServiceName(URL url, String category) {
        StringBuilder serviceNameBuilder = new StringBuilder(category);
        this.appendIfPresent(serviceNameBuilder, url, "interface");
        this.appendIfPresent(serviceNameBuilder, url, "version");
        this.appendIfPresent(serviceNameBuilder, url, "group");
        return serviceNameBuilder.toString();
    }

    private void appendIfPresent(StringBuilder target, URL url, String parameterName) {
        String parameterValue = url.getParameter(parameterName);
        if (!StringUtils.isBlank(parameterValue)) {
            target.append(SERVICE_NAME_SEPARATOR).append(parameterValue);
        }

    }

    private void execute(NamingServiceCallback callback) {
        try {
            callback.callback(this.namingService);
        } catch (NacosException var3) {
            if (this.logger.isErrorEnabled()) {
                this.logger.error(var3.getErrMsg(), var3);
            }
        }

    }

    private void filterHealthyInstances(Collection<Instance> instances) {
        this.filterData(instances, new NacosDataFilter<Instance>() {
            public boolean accept(Instance data) {
                return data.isEnabled();
            }
        });
    }

    private static <T> T[] of(T... values) {
        return values;
    }

    interface NamingServiceCallback {
        void callback(NamingService var1) throws NacosException;
    }

    private interface NacosDataFilter<T> {
        boolean accept(T var1);
    }
}
*/
