package com.xiaomi.dayu.model.requests;

import com.xiaomi.dayu.mybatis.example.ConfigInfoExample;
import lombok.Builder;
import lombok.Value;

import java.util.List;

import static com.google.common.base.Strings.isNullOrEmpty;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/1/5
 */
@Builder
@Value
public class SearchConfigReq {

    String tenantId;

    String dataId;

    String groupId;

    String appName;

    Integer configType;

    Long pageNo;

    Integer pageSize;

    String envId;

    boolean withBlob;

    boolean asc;

    List<String> appNameList;

    public ConfigInfoExample toExample() {
        return ConfigInfoExample.builder()
                .limit(this.pageSize)
                .offset(this.pageNo == null || this.pageSize == null ? null : this.pageNo * this.pageSize)
                .orderByClause(this.asc ? null : "id DESC")
                .withBlob(this.withBlob)
                .build();
    }

    public ConfigInfoExample.Criteria defaultCriteria(ConfigInfoExample example) {
        ConfigInfoExample.Criteria criteria = example.createCriteria();

        if (!isNullOrEmpty(this.tenantId)) {
            criteria.andTenantIdEqualTo(this.tenantId);
        }
        if (!isNullOrEmpty(this.groupId)) {
            criteria.andGroupIdLike(this.groupId + "%");
        }
        if (!isNullOrEmpty(this.appName)) {
            criteria.andAppNameEqualTo(this.appName);
        } else if (this.appNameList != null && !this.appNameList.isEmpty()) {
            criteria.andAppNameIn(this.appNameList);
        }
        return criteria;
    }
}
