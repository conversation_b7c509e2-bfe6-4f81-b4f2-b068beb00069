package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.xiaomi.dayu.common.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class UserService {
    @NacosValue(value = "${adminList}", autoRefreshed = true)
    private List<String> adminList;

    @Value("${idm.base.url}")
    private String idmBaseUrl;
    @Value("${idm.app.id}")
    private String idmAppId;
    @Value("${idm.app.key}")
    private String idmAppKey;

   public String queryFullDeptByUid(String uid,String userName){
/*       if(CollectionUtils.isNotEmpty(adminList) && adminList.contains(userName)){
            return "1";
       }*/
       String result = HttpClient.queryFullDeptByUid(idmBaseUrl, idmAppId, idmAppKey, uid);
       if(StringUtils.isNotBlank(result)){
           JSONArray array = (JSONArray)JSON.parseObject(result).get("data");
           if(CollectionUtils.isNotEmpty(array)){
               for(int i=array.size()-1; i>=0 ;i--){
                   int level = ((JSONObject) array.get(i)).getIntValue("level");
                   if(1 == level || 2 == level){
                       String deptName = ((JSONObject) array.get(i)).getString("deptName");
                       if(StringUtils.isNotBlank(deptName)){
                           if(DeptEnum.CHAIN.getDeptName().equals(deptName)){
                               return DeptEnum.CHAIN.getDetpID()+"";
                           }else if(deptName.contains(DeptEnum.YOUPIN.getDeptName())){
                               return DeptEnum.YOUPIN.getDetpID()+"";
                           }else if(deptName.contains(DeptEnum.MIT.getDeptName())){
                               return DeptEnum.MIT.getDetpID()+"";
                           }
                       }
                   }
               }
           }
       }
       return null;
   }
    public String findUidByUserName(String userName){
        String uid = HttpClient.findUidByUserName(idmBaseUrl, idmAppId, idmAppKey, userName);
        if(StringUtils.isNotBlank(uid)){
            JSONObject jsonObject = JSONObject.parseObject(uid);
            if("success".equals(jsonObject.get("msg"))){
                JSONArray data = (JSONArray)jsonObject.get("data");
                if(CollectionUtils.isNotEmpty(data)){
                    return ((JSONObject)data.get(0)).getString("uid");
                }
            }
        }
        return null;
    }
    public String findDepartmentFullName(String uid){
        if(StringUtils.isNotBlank(uid)){
            String result = HttpClient.queryFullDeptByUid(idmBaseUrl, idmAppId, idmAppKey, uid);
            if(StringUtils.isNotBlank(result)) {
                JSONArray array = (JSONArray) JSON.parseObject(result).get("data");
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 1; i < array.size(); i++) {
                    stringBuilder.append(((JSONObject) array.get(i)).getString("deptName")).append("-");
                }
                return stringBuilder.substring(0,stringBuilder.length()-1);
            }
        }
        return null;
    }

    public Pair<String, Boolean> queryDepAndAdminByUid(String uid, String userName){

        String depart="1";
        String result = null;
        try{
            result = HttpClient.queryFullDeptByUid(idmBaseUrl, idmAppId, idmAppKey, uid);
            JSONArray array = null;
            if(StringUtils.isNotBlank(result)){
                array = (JSONArray)JSON.parseObject(result).get("data");
                if(CollectionUtils.isNotEmpty(array)){
                    for(int i=array.size()-1; i>=0 ;i--){
                        int level = ((JSONObject) array.get(i)).getIntValue("level");
                        if(1 == level || 2 == level){
                            String deptName = ((JSONObject) array.get(i)).getString("deptName");
                            if(StringUtils.isNotBlank(deptName)){
                                if(DeptEnum.CHAIN.getDeptName().equals(deptName)){
                                    depart = DeptEnum.CHAIN.getDetpID()+"";
                                }else if(deptName.contains(DeptEnum.YOUPIN.getDeptName())){
                                    depart = DeptEnum.YOUPIN.getDetpID()+"";
                                }else if(deptName.contains(DeptEnum.MIT.getDeptName())){
                                    depart = DeptEnum.MIT.getDetpID()+"";
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("queryDepAndAdminByUid 调用异常，result={}",result,e);
            e.printStackTrace();
        }
        Boolean isAdmin = false;
        if(CollectionUtils.isNotEmpty(adminList) && adminList.contains(userName)){
            isAdmin = true;
        }

        Pair<String, Boolean> pair = Pair.of(depart, isAdmin);
        return pair;
    }

   public boolean checkAdmin(String userName){
       if(CollectionUtils.isNotEmpty(adminList) && adminList.contains(userName)){
           return true;
       }
       return false;
   }

   public Set<String> getAdminSet() {
       return new HashSet<>(this.adminList);
   }

   public enum DeptEnum {

       CHAIN(2, "中国区"),
       YOUPIN(3, "有品"),
       MIT(4, "信息技术部");

       private final String deptName;
       private final int detpID;

       DeptEnum(int detpID, String deptName) {
           this.detpID = detpID;
           this.deptName = deptName;
       }

       public String getDeptName() {
           return deptName;
       }

       public int getDetpID() {
           return detpID;
       }
   }
}
