package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.DataIdReq;
import com.xiaomi.dayu.api.bo.DataIdResp;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.constants.ApproveTypeEnum;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.approval.ApprovalVO;
import com.xiaomi.dayu.mybatis.entity.Approval;
import com.xiaomi.dayu.mybatis.example.ApprovalExample;

import java.util.List;

public interface ApprovalService {

    public PageResult<ApprovalVO> queryByPage(String appName, Integer approveType, Integer status, Integer pageNum, Integer pageSize);

    boolean insert(Approval approval);

    boolean update(Approval approval, ApprovalExample example);

    Approval selectByPrimaryKey(Integer id);


     List<Approval> queryByTypeAndRelateKey(ApproveTypeEnum approveType, String relateKey);

    void createApproval(PublishConfigReq requestBody, Enums.OperateType operateType);

    Boolean updateStatus(Integer id, String appName, Integer status, String operateRemark);

	List<DataIdResp> query(DataIdReq dataIdReq);
}
