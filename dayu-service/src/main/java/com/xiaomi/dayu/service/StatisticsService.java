package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.RedisUtil;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
@Slf4j
@Service
public class StatisticsService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RegistryServerSync registryServerSync;

    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;

    @Value("${depart}")
    private String depart;

    private List<String> departList = Arrays.asList("chain","youpin");

    public void addUserView(String userName){
        String dateFormat = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        redisUtil.sadd(dateFormat+"-UV", userName);
    }
    public Set<String> queryUserView(String dateFormat){
        Set<String> smembers = redisUtil.smembers(dateFormat+"-UV");
        if(CollectionUtils.isEmpty(smembers)){
            smembers = new HashSet<>();
        }
        return smembers;
    }
    public int queryProjectNums(String date) {
        int num = 0;
        for (String key : departList) {
            String value = redisUtil.get(date + "-projectNums-"+key);
            if(StringUtils.isNotBlank(value)){
                num += Integer.parseInt(value);
            }
        }
        return num;
    }
    @Scheduled(cron="0 0 8 * * *")
    public void getProjectNum() {
        statisticsProject();
    }

    public Map<String,Object> statisticsProject(){


        ConcurrentMap<String, Map<String, URL>> providerMap = registryServerSync.getRegistryCache().get(Constants.PROVIDERS_CATEGORY);
        ConcurrentMap<String, Map<String, URL>> consumerMap = registryServerSync.getRegistryCache().get(Constants.CONSUMERS_CATEGORY);

        HashSet<String> dubboApps = new HashSet<>();
        for (Map<String, URL> values : providerMap.values()) {
            values.forEach((key,value)->dubboApps.add(value.getParameter("application")));
        }
        for (Map<String, URL> values : consumerMap.values()) {
            values.forEach((key,value)->dubboApps.add(value.getParameter("application")));
        }
        log.warn("dubbo-app-nums ="+dubboApps.size());
        ArrayList<String> list = new ArrayList<>(dubboApps);
        List<List<String>> partitions = Lists.partition(list, 100);


        HashSet<String> mioneApps = new HashSet<>();
        partitions.forEach(part->{
            List<ProjectBo> projectListByNameList = iProjectServiceRpc.getProjectListByNameList(part);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(projectListByNameList)){
                mioneApps.addAll(projectListByNameList.stream().map(projectBo -> projectBo.getName()).collect(Collectors.toSet()));
            }
        });
        log.warn("mione-app-nums ="+mioneApps.size());

        Collection dubbo_mione = org.apache.commons.collections.CollectionUtils.subtract(dubboApps, mioneApps);
        log.warn("dubbo-mione-nums ="+dubbo_mione.size());
        log.warn("dubbo-mion-app ="+ JSON.toJSONString(dubbo_mione));

        Collection dubbo_inter_mione = org.apache.commons.collections.CollectionUtils.intersection(mioneApps,dubboApps);
        log.warn("dubbo_inter_mione-nums ="+dubbo_inter_mione.size());
        log.warn("dubbo_inter_mione-app ="+ JSON.toJSONString(dubbo_inter_mione));

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("dubbo-app",dubboApps.size());
        hashMap.put("mione-app",mioneApps.size());
        hashMap.put("dubbo-mione-nums",dubbo_mione.size());
        hashMap.put("dubbo-mione-app",dubbo_mione);

        hashMap.put("dubbo_inter_mione-nums",dubbo_inter_mione.size());
        hashMap.put("dubbo_inter_mione-app",dubbo_inter_mione);
        setProjectNumsToRedis(mioneApps.size());
        return hashMap;
    }

    private void setProjectNumsToRedis(int value){
        String dateStr = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        String suffix = "-projectNums-"+depart;
        String v1 = redisUtil.get(dateStr+suffix);
        String storeValue = null;
        if(StringUtils.isBlank(v1)){
            LocalDate localDate = LocalDate.now().minusDays(1L);
            String v2 = redisUtil.get(localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            if(StringUtils.isNotBlank(v2) ){
                if(Integer.parseInt(v2)>value){
                    storeValue = v2;
                }else{
                   storeValue = value+"";
                }
            }else {
                storeValue = value+"";
            }
        }else{
            if(Integer.parseInt(v1)>value){
                storeValue = v1;
            }else{
                storeValue = value+"";
            }
        }
        redisUtil.set(dateStr+suffix,storeValue);
    }

}
