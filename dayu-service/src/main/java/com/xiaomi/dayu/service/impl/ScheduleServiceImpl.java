package com.xiaomi.dayu.service.impl;

import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.nacos.NacosNaming;
import com.xiaomi.dayu.model.requests.response.DubboService;
import com.xiaomi.dayu.model.requests.response.DubboServiceList;
import com.xiaomi.dayu.service.NacosLoginService;
import com.xiaomi.dayu.service.ScheduleService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ScheduleServiceImpl implements ScheduleService {

    public static final Gson gson = new Gson();

    private String DEFAULT_NAMESPACE = "";



    private static final String Dubbo_Service_Prefix = "providers:";


    @Autowired
    private NacosNaming namingService;
    @Autowired
    private NacosLoginService nacosLoginService;

    @Override
    public Result<List<DubboService>> loadDubboApiServices(String serviceName) throws NacosException {
        DubboServiceList serviceList = new DubboServiceList();

        String nacosAccessToken = nacosLoginService.getNacosAccessToken();
        String serviceStr = namingService.serviceList2(DEFAULT_NAMESPACE, 1, 20, serviceName, nacosAccessToken);
        if (Objects.nonNull(serviceStr) && StringUtils.isNotEmpty(serviceStr)) {
            try {
                serviceList = gson.fromJson(serviceStr, new TypeToken<DubboServiceList>() {
                }.getType());
            } catch (JsonSyntaxException e) {
                log.error("获取nacos服务列表失败,serviceList:{},accessToken:{}", serviceStr, nacosAccessToken,e);
                return Result.success(new ArrayList<>());
            }
        }
        if (Objects.nonNull(serviceList) && Objects.nonNull(serviceList.getServiceList())) {
            List<DubboService> services = serviceList.getServiceList().stream().filter(s -> (s.getHealthyInstanceCount() > 0 && s.getName().startsWith(Dubbo_Service_Prefix))).collect(Collectors.toList());
            services.forEach(dubboService -> {
                String[] split = dubboService.getName().split(":");
                if(split.length == 3){
                    try {
                        List<Instance> allInstances = namingService.getAllInstances(dubboService.getName());
                        if(CollectionUtils.isNotEmpty(allInstances)){
                            Map<String, String> metadata = allInstances.get(0).getMetadata();
                            dubboService.setGroup(metadata.get("group"));
                            dubboService.setVersion(metadata.get("version"));
                        }
                    } catch (NacosException e) {
                        e.printStackTrace();
                    }

                }else if(split.length == 4){
                    dubboService.setVersion(split[2]);
                    dubboService.setGroup(split[3]);
                }
                dubboService.setService(split[1]);
                dubboService.setName(dubboService.getName().substring(Dubbo_Service_Prefix.length()));
            });
            return Result.success(services);
        } else {
            return Result.success(new ArrayList<>());
        }
    }

    @Override
    public Result<List<String>> getServiceMethod(String serviceName) throws NacosException {
        List<String> methodNames;
        List<Instance> instanceList = namingService.getAllInstances(Dubbo_Service_Prefix + serviceName);
        if (Objects.nonNull(instanceList) && !instanceList.isEmpty()) {
            Instance instance = instanceList.get(0);
            String[] methodsArr = instance.getMetadata().getOrDefault("methods", "").split(",");
            methodNames = Arrays.stream(methodsArr).collect(Collectors.toList());
            return Result.success(methodNames);
        }
        return Result.success(new ArrayList<>());
    }


}
