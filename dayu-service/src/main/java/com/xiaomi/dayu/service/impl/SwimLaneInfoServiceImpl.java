package com.xiaomi.dayu.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.dayu.dao.SwimLaneGroupMapper;
import com.xiaomi.dayu.dao.SwimLaneMapper;
import com.xiaomi.dayu.mybatis.entity.SwimLaneGroup;
import com.xiaomi.dayu.mybatis.entity.SwimLaneGroupExample;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import swimlane.bo.AppEnvDTO;
import com.xiaomi.dayu.mybatis.entity.SwimLane;
import com.xiaomi.dayu.mybatis.entity.SwimLaneExample;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import swimlane.bo.SwimLaneConditionBo;
import swimlane.bo.SwimLaneGroupInfoBo;
import swimlane.bo.SwimLaneInfoBo;
import swimlane.service.SwimLaneInfoService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
@Slf4j
@DubboService(interfaceClass = SwimLaneInfoService.class, group = "${rpc.swimlane.group}")
public class SwimLaneInfoServiceImpl implements SwimLaneInfoService {

    @Autowired
    private SwimLaneGroupMapper swimLaneGroupMapper;
    @Autowired
    private SwimLaneMapper swimLaneMapper;

    public static final Gson gson = new Gson();

    private static Cache<String,List<SwimLaneGroupInfoBo>> localCache = CacheBuilder.newBuilder().expireAfterWrite(15, TimeUnit.SECONDS)
            .maximumSize(10).build();

    @Override
    public List<SwimLaneGroupInfoBo> getSwimLaneGroupsInfo() {

        try {
            return localCache.get("swimlane", () -> {
                log.info("查询泳道信息--------");
                List<SwimLaneGroupInfoBo> result = new ArrayList<>();
                List<SwimLaneGroup> swimLaneGroups = swimLaneGroupMapper.selectByExample(new SwimLaneGroupExample());
                Map<Integer, List<SwimLane>> groupIdSwimLaneMapTemp = new HashMap<>();
                if (CollectionUtils.isNotEmpty(swimLaneGroups)) {
                    SwimLaneExample example = new SwimLaneExample();
                    example.createCriteria().andSwimLaneGroupIdIn(swimLaneGroups.stream().map(SwimLaneGroup::getId).collect(Collectors.toList()));
                    List<SwimLane> swimLanes = swimLaneMapper.selectByExampleWithBLOBs(example);
                    groupIdSwimLaneMapTemp = swimLanes.stream().collect(Collectors.groupingBy(SwimLane::getSwimLaneGroupId));
                }
                final Map<Integer, List<SwimLane>> groupIdSwimLaneMap = groupIdSwimLaneMapTemp;
                swimLaneGroups.parallelStream().forEach(swimLaneGroup -> {
                    SwimLaneGroupInfoBo groupInfoBo = new SwimLaneGroupInfoBo();
                    groupInfoBo.setGroupName(swimLaneGroup.getName());
                    groupInfoBo.setPrefixHeader(swimLaneGroup.getPrefixHeader());

                    List<SwimLane> swimLanes = groupIdSwimLaneMap.get(swimLaneGroup.getId());
                    if (CollectionUtils.isNotEmpty(swimLanes)) {
                        List<SwimLaneInfoBo> swimLaneInfoBos = new ArrayList<>();
                        swimLanes.forEach(swimLane -> {
                            SwimLaneInfoBo swimLaneInfoBo = new SwimLaneInfoBo();
                            swimLaneInfoBo.setName(swimLane.getName());
                            swimLaneInfoBo.setFlowControlTag(swimLane.getFlowControlTag());
                            swimLaneInfoBo.setStatus(swimLane.getStatus());
                            swimLaneInfoBo.setAppEnvs(gson.fromJson(swimLane.getAppEnvJson(), new TypeToken<List<AppEnvDTO>>() {
                            }.getType()));
                            swimLaneInfoBo.setSwimLaneCondition(gson.fromJson(swimLane.getConditionJson(), new TypeToken<SwimLaneConditionBo>() {
                            }.getType()));
                            swimLaneInfoBos.add(swimLaneInfoBo);
                        });
                        groupInfoBo.setSwimLaneInfoBos(swimLaneInfoBos);
                        result.add(groupInfoBo);
                    }
                });
                return result;
            });
        } catch (Exception e) {
            log.error("SwimLaneInfoServiceImpl getSwimLaneGroupsInfo failed,cause by:",e);
            return null;
        }
    }
}
