/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xiaomi.dayu.service.impl;

import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.metrics.model.MetricsCategory;
import org.apache.dubbo.metrics.service.MetricsEntity;
import org.apache.dubbo.metrics.service.MetricsService;

import java.util.List;
import java.util.Map;

public class MetrcisCollectServiceImpl {

    private ReferenceConfig<MetricsService> referenceConfig;

    public MetrcisCollectServiceImpl() {
        referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("dayu"));
        referenceConfig.setInterface(MetricsService.class);
    }

    public void setUrl(String url) {
        referenceConfig.setUrl(url);
    }

    //    public Object invoke(String group) {
//        MetricsService metricsService = referenceConfig.get();
//        return metricsService.getMetricsByGroup(group);
//    }
    public Map<MetricsCategory, List<MetricsEntity>> invoke(List<MetricsCategory> categories) {
        MetricsService metricsService = referenceConfig.get();
        return metricsService.getMetricsByCategories(categories);
    }
}
