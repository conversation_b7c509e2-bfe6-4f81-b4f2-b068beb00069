package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.SentinelRulesReq;
import com.xiaomi.dayu.api.service.SentinelRulesProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Type SentinelRulesProviderImpl.java
 * @Desc
 * @date 2024/10/30 18:46
 */
@Slf4j
@DubboService(interfaceClass = SentinelRulesProvider.class,group = "${provider.group}")
public class SentinelRulesProviderImpl implements SentinelRulesProvider {
	@Resource
	private SearchConfigService searchConfigService;
	@Value("${rpc.auth}")
	private String rpcAuth;

	@Override
	public Result<PageResult> querySentinelAllRules(SentinelRulesReq sentinelRulesReq) {
		String auth = (String) RpcContext.getContext().getAttachment("auth");
		if(StringUtils.isBlank(auth)){
			return Result.fail(GeneralCodes.InternalError, "auth不能为空");
		}
		if(!rpcAuth.equals(auth)){
			return Result.fail(GeneralCodes.InternalError, "auth错误");
		}
		if(StringUtils.isBlank(sentinelRulesReq.getSuffix())){
			return Result.fail(GeneralCodes.InternalError, "suffix不能为空");
		}
		if(sentinelRulesReq.getPageSize() == 0){
			sentinelRulesReq.setPageSize(20);
		}
		if(sentinelRulesReq.getPageNo() == 0){
			sentinelRulesReq.setPageNo(1);
		}

		return Result.success(searchConfigService.querySentinelAllRules(sentinelRulesReq));
	}
}
