package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.SlaManageDTO;
import com.xiaomi.dayu.api.bo.SlaManageRequest;

public interface SlaManageService {
    PageResult<SlaManageDTO> queryList(SlaManageRequest slaManageRequest);

    Boolean create(SlaManageRequest slaManageRequest);

    Boolean update(SlaManageRequest slaManageRequest);

    Boolean delete(SlaManageRequest slaManageRequest);

    PageResult<SlaManageDTO> queryHis(SlaManageRequest slaManageRequest);
}
