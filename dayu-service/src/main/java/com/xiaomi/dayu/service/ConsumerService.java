/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service;

import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.dto.ConsumerServiceDTO;
import org.apache.dubbo.metadata.report.identifier.MetadataIdentifier;

import java.util.List;
import java.util.Set;

/**
 * Query service for consumer info
 *
 */
public interface ConsumerService {

    List<Consumer> findByService(String serviceName);

    Set<String> findConsumers();

    String getConsumerMetadata(MetadataIdentifier consumerIdentifier);

    List<Consumer> findAll();

    /**
     * query for all consumer addresses
     */
    List<Consumer> findByAddress(String consumerAddress);

    /**
     * query consumer application version.
     * @param application application name.
     * @return version.
     */
    String findVersionInApplication(String application);

    String getConsumerMetaData(MetadataIdentifier consumerIdentifier);


    Set<ConsumerServiceDTO> getServiceDTOS(String pattern, String filter, boolean self,boolean aggregate);
    void fillExtendInfo(List<ConsumerServiceDTO> content);

    Set<String> findServices();

    List<String> findAddresses();

    Set<String> findApplications();
}
