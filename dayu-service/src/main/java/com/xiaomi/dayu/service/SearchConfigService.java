package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.SentinelRulesReq;
import com.xiaomi.dayu.common.ConfigTypeEnum;
import com.xiaomi.dayu.model.dto.ConfigInfoAndExtendDTO;
import com.xiaomi.dayu.model.requests.SearchConfigReq;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;

import java.util.List;

public interface SearchConfigService {

    List<ConfigInfo> searchConfigByType(ConfigTypeEnum configTypeEnum);

    List<ConfigInfo> searchConfigByTypeAndApp(ConfigTypeEnum configTypeEnum,String application);

    List<ConfigInfo> queryConfigByIds(List<Long> ids);

    List<ConfigInfoAndExtendDTO> queryConfigAndExtendListByAppName(String appName);

    List<ConfigInfoAndExtendDTO> searchConfigAndExtend(SearchConfigReq request);

    Long searchConfigAndExtendTotal(SearchConfigReq request);

    long countTotalConfigs(SearchConfigReq request);

    int queryCountByAppNameNotEmpty();

    PageResult querySentinelAllRules(SentinelRulesReq sentinelRulesReq);



    ConfigInfo searchConfig(String tenant, String group, String dataId);
}
