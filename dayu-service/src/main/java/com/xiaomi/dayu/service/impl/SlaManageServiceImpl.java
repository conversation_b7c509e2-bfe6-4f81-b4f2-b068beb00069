package com.xiaomi.dayu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.SlaManageDTO;
import com.xiaomi.dayu.api.bo.SlaManageRequest;
import com.xiaomi.dayu.api.bo.SlaTypeDTO;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.dao.SlaManageHisMapper;
import com.xiaomi.dayu.dao.SlaManageMapper;
import com.xiaomi.dayu.mybatis.entity.SlaManage;
import com.xiaomi.dayu.mybatis.entity.SlaManageHis;
import com.xiaomi.dayu.mybatis.example.SlaManageExample;
import com.xiaomi.dayu.mybatis.example.SlaManageHisExample;
import com.xiaomi.dayu.service.SlaManageService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SlaManageServiceImpl implements SlaManageService {
    @Resource
    private SlaManageMapper slaManageMapper;
    @Resource
    private SlaManageHisMapper slaManageHisMapper;

    @Override
    public PageResult<SlaManageDTO> queryList(SlaManageRequest slaManageRequest) {
        SlaManageExample example = new SlaManageExample();
        SlaManageExample.Criteria criteria = example.createCriteria();
        criteria.andDelEqualTo(0);
        if (slaManageRequest.getType() != null) {
            criteria.andTypeEqualTo(slaManageRequest.getType());
        }
        if (slaManageRequest.getAppId() != null) {
            criteria.andAppIdEqualTo(slaManageRequest.getAppId());
        }
        example.setOrderByClause(SlaManage.Column.updateTime.desc());
        PageRowBounds pageRowBounds = new PageRowBounds((slaManageRequest.getPageNum() - 1) * slaManageRequest.getPageSize(), slaManageRequest.getPageSize());

        Page<SlaManage> list = (Page) slaManageMapper.selectByExampleWithRowbounds(example, pageRowBounds);
        List<SlaManageDTO> collect = list.stream().map(slaManage -> {
            SlaManageDTO slaManageDTO = new SlaManageDTO();
            BeanUtils.copyProperties(slaManage, slaManageDTO);
            slaManageDTO.setSlaContent(JSON.parseObject(slaManage.getSlaContent(), new TypeReference<List<SlaTypeDTO>>() {}));
            return slaManageDTO;
        }).collect(Collectors.toList());
        return new PageResult<>(collect, (int) list.getTotal(), slaManageRequest.getPageNum(), slaManageRequest.getPageSize());
    }

    @Override
    public Boolean create(SlaManageRequest slaManageRequest) {
        checkData(slaManageRequest,false);

        SlaManage slaManage = new SlaManage();
        slaManage.setType(slaManageRequest.getType());
        slaManage.setOperator(UserInfoThreadLocal.getUserInfo().getUserName());
        slaManage.setAppId(slaManageRequest.getAppId());
        slaManage.setAppName(slaManageRequest.getAppName());
        slaManage.setClassName(slaManageRequest.getClassName());
        slaManage.setMethodName(slaManageRequest.getMethodName());
        slaManage.setSlaContent(JSON.toJSONString(slaManageRequest.getSlaContent()));
        slaManage.setBusinessKey(buildBusinessKey(slaManageRequest));
        slaManage.setDel(0);
        slaManage.setCreateTime(new Date());
        slaManage.setUpdateTime(slaManage.getCreateTime());
        if ("dubbo".equals(slaManageRequest.getType())) {
            slaManage.setDubboGroup(slaManageRequest.getDubboGroup());
            slaManage.setDubboVersion(slaManageRequest.getDubboVersion());
        } else if ("http".equals(slaManageRequest.getType())) {

        }

        return slaManageMapper.insert(slaManage) > 0 ? true : false;
    }

    private void checkData(SlaManageRequest slaManageRequest,Boolean update) {

        if(StringUtils.isBlank(slaManageRequest.getType())){
            throw new ParamValidationException("type不能为空！");
        }
        if(StringUtils.isBlank(slaManageRequest.getClassName())){
            throw new ParamValidationException("className不能为空！");
        }
        if(CollectionUtils.isEmpty(slaManageRequest.getSlaContent()) ){
            throw new ParamValidationException("sla不能为空！");
        }
        String businessKey = buildBusinessKey(slaManageRequest);

        SlaManageExample slaManageExample = new SlaManageExample();
        SlaManageExample.Criteria criteria = slaManageExample.createCriteria();
        criteria.andBusinessKeyEqualTo(businessKey);
        criteria.andDelEqualTo(0);
        if(update){
            criteria.andIdNotEqualTo(slaManageRequest.getId());
        }
        long count = slaManageMapper.countByExample(slaManageExample);
        if(count > 0){
            throw new ParamValidationException("不能重复");
        }
    }

    private String buildBusinessKey(SlaManageRequest slaManageRequest) {
        StringBuilder stringBuilder = new StringBuilder(slaManageRequest.getType()).append("_");

        if ("dubbo".equals(slaManageRequest.getType())) {
            stringBuilder.append(slaManageRequest.getDubboGroup()!=null ? slaManageRequest.getDubboGroup():"").append("/");
            stringBuilder.append(slaManageRequest.getClassName()).append(":");
            stringBuilder.append(slaManageRequest.getDubboVersion()!=null ? slaManageRequest.getDubboVersion():"").append("_");
            stringBuilder.append(slaManageRequest.getMethodName()!=null ? slaManageRequest.getMethodName():"").append(":");
        } else if ("http".equals(slaManageRequest.getType())) {
            stringBuilder.append(slaManageRequest.getClassName()).append("_");
            stringBuilder.append(slaManageRequest.getMethodName()!=null ? slaManageRequest.getMethodName():"").append(":");
        }
        return stringBuilder.toString();
    }

    @Override
    public Boolean update(SlaManageRequest slaManageRequest) {
        checkData(slaManageRequest,true);
        SlaManage slaManageDb = slaManageMapper.selectByPrimaryKey(slaManageRequest.getId());
        SlaManageHis slaManageHis = new SlaManageHis();
        BeanUtils.copyProperties(slaManageDb, slaManageHis);
        slaManageHis.setSlaManageId(slaManageDb.getId());
        int insert = slaManageHisMapper.insert(slaManageHis);
        if (insert > 0) {

            SlaManage slaManage = new SlaManage();
            slaManage.setId(slaManageRequest.getId());
            slaManage.setClassName(slaManageRequest.getClassName());
            slaManage.setMethodName(slaManageRequest.getMethodName());
            slaManage.setSlaContent(JSON.toJSONString(slaManageRequest.getSlaContent()));
            slaManage.setBusinessKey(buildBusinessKey(slaManageRequest));
            if ("dubbo".equals(slaManageRequest.getType())) {
                slaManage.setDubboGroup(slaManageRequest.getDubboGroup());
                slaManage.setDubboVersion(slaManageRequest.getDubboVersion());
            } else if ("http".equals(slaManageRequest.getType())) {

            }
            slaManage.setOperator(UserInfoThreadLocal.getUserInfo().getUserName());
            slaManage.setUpdateTime(new Date());
            return slaManageMapper.updateByPrimaryKeySelective(slaManage) > 0 ? true : false;
        } else {
            return false;
        }
    }

    @Override
    public Boolean delete(SlaManageRequest slaManageRequest) {
        SlaManage slaManageDb = slaManageMapper.selectByPrimaryKey(slaManageRequest.getId());
        SlaManageHis slaManageHis = new SlaManageHis();
        BeanUtils.copyProperties(slaManageDb, slaManageHis);
        slaManageHis.setSlaManageId(slaManageDb.getId());
        int insert = slaManageHisMapper.insert(slaManageHis);
        if (insert > 0) {
            SlaManage slaManage = new SlaManage();
            slaManage.setId(slaManageRequest.getId());
            slaManage.setOperator(UserInfoThreadLocal.getUserInfo().getUserName());
            slaManage.setDel(1);
            slaManage.setUpdateTime(new Date());
            return slaManageMapper.updateByPrimaryKeySelective(slaManage) > 0 ? true : false;
        } else {
            return false;
        }
    }

    @Override
    public PageResult<SlaManageDTO> queryHis(SlaManageRequest slaManageRequest) {
        SlaManageHisExample example = new SlaManageHisExample();
        example.setOrderByClause(SlaManageHis.Column.id.desc());
        PageRowBounds pageRowBounds = new PageRowBounds((slaManageRequest.getPageNum() - 1) * slaManageRequest.getPageSize(), slaManageRequest.getPageSize());

        Page<SlaManage> list = (Page) slaManageHisMapper.selectByExampleWithRowbounds(example, pageRowBounds);
        List<SlaManageDTO> collect = list.stream().map(slaManage -> {
            SlaManageDTO slaManageDTO = new SlaManageDTO();
            BeanUtils.copyProperties(slaManage, slaManageDTO);
            return slaManageDTO;
        }).collect(Collectors.toList());
        return new PageResult<>(collect, (int) list.getTotal(), slaManageRequest.getPageSize(), slaManageRequest.getPageNum());
    }
}
