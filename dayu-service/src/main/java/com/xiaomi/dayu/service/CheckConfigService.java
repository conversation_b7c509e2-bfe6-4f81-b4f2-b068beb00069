package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.bo.QueryConfigReq;
import com.xiaomi.dayu.api.constants.Enums;

/**
 * <AUTHOR> (yang<PERSON><PERSON><PERSON>@xiaomi.com)
 * @version 1.0
 * @since 2022/1/10
 */
public interface CheckConfigService {

    /**
     * for read type only, used only by controller apis
     */
    void checkConfigDefault(String tenant, String group, String dataId);

    /**
     * support all CRUD operations, used only by controller apis
     */
    void checkConfigByType(String tenant, String group, String dataId, int operateType);

    boolean checkPublishConfig(PublishConfigReq publishConfigReq, Enums.ChannelType channelType);
    void checkPublishConfig2(PublishConfigReq publishConfigReq, Enums.ChannelType channelType);

    void checkPermission(String userName, String appName);

    void checkQueryConfig(QueryConfigReq requestBody, Enums.ChannelType dubbo);
}
