package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.DubboMangerReq;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.api.service.DubboMangerService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.ErrorScope;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DubboService(interfaceClass = DubboMangerService.class, group = "${provider.group}")
public class DubboMangerServiceImpl implements DubboMangerService {
    @Autowired
    private  InstanceService instanceService;
    @Override
    public Result onlineOrOffline(DubboMangerReq requestBo) {
        List<String> list = instanceService.onlineOrOfflineOneByOne(requestBo.getAppName(), requestBo.getAppId(), requestBo.getEnabled(), requestBo.getIps(), requestBo.getOperator(), Enums.ChannelType.DUBBO);
        if(CollectionUtils.isNotEmpty(list)){
            return Result.fail(GeneralCodes.InternalError, "下线失败："+list);
        }
        return Result.success(null);
    }
}
