package com.xiaomi.dayu.service.impl;

import com.github.pagehelper.PageHelper;
import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.bo.QueryConfigReq;
import com.xiaomi.dayu.api.bo.QueryConfigRes;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.api.service.NacosConfigService;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.model.dto.ConfigInfoAndExtendDTO;
import com.xiaomi.dayu.model.requests.SearchConfigReq;
import com.xiaomi.dayu.service.ApplicationService;
import com.xiaomi.dayu.service.CheckConfigService;
import com.xiaomi.dayu.service.NacosConfigExtendService;
import com.xiaomi.dayu.service.SearchConfigService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yang (<EMAIL>)
 * @version 1.0
 * @since 2022/3/22
 */
@DubboService(interfaceClass = NacosConfigService.class, group = "${provider.group}",version = "1.0")
public class NacosConfigServiceImpl implements NacosConfigService {

    @Autowired
    private NacosConfigExtendService nacosConfigExtendService;

    @Autowired
    private CheckConfigService checkConfigService;
    @Autowired
    private SearchConfigService searchConfigService;
    @Autowired
    private ApplicationService applicationService;

    public Result<Object> publishConfig(PublishConfigReq requestBody) {
        try{
            this.checkConfigService.checkPublishConfig2(requestBody, Enums.ChannelType.DUBBO);
        }catch (IllegalArgumentException e){
            return Result.fail(GeneralCodes.InternalError,  e.getMessage());
        }
        ResultResponse<Object> result = this.nacosConfigExtendService.publishConfig(null, requestBody);

        if (result != null && result.isSuccess()) {
            return Result.success(result.getData());
        }
        if (result == null) {
            return Result.fail(GeneralCodes.InternalError, "code: 503, result is null");
        }
        return Result.fail(GeneralCodes.InternalError, "code: " + result.getCode() + ", " + result.getMessage());
    }

    @Override
    public Result<PageResult<QueryConfigRes>> queryConfig(QueryConfigReq requestBody) {
        String username = requestBody.getUsername();
        String appName = requestBody.getAppName();
        try{
            this.checkConfigService.checkQueryConfig(requestBody, Enums.ChannelType.DUBBO);
        }catch (IllegalArgumentException e){
            return Result.fail(GeneralCodes.InternalError, e.getMessage());
        }
        SearchConfigReq searchConfigReq = SearchConfigReq.builder()
                .pageSize(requestBody.getPageSize())
                .pageNo(requestBody.getPageNo())
                .appName(requestBody.getAppName())
                .dataId(requestBody.getDataId())
                .groupId(requestBody.getGroup())
                .tenantId(requestBody.getTenant())
                .appNameList(applicationService.getApplicationNames(username))
                .build();
        List<ConfigInfoAndExtendDTO> configInfoAndExtendDTOS = searchConfigService.searchConfigAndExtend(searchConfigReq);
        List<QueryConfigRes> data = null;
        PageHelper.clearPage();
        if(CollectionUtils.isNotEmpty(configInfoAndExtendDTOS)){
            data = configInfoAndExtendDTOS.stream().map(config -> {
                QueryConfigRes queryConfigRes = new QueryConfigRes();
                BeanUtils.copyProperties( config,queryConfigRes);
                return queryConfigRes;
            }).collect(Collectors.toList());
        }
        return Result.success(new PageResult(data, searchConfigService.searchConfigAndExtendTotal(searchConfigReq), requestBody.getPageNo().intValue(), requestBody.getPageSize()));
    }
}
