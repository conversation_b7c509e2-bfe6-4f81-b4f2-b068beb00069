/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service.impl;

import com.google.common.collect.Lists;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.InstanceUtils;
import com.xiaomi.dayu.common.util.SyncUtils;
import com.xiaomi.dayu.common.util.Tool;
import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.dto.ConsumerServiceDTO;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.dayu.service.ConsumerService;
import com.xiaomi.dayu.service.UserService;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.metadata.report.identifier.MetadataIdentifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
public class ConsumerServiceImpl extends AbstractService implements ConsumerService {
    @Autowired
    private AccountServiceRpc accountServiceRpc;


    @Autowired
    private UserService userService;

    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;

    @Value("${monitor.url.pattern}")
    private String monitorUrlPattern;

    @Override
    public List<Consumer> findByService(String service) {
        return SyncUtils.url2ConsumerList(findConsumerUrlByService(service));
    }

    @Override
    public Set<String> findConsumers() {
        Set<String> ret = new HashSet<>();
        ConcurrentMap<String, Map<String, URL>> consumerUrls = getRegistryCache().get(Constants.CONSUMERS_CATEGORY);
        if (consumerUrls != null){
            ret.addAll(consumerUrls.keySet());
        }
        return ret;
    }


    @Override
    public List<Consumer> findAll() {
        return SyncUtils.url2ConsumerList(findAllConsumerUrl());
    }

    @Override
    public String getConsumerMetadata(MetadataIdentifier consumerIdentifier) {
        return metaDataCollector.getConsumerMetaData(consumerIdentifier);
    }

    private Map<String, URL> findAllConsumerUrl() {
        Map<String, String> filter = new HashMap<String, String>();
        filter.put(Constants.CATEGORY_KEY, Constants.CONSUMERS_CATEGORY);
        return SyncUtils.filterFromCategory(getRegistryCache(), filter);
    }




    @Override
    public List<Consumer> findByAddress(String consumerAddress) {
        return SyncUtils.url2ConsumerList(findConsumerUrlByAddress(consumerAddress));
    }


    private Map<String, URL> findConsumerUrlByAddress(String address) {
        Map<String, String> filter = new HashMap<String, String>();
        filter.put(Constants.CATEGORY_KEY, Constants.CONSUMERS_CATEGORY);
        filter.put(SyncUtils.ADDRESS_FILTER_KEY, address);

        return SyncUtils.filterFromCategory(getRegistryCache(), filter);
    }

    public Map<String, URL> findConsumerUrlByService(String service) {
        Map<String, String> filter = new HashMap<String, String>();
        filter.put(Constants.CATEGORY_KEY, Constants.CONSUMERS_CATEGORY);
        filter.put(SyncUtils.SERVICE_FILTER_KEY, service);

        return SyncUtils.filterFromCategory(getRegistryCache(), filter);
    }

    @Override
    public String findVersionInApplication(String application) {
        Map<String, String> filter = new HashMap<>();
        filter.put(Constants.CATEGORY_KEY, Constants.CONSUMERS_CATEGORY);
        filter.put(Constants.APPLICATION_KEY, application);
        Map<String, URL> stringURLMap = SyncUtils.filterFromCategory(getRegistryCache(), filter);
        if (stringURLMap == null || stringURLMap.isEmpty()) {
            throw new ParamValidationException("there is no consumer for application: " + application);
        }
        String defaultVersion = "2.6";
        URL url = stringURLMap.values().iterator().next();
        String version = url.getParameter(Constants.SPECIFICATION_VERSION_KEY);
        return StringUtils.isBlank(version) ? defaultVersion : version;
    }

    @Override
    public String getConsumerMetaData(MetadataIdentifier consumerIdentifier) {
        return metaDataCollector.getProviderMetaData(consumerIdentifier);
    }
    @Override
    public Set<ConsumerServiceDTO> getServiceDTOS(String pattern, String filter, boolean self,boolean aggregate) {
        List<Consumer> consumers = new ArrayList<>();
        Set<ConsumerServiceDTO> result;
        if (!filter.contains(Constants.ANY_VALUE) && !filter.contains(Constants.INTERROGATION_POINT)) {
            // filter with specific string
            if (Constants.IP.equals(pattern)) {
                consumers = findByAddress(filter);
            } else if (Constants.SERVICE.equals(pattern)) {
                consumers = findByService(filter);
            } else if (Constants.APPLICATION.equals(pattern)) {
                consumers = findByApplication(filter);
            }
            if(self && !UserInfoThreadLocal.getUserInfo().isAdmin()){
                List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
                consumers = consumers.stream().filter(consumer -> applicationNames.contains(consumer.getApplication())).collect(Collectors.toList());
            }
            if(CollectionUtils.isNotEmpty(consumers)) {
                if (aggregate) {
                    if (Constants.SERVICE.equals(pattern)) {
                        Consumer consumer = consumers.get(0);
                        consumer.setConsumerCount(consumers.size());
                        consumers = Lists.newArrayList(consumer);
                    } else {
                        Map<String, List<Consumer>> collect = consumers.stream().collect(Collectors.groupingBy(Consumer::getService));
                        List<Consumer> consumersNew = new ArrayList<>(collect.size());
                        collect.forEach((key, values) -> {
                            consumersNew.add(values.get(0));
                            values.get(0).setConsumerCount(values.size());
                        });
                        consumers = consumersNew;
                    }
                }
            }
        } else {
            // filter with fuzzy search
            Set<String> candidates = Collections.emptySet();
            if (Constants.SERVICE.equals(pattern)) {
                candidates = findServices();
            } else if (Constants.APPLICATION.equals(pattern)) {
                candidates = findApplications();
            }
            else if (Constants.IP.equals(pattern)) {
                candidates = findAddresses().stream().collect(Collectors.toSet());
            }
            // replace dot symbol and asterisk symbol to java-based regex pattern
            filter = filter.toLowerCase().replace(Constants.PUNCTUATION_POINT, Constants.PUNCTUATION_SEPARATOR_POINT);
            // filter start with [* 、? 、+] will triggering PatternSyntaxException
            if (filter.startsWith(Constants.ANY_VALUE)
                    || filter.startsWith(Constants.INTERROGATION_POINT) || filter.startsWith(Constants.PLUS_SIGNS)) {
                filter = Constants.PUNCTUATION_POINT + filter;
            }
            // search with no case insensitive
            Pattern regex = Pattern.compile(filter, Pattern.CASE_INSENSITIVE);
            for (String candidate : candidates) {
                Matcher matcher = regex.matcher(candidate);
                if (matcher.matches() || matcher.lookingAt()) {
                    List<Consumer> consumerList;
                    if (Constants.SERVICE.equals(pattern)) {
                        consumerList = findByService(candidate);
                    }
                    else if (Constants.IP.equals(pattern)) {
                        consumerList = findByAddress(candidate);
                    }
                    else {
                        consumerList = findByApplication(candidate);
                    }
                    if(self && !UserInfoThreadLocal.getUserInfo().isAdmin()){
                        List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
                        consumerList = consumerList.stream().filter(consumer -> applicationNames.contains(consumer.getApplication())).collect(Collectors.toList());
                    }
                    consumers.addAll(consumerList);
//                    if(CollectionUtils.isNotEmpty(consumerList)){
//                        if(aggregate){
//                            Consumer consumer = consumerList.get(0);
//                            consumer.setConsumerCount(consumerList.size());
//                            consumers.add(consumer);
//                        }else{
//                            consumers.addAll(consumerList);
//                        }
//                    }
                }
            }
            if (aggregate) {
                Map<String, List<Consumer>> collect = consumers.stream().collect(Collectors.groupingBy(Consumer::getService));
                List<Consumer> consumersNew = new ArrayList<>(collect.size());
                collect.forEach((key, values) -> {
                    consumersNew.add(values.get(0));
                    values.get(0).setConsumerCount(values.size());
                });
                consumers = consumersNew;
            }
        }
        result = convertConsumers2DTO(consumers,aggregate);

        return result;
    }



    @Override
    public Set<String> findServices() {
        Set<String> ret = new HashSet<>();
        ConcurrentMap<String, Map<String, URL>> consumerUrls = getRegistryCache().get(Constants.CONSUMERS_CATEGORY);
        if (consumerUrls != null){
            ret.addAll(consumerUrls.keySet());
        }
        return ret;
    }
    @Override
    public List<String> findAddresses() {
        List<String> ret = new ArrayList<String>();

        ConcurrentMap<String, Map<String, URL>> consumerUrls = getRegistryCache().get(Constants.CONSUMERS_CATEGORY);
        if (null == consumerUrls) {
            return ret;
        }

        for (Map.Entry<String, Map<String, URL>> e1 : consumerUrls.entrySet()) {
            Map<String, URL> value = e1.getValue();
            for (Map.Entry<String, URL> e2 : value.entrySet()) {
                URL u = e2.getValue();
                String app = u.getAddress();
                if (app != null) {
                    ret.add(app);
                }
            }
        }

        return ret;
    }
    @Override
    public Set<String> findApplications() {
        Set<String> ret = new HashSet<>();
        ConcurrentMap<String, Map<String, URL>> consumerUrls = getRegistryCache().get(Constants.CONSUMERS_CATEGORY);
        if (consumerUrls == null){
            return ret;
        }

        for (Map.Entry<String, Map<String, URL>> e1 : consumerUrls.entrySet()) {
            Map<String, URL> value = e1.getValue();
            for (Map.Entry<String, URL> e2 : value.entrySet()) {
                URL u = e2.getValue();
                String app = u.getParameter(Constants.APPLICATION);
                if (app != null) {
                    ret.add(app);
                }
            }
        }

        return ret;
    }

    private Set<ConsumerServiceDTO> convertConsumers2DTO(List<Consumer> consumers,boolean agg) {
        Set<ConsumerServiceDTO> result = new TreeSet<>();
        List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
        for (Consumer consumer : consumers) {
            String service = consumer.getService();
            String group = Tool.getGroup(service);
            String version = Tool.getVersion(service);
            String interfaze = Tool.getInterface(service);
            ConsumerServiceDTO cons = new ConsumerServiceDTO();
            cons.setServiceId(consumer.getService());
            cons.setService(interfaze);
            cons.setGroup(group);
            cons.setVersion(version);
            cons.setConsumerCount(consumer.getConsumerCount());
            if(!agg){
                cons.setApplication(consumer.getApplication());
                cons.setAddress(consumer.getAddress());
                cons.setDubboVersion(InstanceUtils.getDubboVersion(consumer));
                cons.setOwn(applicationNames.contains(consumer.getApplication()));
            }

            result.add(cons);
        }
        return result;
    }
    @Override
    public void fillExtendInfo(List<ConsumerServiceDTO> content) {
        if(CollectionUtils.isNotEmpty(content)){
            for (ConsumerServiceDTO consumerServiceDTO : content) {
                if(StringUtils.isNotEmpty(consumerServiceDTO.getApplication())){
                    try{
                        List<UserInfoResult> userList = accountServiceRpc.queryUsersByAppName(consumerServiceDTO.getApplication());
                        if(CollectionUtils.isNotEmpty(userList)){
                            consumerServiceDTO.setDevelopers(userList.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
                            String uid = userService.findUidByUserName(userList.get(0).getUserName());
                            consumerServiceDTO.setDepartment(userService.findDepartmentFullName(uid));
                        }
                    }catch (Exception e){
                        logger.error("查询开发人员或部门信息时报错",e);
                    }
                    ProjectBo projectBo = iProjectServiceRpc.getProjectByName(consumerServiceDTO.getApplication());
                    if(projectBo != null && projectBo.getId() != null){
                        consumerServiceDTO.setMonitorUrl(monitorUrlPattern.replace("projectId",projectBo.getId()+"_"+projectBo.getName().replace("-","_")));
                    }
                }
            }
        }

    }

    private List<Consumer> findByApplication(String application) {
        return SyncUtils.url2ConsumerList(findConsumerUrlByApplication(application));
    }

    private Map<String, URL> findConsumerUrlByApplication(String application) {
        Map<String, String> filter = new HashMap<>();
        filter.put(Constants.CATEGORY_KEY, Constants.CONSUMERS_CATEGORY);
        filter.put(Constants.APPLICATION, application);
        return SyncUtils.filterFromCategory(getRegistryCache(), filter);
    }
}
