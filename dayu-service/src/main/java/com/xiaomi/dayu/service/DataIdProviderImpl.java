package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.DataIdReq;
import com.xiaomi.dayu.api.bo.DataIdResp;
import com.xiaomi.dayu.api.service.DataIdProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Type DataIdProviderImpl.java
 * @Desc
 * @date 2024/11/25 17:08
 */
@DubboService(interfaceClass = DataIdProvider.class, group = "${provider.group}")
public class DataIdProviderImpl implements DataIdProvider {

	@Autowired
	private ApprovalService approvalService;

	@Override
	public Result<List<DataIdResp>> queryDataIdRecord(DataIdReq dataIdReq) {
		if(StringUtils.isBlank(dataIdReq.getDataId())){
			return Result.fail(GeneralCodes.ParamError,"dataId不能为空");
		}
		String auth = RpcContext.getContext().getAttachment("auth");
		if(StringUtils.isBlank(auth) ||!auth.equals("5fG9pJ2")){
			return Result.fail(GeneralCodes.ParamError,"auth不能为空");
		}

		List<DataIdResp> list = approvalService.query(dataIdReq);
		return Result.success(list);
    }
}
