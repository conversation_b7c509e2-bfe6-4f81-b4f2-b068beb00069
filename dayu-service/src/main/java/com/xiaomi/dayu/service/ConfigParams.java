package com.xiaomi.dayu.service;

import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.mone.miline.api.bo.common.EnvEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
@Slf4j
@Configuration
public class ConfigParams {

    @Value("${sentinel.url}")
    private String sentinalUrl;
    @Value("${registry.address}")
    private String registryAddress;
    @Value("${iauth.url}")
    private String iauthUrl;
    @Value("${arthas.url}")
    private String arthasUrl;
    @Value("${env.name}")
    private String envName;

    public static String PROFILE ="";
    public static String ENV ="";//staging online

    public static String ENV_NAME="";

    @PostConstruct
    public void initParam(){
        HttpClient.setSentinelUrl(sentinalUrl);
        HttpClient.setNacosUrl(registryAddress);
        HttpClient.setIauthUrl(iauthUrl);
        HttpClient.setArthasUrl(arthasUrl);
        log.info("sentinalUrl={}",sentinalUrl);
        log.info("registryAddress={}",registryAddress);
        log.info("iauthUrl={}",iauthUrl);
        log.info("arthasUrl={}",arthasUrl);

        String profile = System.getProperty("spring.profiles.active");
        ENV_NAME = envName;
        PROFILE = profile;
        if(envName.startsWith("pro") ||envName.endsWith("pro") ){
            ENV = "online";
        }else{
            ENV = "staging";
        }
        log.info("ENV={}",ENV);
        log.info("ENV_NAME={}",ENV_NAME);
        log.info("PROFILE={}",PROFILE);

    }
    public static EnvEnum getMilineEnv(){
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        String currEnv = userInfo==null ? null : userInfo.getCurrEnv() ;
        switch (ENV_NAME){
            case "dev":
               return EnvEnum.DEV;
            case "staging":
               return EnvEnum.STAGING;
            case "test":
               return EnvEnum.STAGING;
            case "pro":
               return EnvEnum.ONLINE;
            case "pro-sgp":
               return StringUtils.isBlank(currEnv)? null : "global".equals(currEnv) ? EnvEnum.GLOBAL_ONLINE:EnvEnum.SGP_ONLINE;
            case "staging-sgp":
               return StringUtils.isBlank(currEnv)? null : "global".equals(currEnv) ? EnvEnum.GLOBAL_STAGING:EnvEnum.SGP_STAGING;
            case "pro-eur":
               return EnvEnum.EUR_ONLINE;
        }
        return null;
    }
}
