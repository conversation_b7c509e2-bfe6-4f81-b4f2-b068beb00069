package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.data.push.common.SafeRun;
import com.xiaomi.dayu.common.util.ConvertUtil;
import com.xiaomi.dayu.dao.SwimLaneMapper;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.domain.Tag;
import com.xiaomi.dayu.model.dto.TagRouteDTO;
import com.xiaomi.dayu.mybatis.entity.SwimLane;
import com.xiaomi.dayu.mybatis.entity.SwimLaneExample;
import com.xiaomi.dayu.rpc.OpenApiServiceRpc;
import com.xiaomi.mone.miline.api.bo.common.EnvEnum;
import com.xiaomi.youpin.gwdash.service.OpenApiService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import swimlane.bo.AppEnvDTO;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RouteTagSync {

    @Resource
    private SwimLaneMapper swimLaneMapper;

    @Autowired
    private RouteService routeService;

//    @DubboReference(check = false, interfaceClass = OpenApiService.class, group = "${ref.gwdash.service.group}", timeout = 10000)
//    private OpenApiService gwdashOpenApiService;
    @Autowired
    private OpenApiServiceRpc openApiServiceRpc;


    @Autowired
    private ProviderService providerService;

    public static final Gson gson = new Gson();

    private final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();


    /**
     * 每min刷新标签信息
     */

    @PostConstruct
    public void init() {
        String profile = System.getProperty("spring.profiles.active");
        if ("pro".equals(profile) || "staging".equals(profile)) {
            executorService.scheduleAtFixedRate(this::refreshAppTagInfo, 2, 1, TimeUnit.MINUTES);
        }
    }

    private void refreshAppTagInfo() {
        SafeRun.run(() -> {
            log.warn("--------------开始自动刷新泳道数据----------------");
            //需要打标的所有项目及环境及标签
            //<test0930,<staging,[tag1,tag2]>>
            //<appName,<envName,[tag1,tag2]>>
            Map<String, Map<String, List<String>>> appEnvBeTag = new HashMap<>();
            SwimLaneExample swimLaneExample = new SwimLaneExample();
            SwimLaneExample.Criteria criteria = swimLaneExample.createCriteria();
            //criteria.andNameIn(Lists.newArrayList("泳道组3"));
            List<SwimLane> swimLanes = swimLaneMapper.selectByExampleWithBLOBs(swimLaneExample);
            swimLanes.forEach(swimLane -> {
                if (swimLane.getStatus()) {
                    List<AppEnvDTO> appEnvs = gson.fromJson(swimLane.getAppEnvJson(), new TypeToken<List<AppEnvDTO>>() {
                    }.getType());
                    appEnvs.forEach(appEnvDTO -> {
                        if (appEnvBeTag.containsKey(appEnvDTO.getAppName())) {
                            Map<String, List<String>> envAndTags = appEnvBeTag.get(appEnvDTO.getAppName());
                            if (envAndTags.containsKey(appEnvDTO.getEnvName())) {
                                envAndTags.get(appEnvDTO.getEnvName()).add(swimLane.getFlowControlTag());
                            } else {
                                List<String> envTags = new ArrayList<>();
                                envTags.add(swimLane.getFlowControlTag());
                                envAndTags.put(appEnvDTO.getEnvName(), envTags);
                            }
                        } else {
                            Map<String, List<String>> envTagsMap = new HashMap<>();
                            List<String> tagList = new ArrayList<>();
                            tagList.add(swimLane.getFlowControlTag());
                            envTagsMap.put(appEnvDTO.getEnvName(), tagList);
                            appEnvBeTag.put(appEnvDTO.getAppName(), envTagsMap);
                        }
                    });
                }
            });
            for (String appName : appEnvBeTag.keySet()) {
                TagRouteDTO tagRoute = routeService.findTagRoute(appName);
                if (Objects.isNull(tagRoute) || !tagRoute.isEnabled()) {
                    continue;
                }

                //mione中获取的环境实例列表
                log.info("envMachinesByAppName 请求,appName={}", appName);
                Map<String, List<String>> envIpListMap;
                if(ConfigParams.ENV_NAME.equals("staging-sgp")){
                    envIpListMap = openApiServiceRpc.envMachinesByAppName(appName, EnvEnum.SGP_STAGING.getName());
                  //  envIpListMap = gwdashOpenApiService.envMachinesByAppName(appName, EnvEnum.SGP_STAGING.getName()).getData();
                    envIpListMap.putAll(openApiServiceRpc.envMachinesByAppName(appName, EnvEnum.GLOBAL_STAGING.getName()));
                } else if(ConfigParams.ENV_NAME.equals("pro-sgp")){
                    envIpListMap = openApiServiceRpc.envMachinesByAppName(appName, EnvEnum.SGP_ONLINE.getName());
                    envIpListMap.putAll(openApiServiceRpc.envMachinesByAppName(appName, EnvEnum.GLOBAL_ONLINE.getName()));
                } else {
                    envIpListMap = openApiServiceRpc.envMachinesByAppName(appName, ConfigParams.getMilineEnv().getName());
                }
                log.info("envMachinesByAppName 响应,mapResult={}", JSON.toJSONString(envIpListMap));
                envIpListMap = ConvertUtil.clearEmptyEnv(envIpListMap);
                //nacos上的providers列表
                //  providerService.findByApplication(appName).forEach(provider -> providerMap.putIfAbsent(provider.getAddress().substring(0, provider.getAddress().indexOf(":")), provider));

                //<ip,HashMap<port,provider>>
                //  Map<String, Provider> providerMap = new HashMap<>();
                Map<String, HashMap<String, Provider>> providerMap = new HashMap<>();
                //nacos上的providers列表
                providerService.findByApplication(appName).forEach(provider -> {
                    providerMap.putIfAbsent(provider.getIp(), new HashMap<>());
                    providerMap.get(provider.getIp()).putIfAbsent(provider.getPort(), provider);

                });
                //CreateType == 1
                Map<String, Tag> tagRouterMap = tagRoute.getTags().stream().filter(tag -> tag.getCreateType() != null && tag.getCreateType() == 1).collect(Collectors.toMap(Tag::getName, Function.identity()));
                //CreateType != 1
                Map<String, Tag> tagRouterMap2 = tagRoute.getTags().stream().filter(tag -> tag.getCreateType() == null || tag.getCreateType() != 1).collect(Collectors.toMap(Tag::getName, Function.identity()));


                List<String> allTagNameList = new ArrayList<>();
                for (Map.Entry<String, List<String>> envEntry : envIpListMap.entrySet()) {
                    if (!appEnvBeTag.get(appName).containsKey(envEntry.getKey())) {
                        continue;
                    }

                    //流水线和provider中存在的ip:port
                    List<String> envIpList = envEntry.getValue();
                    List<String> existEnvIpPortList = new ArrayList<>();
                    envIpList.forEach(envIp -> {
                        if (providerMap.containsKey(envIp)) {
                            existEnvIpPortList.addAll(providerMap.get(envIp).values().stream().map(Provider::getAddress).collect(Collectors.toList()));
                        }
                    });

                    List<String> tagList = appEnvBeTag.get(appName).get(envEntry.getKey());
                    for (String tagName : tagList) {
                        if (!tagRouterMap.containsKey(tagName)) {
                            tagRouterMap.put(tagName, new Tag(tagName, 1, "system", new SimpleDateFormat("yyyy-MM-dd ").format(new Date()), new ArrayList<>()));
                        }
                        Tag tag = tagRouterMap.get(tagName);
                        List<String> addresses = tag.getAddresses();
                        if (addresses == null) {
                            addresses = new ArrayList<>();
                            tag.setAddresses(addresses);
                        }
                        addresses.clear();
                        addresses.addAll(existEnvIpPortList);
                        allTagNameList.add(tagName);
                    }
                }
                Iterator<String> tagNameIterator = tagRouterMap.keySet().iterator();
                while (tagNameIterator.hasNext()) {
                    String tagName = tagNameIterator.next();
                    if (!allTagNameList.contains(tagName)) {
                        tagNameIterator.remove();
                    }
                }

                TagRouteDTO tagRouteNew = new TagRouteDTO();
                tagRouteNew.setTags(new ArrayList<>(tagRouterMap.values()));

                String newMd5 = tagRouteNew.getNewMd5();
                if (tagRoute.getMd5() == null || !newMd5.equals(tagRoute.getMd5())) {
                    tagRoute.setMd5(newMd5);
                    if (MapUtils.isNotEmpty(tagRouterMap2)) {
                        List<Tag> tagsNew = new ArrayList<>(tagRouterMap2.values());
                        tagsNew.addAll(tagRouterMap.values());
                        tagRoute.setTags(tagsNew);
                    }
                    log.warn("更新泳道数据,tagRoute={}", JSON.toJSONString(tagRoute));
                    routeService.updateTagRoute(tagRoute);
                }
            }
            log.warn("--------------结束自动刷新泳道数据----------------");
        });
    }

    public List<String> getAddresses(List<Tag> tags) {
        return tags.stream()
                .filter(tag -> org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(tag.getAddresses()))
                .flatMap(tag -> tag.getAddresses().stream())
                .collect(Collectors.toList());
    }
}
