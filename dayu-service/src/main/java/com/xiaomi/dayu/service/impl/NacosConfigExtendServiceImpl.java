package com.xiaomi.dayu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.common.Constants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.interfaces.Success;
import com.xiaomi.dayu.dao.ConfigInfoExtendMapper;
import com.xiaomi.dayu.dao.ConfigInfoMapper;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend;
import com.xiaomi.dayu.mybatis.example.ConfigInfoExample;
import com.xiaomi.dayu.service.ApprovalService;
import com.xiaomi.dayu.service.NacosConfigExtendService;
import com.xiaomi.dayu.wrapper.DubboAdminHttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;

import static com.xiaomi.dayu.common.util.RetryUtil.callWithRetries;

@Slf4j
@Service
public class NacosConfigExtendServiceImpl implements NacosConfigExtendService {

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private ConfigInfoExtendMapper configInfoExtendMapper;
    @Autowired
    private ConfigInfoMapper configInfoMapper;

    @Override
    public ConfigInfoExtend queryByConfigInfoId(Long id) {
        return configInfoExtendMapper.queryByConfigInfoId(id);
    }

    @Override
    public List<ConfigInfoExtend> queryByConfigInfoIds(List<Long> ids) {
        return configInfoExtendMapper.queryByConfigInfoIds(ids);
    }

    @Override
    public int insert(ConfigInfoExtend configInfoExtend) {
        return configInfoExtendMapper.insertOrUpdate(configInfoExtend);
    }

    @Override
    public ResultResponse<Object> publishConfig(HttpServletRequest request, PublishConfigReq requestBody) {
        if (StringUtils.isBlank(requestBody.getUsername())) {
            requestBody.setUsername(UserInfoThreadLocal.getUserInfo().getUserName());
        }
        if (request == null) {
            return this.publishConfig(requestBody);
        }

        //return this.publishConfig1(request, requestBody);


        //走审批流程
        approvalService.createApproval(requestBody,requestBody.getId() == null ? Enums.OperateType.CREATE :Enums.OperateType.UPDATE);

        return ResultResponse.success(true);
    }


    private ResultResponse<Object> publishConfig1(HttpServletRequest request, PublishConfigReq requestBody) {
        if (StringUtils.isNotBlank(requestBody.getUsername())) {
            DubboAdminHttpServletRequestWrapper requestWrapper =(DubboAdminHttpServletRequestWrapper)request;
            requestWrapper.getParams().put("src_user", new String[]{requestBody.getUsername()});
        }
        String logPrefix = this.getClass().getSimpleName() + String.format("publishConfig1 operateType %d, id " +
                        requestBody.getId() + " username %s, tenant %s, group %s, dataId %s: ",
                requestBody.getOperateType(), requestBody.getUsername(), requestBody.getTenant(), requestBody.getGroup(),
                requestBody.getDataId());
        ResultResponse<Object> response = HttpClient.nacosProxyHttp(request);

        if (!response.isSuccess()) {
            log.error(logPrefix + "fail to publish config");
            return response;
        }
        // we don't support updating config type anymore to simplify the history rollback process,
        // so there's no need to update the config extend table;
        if (requestBody.getId() != null || Enums.OperateType.UPDATE.getValue() == requestBody.getOperateType()) {
            log.info(logPrefix + "successfully published config changes");
            return response;
        }
        CompletableFuture.runAsync(() -> updateExtend1(request, requestBody, logPrefix));
        return response;
    }
    @Override
    public ResultResponse<Object> publishConfig(PublishConfigReq requestBody) {
        String logPrefix = this.getClass().getSimpleName() + String.format(" publishConfig operateType %d, id " +
                        requestBody.getId() + " username %s, tenant %s, group %s, dataId %s: ",
                requestBody.getOperateType(), requestBody.getUsername(), requestBody.getTenant(), requestBody.getGroup(),
                requestBody.getDataId());
        ResultResponse<Object> response = null;

        try {
            List<String> headers = new ArrayList<>();
            headers.add("Content-Type");
            headers.add("application/x-www-form-urlencoded");
            response = HttpClient.nacosRequest(requestBody, Constants.CONFIG_CONTROLLER_PATH, HttpMethod.POST.name(), null, headers);
            log.warn("NacosConfigExtendServiceImpl.publishConfig, requestBody={} response={}",JSON.toJSONString(requestBody), JSON.toJSONString(response));

            if (!response.isSuccess()) {
                log.error(logPrefix + "fail to publish config");
                return response;
            }
            // we don't support updating config type anymore to simplify the history rollback process,
            // so there's no need to update the config extend table;
/*            if (requestBody.getId() != null || Enums.OperateType.UPDATE.getValue() == requestBody.getOperateType()) {
                log.info(logPrefix + "successfully published config changes");
                return response;
            }*/
//            CompletableFuture.runAsync(() -> updateExtend(requestBody, logPrefix));
            if(StringUtils.isNotBlank(requestBody.getEnvId()) &&  StringUtils.isNotBlank(requestBody.getEnvName()) && requestBody.getConfigType() != null ){
                updateExtend(requestBody,logPrefix);
            }
        } catch (Exception e) {
            log.error(logPrefix + " failed to public config", e);
        }
        return response;
    }

    private void updateExtend(PublishConfigReq requestBody, String logPrefix) {
        ConfigInfoExample example = new ConfigInfoExample();
        ConfigInfoExample.Criteria criteria = example.createCriteria();
        criteria.andDataIdEqualTo(requestBody.getDataId());
        criteria.andGroupIdEqualTo(requestBody.getGroup());
        if(StringUtils.isNotBlank(requestBody.getTenant())){
            criteria.andTenantIdEqualTo(requestBody.getTenant());
        }
        List<ConfigInfo> configInfos = configInfoMapper.searchConfig(example);
        if(CollectionUtils.isNotEmpty(configInfos)){
            ConfigInfo configInfo = configInfos.get(0);
            this.insertExtend(configInfo.getId(), requestBody, logPrefix);
        }

    }

    private void updateExtend1(HttpServletRequest request, PublishConfigReq requestBody, String logPrefix) {
        HashMap<String, String> params = new HashMap<>();
        params.put("show", "all");

        Callable<Success> getConfigCall = () -> {
            ResultResponse<Object> resp = HttpClient.nacosProxyHttp(request, RequestMethod.GET.name(), params);

            if (resp == null) {
                resp = new ResultResponse<>();
                resp.setSuccess(false);
            }
            if (!resp.isSuccess() || resp.getData() == null) {
                resp.setSuccess(false);
                log.error(logPrefix + "query config response data is empty");
                return resp;
            }
            return resp;
        };
        ResultResponse<Object> configResp = (ResultResponse<Object>) callWithRetries(getConfigCall, 2, log,
                logPrefix + "query config response data");

        if (!configResp.isSuccess() || configResp.getData() == null) {
            log.error(logPrefix + "query config response data is empty");
            return;
        }
      //  this.insertExtend((JSONObject) configResp.getData(), requestBody, logPrefix);
    }

    private void insertExtend (Long configInfoId, PublishConfigReq requestBody, String logPrefix) {
        Callable<Success> insertExtendCall = () -> {
            ResultResponse<Object> resp = new ResultResponse<>();
            resp.setSuccess(false);
            int res = this.insert(new ConfigInfoExtend(requestBody.getDataId(), configInfoId,
                    requestBody.getEnvId(), requestBody.getEnvName(), requestBody.getConfigType()));

            if (res == 0) {
                log.error(logPrefix + "fail to insert into ConfigInfoExtend");
                return resp;
            }
            resp.setSuccess(true);
            return resp;
        };
        Success insertResp = callWithRetries(insertExtendCall, 2, log,
                logPrefix + "insert into ConfigInfoExtend");

        if (!insertResp.isSuccess()) {
            // todo alert
            log.error(logPrefix + "fail to insert into ConfigInfoExtend after retry. data is inconsistent!");
        }
    }

    @Override
    public void extendDataById(JSONObject data, Long id) {
        ConfigInfoExtend configInfoExtend = this.queryByConfigInfoId(id);

        if (configInfoExtend == null) {
            log.warn("can not find config extend data info with id {}", id);
            return;
        }
        data.put("createTime", configInfoExtend.getCreateTime());
        data.put("envId", configInfoExtend.getEnvId());
        data.put("envName", configInfoExtend.getEnvName());
        data.put("configType", configInfoExtend.getConfigType());
    }

    @Override
    public boolean deleteConfig(String relateInfo) {
        HashMap<String, String> hashMap = JSON.parseObject(relateInfo, HashMap.class);
        ResultResponse<Object> response=null;
        try {
            response = HttpClient.nacosRequest(null, Constants.CONFIG_CONTROLLER_PATH, RequestMethod.DELETE.name(), hashMap);
            log.warn("NacosConfigExtendServiceImpl.deleteConfig, relateInfo={} response={}",relateInfo, JSON.toJSONString(response));
            String data = (String)response.getData();
            if(Boolean.valueOf(data)){
                return true;
            }
            log.error("NacosConfigExtendServiceImpl.deleteConfig，删除数据失败 relateInfo={} response={}",relateInfo, JSON.toJSONString(response));
        } catch (JsonProcessingException e) {
            log.error("NacosConfigExtendServiceImpl.deleteConfig，数据解析异常 relateInfo={} response={}",relateInfo, JSON.toJSONString(response),e);
            return false;
        }
        return false;
    }

//    @Override
//    public ResultResponse<Object> queryConfig(QueryConfigReq requestBody) {
//        return null;
//    }
}
