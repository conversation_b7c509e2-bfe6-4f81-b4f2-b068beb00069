package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageRowBounds;
import com.xiaomi.data.push.client.HttpClientV2;
import com.xiaomi.dayu.api.service.NacosNamingService;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.mybatis.example.NamingInstanceExample;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@DubboService(interfaceClass = NacosNamingService.class, group = "${provider.group}")
public class NacosNamingServiceImpl implements NacosNamingService{


    @Value("${registry.address}")
    private String nacosAddress;
    @Autowired
    private NacosLoginService nacosLoginService;
    @Autowired
    private NacosNamingService nacosNamingService;
    @Autowired
    private NamingInstanceService namingInstanceService;

    public JSONArray queryInstance(String serviceName,String namespaceId,String groupName,String clusterName){
        StringBuilder stringBuilder = new StringBuilder("http://").append(nacosAddress);
        stringBuilder.append("/nacos/v1/ns/catalog/instances?pageSize=1000&pageNo=1&accessToken=").append(nacosLoginService.getNacosAccessToken());
        stringBuilder.append("&serviceName=").append(serviceName);
        stringBuilder.append("&clusterName=").append(clusterName);
        stringBuilder.append("&groupName=").append(groupName);
        stringBuilder.append("&namespaceId=").append(namespaceId);

        String instanceResult= HttpClientV2.get(stringBuilder.toString(),null);
        try{
            JSONObject jsonObject = JSON.parseObject(instanceResult);
            return  (JSONArray)jsonObject.get("list");
        }catch (Exception e){
            log.error("解析instanceResult异常，serviceName={}，instanceResult={}",serviceName,instanceResult,e);
        }
        return  null;
    }

    public JSONArray queryInstance(String serviceName){
        String groupName ="DEFAULT_GROUP";
        String namespaceId ="";
        String clusterName ="DEFAULT";
        return queryInstance(serviceName,namespaceId,groupName,clusterName);
    }
    public Result cleanNamingInstance(int beforeMinutes){

        log.warn("开始执行清理无效naminginstance任务 "+new Date());
        NamingInstanceExample example = new NamingInstanceExample();
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andDelEqualTo(false);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.minusMinutes(beforeMinutes);
        criteria.andUpdateTimeLessThan(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        List<NamingInstance> namingInstances;
        int pageNum = 1;
        int pageSize = 100;
        int cleanTotol=0;
        List<String> cleanInstanceIdList = new ArrayList<>();
        do{
            PageRowBounds pageRowBounds = new PageRowBounds((pageNum-1)*pageSize,pageSize);
            namingInstances = namingInstanceService.selectByExampleWithRowbounds(example, pageRowBounds);
            if(CollectionUtils.isNotEmpty(namingInstances)){
                for (NamingInstance namingInstanceDb : namingInstances) {
                    JSONArray objects = this.queryInstance(namingInstanceDb.getServiceName(),namingInstanceDb.getNamespaceId(),namingInstanceDb.getGroupName(),namingInstanceDb.getClusterName());
                    NamingInstance namingInstance = new NamingInstance();
                    namingInstance.setId(namingInstance.getId());
                    namingInstance.setDel(true);
                    namingInstance.setUpdateTime(new Date());
                    if(CollectionUtils.isEmpty(objects)){
//                        cleanTotol+= namingInstanceService.deleteByPrimaryKey(namingInstance.getId());

                        cleanTotol+= namingInstanceService.updateByPrimaryKeySelective(namingInstance);
                        cleanInstanceIdList.add(namingInstance.getInstanceId());
                    }else{
                        boolean isMatch = objects.stream().anyMatch(obejct -> namingInstanceDb.getInstanceId().equals(((JSONObject) obejct).getString("instanceId")));
                        if(!isMatch){
                           // cleanTotol+= namingInstanceService.deleteByPrimaryKey(namingInstanceDb.getId());
                            cleanTotol+= namingInstanceService.updateByPrimaryKeySelective(namingInstance);
                            cleanInstanceIdList.add(namingInstanceDb.getInstanceId());
                        }
                    }
                }
            }
            pageNum++;
        } while(CollectionUtils.isNotEmpty(namingInstances) && namingInstances.size() == pageSize);
        log.warn("结束执行清理无效naminginstance任务 "+new Date());
        HashMap<String, Object> resultData = new HashMap<>();
        resultData.put("cleanTotol",cleanTotol);
        resultData.put("cleanInstanceIdList",cleanInstanceIdList);
        return Result.success(resultData);
    }

    @Override
    public Result delSoftDeleteData(int beforeTime) {
        return Result.success(namingInstanceService.delSoftDeleteData(beforeTime));
    }
}
