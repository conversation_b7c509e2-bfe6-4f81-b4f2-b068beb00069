package com.xiaomi.dayu.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageRowBounds;
import com.google.common.collect.Lists;
import com.xiaomi.dayu.InstanceCovert;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.InstanceUtils;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.common.util.Tool;
import com.xiaomi.dayu.dao.NamingInstanceMapper;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.domain.Entity;
import com.xiaomi.dayu.model.dto.ConsumerServiceDTO;
import com.xiaomi.dayu.model.dto.ServiceDTO;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.mybatis.example.NamingInstanceExample;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
@Slf4j
@Service
public class NamingInstanceService {
    @Resource
    private NamingInstanceMapper namingInstanceMapper;
    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;
    @Autowired
    private AccountServiceRpc accountServiceRpc;

    @NacosValue(value = "${isQueryNaming4DB:false}",autoRefreshed = true)
    private volatile Boolean isQueryNaming4DB;

    @NacosValue(value = "${queryNamingPageFlag:false}",autoRefreshed = true)
    private Boolean queryNamingPageFlag;

    @Autowired
    private UserService userService;

    @Value("${monitor.url.pattern}")
    private String monitorUrlPattern;


    public  Boolean getIsQueryNaming4DB(){
        return this.isQueryNaming4DB;
    }
    public List<NamingInstance> selectByExample(NamingInstanceExample example){
        return namingInstanceMapper.selectByExample(example);
    };


    public List<NamingInstance> queryNamingInstance(){
        NamingInstanceExample example = new NamingInstanceExample();
        return namingInstanceMapper.selectByExample(example);
    }


    public List<String> queryServicesByAddress(String address) {
        return namingInstanceMapper.queryServicesByAddress(address);
    }
    public List<Entity> queryByAddress(String side,String address) {
        return  queryNamingService( side,  null,null,address);
    }
    public List<Entity> queryByService(String side,String service) {
        return  queryNamingService( side,  null,service,null);
    }
    public List<Entity> queryByApplication(String side, String application) {
       return  queryNamingService( side,  application,null,null);
    }
    public List<Entity> queryNamingService(String side, String application,String service,String address) {
        NamingInstanceExample example = new NamingInstanceExample();
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andDelEqualTo(false);
        if(StringUtils.isNotBlank(application)){
            criteria.andApplicationEqualTo(application);
        }
        if(StringUtils.isNotBlank(address)){
            criteria.andIpEqualTo(address);
        }
        if(StringUtils.isNotBlank(service)){
            criteria.andFullServiceEqualTo(service);
        }
        criteria.andSideEqualTo(side);
        List<NamingInstance> namingInstances = namingInstanceMapper.selectByExample(example);

        if(CollectionUtils.isEmpty(namingInstances)){
            return null;
        }
        if(Constants.PROVIDER_SIDE.equals(side)){
            return namingInstances.stream().map(instance ->
                    InstanceCovert.namingInstanceCovertToProvider(instance)
            ).collect(Collectors.toList());
        }else{
            return namingInstances.stream().map(instance ->
                    InstanceCovert.namingInstanceCovertToConsumer(instance)
            ).collect(Collectors.toList());
        }
    }



    public ResultResponse<PageResult<ServiceDTO>> getProviderServiceDTOS(String pattern, String filter,String searchKey, boolean self,int module, int pageNum, int pageSize) {
        Page<NamingInstance> providers = null ;
        List<String> appList = null;
        if(self && !UserInfoThreadLocal.getUserInfo().isAdmin()){
            appList = UserInfoThreadLocal.getUserInfo().getApplicationNames();
        }
        if(StringUtils.isNotEmpty(searchKey)){
            appList = Lists.newArrayList(searchKey);
        }
        if (!filter.contains(Constants.ANY_VALUE) && !filter.contains(Constants.INTERROGATION_POINT)) {
            // filter with specific string
            if (Constants.IP.equals(pattern)) {
                providers = findPageByAddress(Constants.PROVIDER_SIDE,filter,appList,pageNum,pageSize);
            } else if (Constants.SERVICE.equals(pattern)) {
                providers = findPageByService(Constants.PROVIDER_SIDE,filter,appList,pageNum,pageSize);
            } else if (Constants.APPLICATION.equals(pattern)) {
                providers = findPageByApplication(Constants.PROVIDER_SIDE,filter,appList,pageNum,pageSize);
            }
        } else {
            //TODO 模糊查询
            // filter with fuzzy search
            if (Constants.SERVICE.equals(pattern)) {
                providers = findPageByService(Constants.PROVIDER_SIDE,null,appList,pageNum,pageSize);
            } else if (Constants.APPLICATION.equals(pattern)) {
                providers = findPageByApplication(Constants.PROVIDER_SIDE,null,appList,pageNum,pageSize);
            }
            else if (Constants.IP.equals(pattern)) {
                providers = findPageByAddress(Constants.PROVIDER_SIDE,null,appList,pageNum,pageSize);
            }
            // replace dot symbol and asterisk symbol to java-based regex pattern
            filter = filter.toLowerCase().replace(Constants.PUNCTUATION_POINT, Constants.PUNCTUATION_SEPARATOR_POINT);
            // filter start with [* 、? 、+] will triggering PatternSyntaxException
            if (filter.startsWith(Constants.ANY_VALUE)
                    || filter.startsWith(Constants.INTERROGATION_POINT) || filter.startsWith(Constants.PLUS_SIGNS)) {
                filter = Constants.PUNCTUATION_POINT + filter;
            }
            // search with no case insensitive
            Pattern regex = Pattern.compile(filter, Pattern.CASE_INSENSITIVE);
            /*for (String candidate : candidates) {
                Matcher matcher = regex.matcher(candidate);
                if (matcher.matches() || matcher.lookingAt()) {
                    if (Constants.SERVICE.equals(pattern)) {
                        providers.addAll(findByService(candidate));
                    }
                    else if (Constants.IP.equals(pattern)) {
                        providers.addAll(findByAddress(candidate));
                    }
                    else {
                        providers.addAll(findByApplication(candidate));
                    }
                }
            }*/
        }
        if(providers == null){
            return ResultResponse.success(new PageResult<ServiceDTO>(null,0,pageSize,pageNum));
        }
        List<NamingInstance> result = providers.getResult();
        List<ServiceDTO> collect = null;
        if(CollectionUtils.isNotEmpty(result)){
            UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
            StopWatch stopWatch = new StopWatch("遍历开始");
            stopWatch.start();
            collect = result.parallelStream().map(instance ->
                    namingInstanceCovertToServiceDTO(instance,module,userInfo)
            ).collect(Collectors.toList());
            stopWatch.stop();
            System.err.println(stopWatch.prettyPrint());
        }
        long total = providers.getTotal();
        return ResultResponse.success(new PageResult<ServiceDTO>(collect,(int)total,pageSize,pageNum));

    }

    private ServiceDTO namingInstanceCovertToServiceDTO(NamingInstance namingInstance, int module, UserInfo userInfo) {
        ServiceDTO seriveDTO = new ServiceDTO();
        seriveDTO.setFullService(namingInstance.getFullService());
        seriveDTO.setAppName(namingInstance.getApplication());
        seriveDTO.setService(namingInstance.getDubboService());
        seriveDTO.setGroup(namingInstance.getDubboGroup());
        seriveDTO.setVersion(namingInstance.getDubboVersion());

        List<String> applicationNames = userInfo.getApplicationNames();
        String fullService = seriveDTO.getFullService();
        seriveDTO.setOwn(applicationNames.contains(seriveDTO.getAppName()));
        seriveDTO.setCanSkip(applicationNames.contains(seriveDTO.getAppName()));

        if(module == 1){
            NamingInstanceExample namingInstanceExample = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = namingInstanceExample.createCriteria();
            criteria.andDelEqualTo(false);
            if(StringUtils.isNotBlank(fullService)){
                criteria.andFullServiceEqualTo(fullService);
            }else{
                if(StringUtils.isNotBlank(namingInstance.getDubboService())){
                    criteria.andDubboServiceEqualTo(namingInstance.getDubboService());
                }
                if(StringUtils.isNotBlank(namingInstance.getDubboGroup())){
                    criteria.andDubboGroupEqualTo(namingInstance.getDubboGroup());
                }
                if(StringUtils.isNotBlank(namingInstance.getDubboVersion())){
                    criteria.andDubboVersionEqualTo(namingInstance.getDubboVersion());
                }
            }
            try{
                List<NamingInstance> namingInstances =namingInstanceMapper.selectByExample(namingInstanceExample);
                int providerCount = 0;
                int consumerCount = 0;
                int providerHealthCount=0;
                int consumerHealthCount=0;
                for (NamingInstance instance : namingInstances) {
                    if(Constants.PROVIDER_SIDE.equals(instance.getSide())){
                        providerCount++;
                        if(instance.getHealthy()){
                            providerHealthCount++;
                        }
                        if(StringUtils.isBlank(seriveDTO.getDubboVersion())){
                            seriveDTO.setDubboVersion(InstanceUtils.getDubboVersion(instance.getMetadata()));
                        }
                    }else if(Constants.CONSUMER_SIDE.equals(instance.getSide())){
                        consumerCount++;
                        if(instance.getHealthy()){
                            consumerHealthCount++;
                        }
                    }
                }
                seriveDTO.setServiceCount(providerCount);
                seriveDTO.setServiceHealth(providerHealthCount);
                seriveDTO.setConsumerCount(consumerCount);
                seriveDTO.setConsumerHealth(consumerHealthCount);

            }catch (Exception e){
                e.printStackTrace();
            }
            ProjectBo projectBo = iProjectServiceRpc.getProjectByName(seriveDTO.getAppName());
            if(projectBo != null && projectBo.getId() != null){
                seriveDTO.setMonitorUrl(monitorUrlPattern.replace("projectId",projectBo.getId()+"_"+projectBo.getName().replace("-","_")));
            }
        }
        return seriveDTO;
    }


    public int updateByExampleSelective(NamingInstance record, NamingInstanceExample example) {
        return namingInstanceMapper.updateByExampleSelective(record,example);
    }


    public Page<NamingInstance> findPageByApplication(String side, String filter, List<String> appList, int pageNum, int pageSize) {
        return queryPageNamingInstances( side,filter,null,null,appList,  pageNum,  pageSize);
    }

    public Page<NamingInstance> findPageByService(String side, String service) {
        return queryPageNamingInstances( side,null, service,null,null,  1,  100);
    }
    public Page<NamingInstance> findPageByService(String side, String filter, List<String> appList, int pageNum, int pageSize) {
        return queryPageNamingInstances( side,null, filter,null,appList,  pageNum,  pageSize);
    }
    public Page<NamingInstance> findPageByService(String side, String filter, String application, List<String> appList, int pageNum, int pageSize) {
        return queryPageNamingInstances( side,application, filter,null,appList,  pageNum,  pageSize);
    }

    public Page<NamingInstance> findPageByAddress(String side,String filter, List<String> appList,int pageNum, int pageSize) {
        return queryPageNamingInstances( side,null,null,filter,appList,  pageNum,  pageSize);
    }
    public Page<NamingInstance> queryPageNamingInstances(String side,String application,String fullService,String ip,List<String> appList, int pageNum, int pageSize){
        NamingInstanceExample example = new NamingInstanceExample();
        example.setDistinct(true);
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andDelEqualTo(false);
        if(StringUtils.isNotBlank(side)){
            criteria.andSideEqualTo(side);
        }
        if(StringUtils.isNotBlank(ip)){
            criteria.andIpEqualTo(ip);
        }
        if(StringUtils.isNotBlank(fullService)){
            criteria.andFullServiceEqualTo(fullService);
        }
        if(CollectionUtils.isNotEmpty(appList) && StringUtils.isNotBlank(application)){
            if(appList.contains(application)){
                criteria.andApplicationEqualTo(application);
            }else{
                return null;
            }
        }else if(CollectionUtils.isNotEmpty(appList) && StringUtils.isBlank(application)){
            criteria.andApplicationIn(appList);
        }else if(CollectionUtils.isEmpty(appList) && StringUtils.isNotBlank(application)){
            criteria.andApplicationEqualTo(application);
        }
        if(Constants.CONSUMER_SIDE.equals(side)){
            example.setOrderByClause(NamingInstance.Column.fullService.value());
            PageRowBounds pageRowBounds = new PageRowBounds((pageNum-1)*pageSize,pageSize);
            List<NamingInstance> namingInstances = namingInstanceMapper.selectByExampleWithDistinctRowboundsNoApp(example, pageRowBounds);
            if(CollectionUtils.isNotEmpty(namingInstances)){
                for (NamingInstance namingInstance : namingInstances) {
                    namingInstance.setDubboService(Tool.getInterface(namingInstance.getFullService()));
                    namingInstance.setDubboGroup(Tool.getGroup(namingInstance.getFullService()));
                    namingInstance.setDubboVersion(Tool.getVersion(namingInstance.getFullService()));
                }
            }
            return (Page)namingInstances;
        }else{
            example.setOrderByClause(
                    new StringBuilder(NamingInstance.Column.application.value()).append(",")
                            .append(NamingInstance.Column.fullService.value()).toString());
            PageRowBounds pageRowBounds = new PageRowBounds((pageNum-1)*pageSize,pageSize);
            if(queryNamingPageFlag){
                return (Page)namingInstanceMapper.selectByExampleWithDistinctRowbounds(example, pageRowBounds);
            }else{
                PageHelper.startPage(pageNum,pageSize,false);
                List<NamingInstance> namingInstances = namingInstanceMapper.selectByExampleWithDistinctNORowbounds(example);
                Page<NamingInstance> data = new Page<>(pageNum, pageSize);
                data.setTotal(16999);
                data.addAll(namingInstances);
                return data;
            }
        }
    }


        public Set<String> findServiceNames(String side) {
        return namingInstanceMapper.queryServiceNames(side);
    }

    public Set<String> findApplicationNames(String side) {
        return namingInstanceMapper.queryApplicationNames(side);
    }

    public ResultResponse<PageResult<ConsumerServiceDTO>> getConsumerServiceDTOS(String pattern, String filter, boolean self, int pageNum, int pageSize,boolean aggregate) {
        Page<NamingInstance> namingInstances = null;
        List<ConsumerServiceDTO> result = null;
        List<String> appList = new ArrayList<>();
        if(self && !UserInfoThreadLocal.getUserInfo().isAdmin()){
            appList = UserInfoThreadLocal.getUserInfo().getApplicationNames();
        }

        if (!filter.contains(Constants.ANY_VALUE) && !filter.contains(Constants.INTERROGATION_POINT)) {
            if (aggregate) {
                // filter with specific string
                if (Constants.IP.equals(pattern)) {
                    namingInstances = findPageByAddress(Constants.CONSUMER_SIDE,filter,appList,pageNum,pageSize);
                } else if (Constants.SERVICE.equals(pattern)) {
                    namingInstances = findPageByService(Constants.CONSUMER_SIDE,filter,appList,pageNum,pageSize);
                } else if (Constants.APPLICATION.equals(pattern)) {
                    namingInstances = findPageByApplication(Constants.CONSUMER_SIDE,filter,appList,pageNum,pageSize);
                }

            }else{
                namingInstances = findPageByFullService(Constants.CONSUMER_SIDE,filter,pageNum,pageSize);
            }

        } else {
            //TODO 模糊查询
            // filter with fuzzy search
            if(aggregate){
                if (Constants.IP.equals(pattern)) {
                    namingInstances = findPageByAddress(Constants.CONSUMER_SIDE,null,null,pageNum,pageSize);
                } else if (Constants.SERVICE.equals(pattern)) {
                    namingInstances = findPageByService(Constants.CONSUMER_SIDE,null,null,pageNum,pageSize);
                } else if (Constants.APPLICATION.equals(pattern)) {
                    namingInstances = findPageByApplication(Constants.CONSUMER_SIDE,null,null,pageNum,pageSize);
                }
            }else{
                namingInstances = this.findPageByFullService(Constants.CONSUMER_SIDE, filter, pageNum, pageSize);
            }

        }
        if(CollectionUtils.isNotEmpty(namingInstances)){
            List<String> finalAppList = appList;
            result = namingInstances.stream().map(instance -> {
                        ConsumerServiceDTO consumerServiceDTO = InstanceCovert.namingInstanceCovertToConsumerServiceDTO(instance);
                        NamingInstanceExample example = new NamingInstanceExample();
                        NamingInstanceExample.Criteria criteria = example.createCriteria();
                        criteria.andDelEqualTo(false);
                        criteria.andSideEqualTo(Constants.CONSUMER_SIDE);
                        if(aggregate){
                            if(StringUtils.isNotBlank(instance.getFullService())){
                                criteria.andFullServiceEqualTo(instance.getFullService());
                            }else{
                                if(StringUtils.isNotBlank(instance.getDubboService())){
                                    criteria.andDubboServiceEqualTo(instance.getDubboService());
                                }
                                if(StringUtils.isNotBlank(instance.getDubboGroup())){
                                    criteria.andDubboGroupEqualTo(instance.getDubboGroup());
                                }
                                if(StringUtils.isNotBlank(instance.getDubboVersion())){
                                    criteria.andDubboVersionEqualTo(instance.getDubboVersion());
                                }
                            }
                            consumerServiceDTO.setConsumerCount((int)namingInstanceMapper.countByExample(example));
                        }else{
                            consumerServiceDTO.setOwn(CollectionUtils.isNotEmpty(finalAppList)&& finalAppList.contains(consumerServiceDTO.getApplication())? true :false);
                            fillConsumerServiceDTOExtendInfo(consumerServiceDTO);
                        }
                        return consumerServiceDTO;
                    }
            ).collect(Collectors.toList());
        }
        return ResultResponse.success(new PageResult<>(result, (int)namingInstances.getTotal(),pageSize,pageNum));
    }

    private void fillConsumerServiceDTOExtendInfo(ConsumerServiceDTO consumerServiceDTO){
        if(StringUtils.isNotEmpty(consumerServiceDTO.getApplication())){
            try{
                List<UserInfoResult> userList = accountServiceRpc.queryUsersByAppName(consumerServiceDTO.getApplication());
                if(org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(userList)){
                    consumerServiceDTO.setDevelopers(userList.stream().map(UserInfoResult::getName).collect(Collectors.joining(",")));
                    String uid = userService.findUidByUserName(userList.get(0).getUserName());
                    consumerServiceDTO.setDepartment(userService.findDepartmentFullName(uid));
                }
            }catch (Exception e){
                log.error("查询开发人员或部门信息时报错",e);
            }
            ProjectBo projectBo = iProjectServiceRpc.getProjectByName(consumerServiceDTO.getApplication());
            if(projectBo != null && projectBo.getId() != null){
                consumerServiceDTO.setMonitorUrl(monitorUrlPattern.replace("projectId",projectBo.getId()+"_"+projectBo.getName().replace("-","_")));
            }
        }
    }

    private List<ConsumerServiceDTO> convertConsumers2DTO(List<Consumer> consumers,boolean agg) {
        Set<ConsumerServiceDTO> result = new TreeSet<>();
        List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
        for (Consumer consumer : consumers) {
            String service = consumer.getService();
            String group = Tool.getGroup(service);
            String version = Tool.getVersion(service);
            String interfaze = Tool.getInterface(service);
            ConsumerServiceDTO cons = new ConsumerServiceDTO();
            cons.setServiceId(consumer.getService());
            cons.setService(interfaze);
            cons.setGroup(group);
            cons.setVersion(version);
            cons.setConsumerCount(consumer.getConsumerCount());
            if(!agg){
                cons.setApplication(consumer.getApplication());
                cons.setAddress(consumer.getAddress());
                cons.setDubboVersion(InstanceUtils.getDubboVersion(consumer));
                cons.setOwn(applicationNames.contains(consumer.getApplication()));
            }

            result.add(cons);
        }
        return new ArrayList<>(result);
    }
    public Page<NamingInstance> findPageByFullService(String providerSide, String fullService, int pageNum, int pageSize) {
        return findPageByFullServiceAndApp( providerSide,  fullService, null,  pageNum,  pageSize);
    }
    public Page<NamingInstance> findPageByFullServiceAndApp(String providerSide, String fullService,String application, int pageNum, int pageSize) {
        NamingInstanceExample example = new NamingInstanceExample();
        example.setDistinct(true);
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andDelEqualTo(false);
        criteria.andSideEqualTo(providerSide);
        criteria.andFullServiceEqualTo(fullService);
        if(StringUtils.isNotBlank(application)){
            criteria.andApplicationEqualTo(application);
        }
        example.setOrderByClause(NamingInstance.Column.id.value());

        PageRowBounds pageRowBounds = new PageRowBounds((pageNum-1)*pageSize,pageSize);
        Page<NamingInstance> namingInstances = (Page)namingInstanceMapper.selectByExampleWithRowbounds(example, pageRowBounds);
        return  namingInstances;
    }


    public List<NamingInstance> selectByExampleWithRowbounds(NamingInstanceExample example, PageRowBounds pageRowBounds) {
        return namingInstanceMapper.selectByExampleWithRowbounds( example,  pageRowBounds);
    }

    public int deleteByPrimaryKey(Integer id) {
        return namingInstanceMapper.deleteByPrimaryKey(id);
    }
    public int updateByPrimaryKeySelective(NamingInstance namingInstance) {
        return namingInstanceMapper.updateByPrimaryKeySelective(namingInstance);
    }

    public List<NamingInstance> findAllSimpleInfoWithSide(String side) {
        int pageNum =1;
        int pageSize =1000;
        List<NamingInstance> listAll = new ArrayList<>();
        List<NamingInstance> list ;
        NamingInstanceExample example = new NamingInstanceExample();
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andDelEqualTo(false);
        criteria.andSideEqualTo(side);
        do{
            PageRowBounds pageRowBounds = new PageRowBounds((pageNum-1)*pageSize,pageSize);
            list = namingInstanceMapper.findAllSimpleInfoWithSideByPage(example, pageRowBounds);
            if(CollectionUtils.isNotEmpty(list)){
                listAll.addAll(list);
                pageNum++;
            }
        }while (CollectionUtils.isNotEmpty(list) && list.size() == pageSize);
        return listAll;
    }
    public Set<String> queryAllAppName(String providerSide) {
        return namingInstanceMapper.queryApplicationNames(providerSide);
    }


    public int delSoftDeleteData(int beforeDate) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.minusDays(beforeDate);
        Date from = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        NamingInstanceExample namingInstanceExample = new NamingInstanceExample();
        NamingInstanceExample.Criteria criteria = namingInstanceExample.createCriteria();
        criteria.andUpdateTimeLessThan(from);
        criteria.andDelEqualTo(true);
        int num = namingInstanceMapper.deleteByExample(namingInstanceExample);
        log.warn("删除时间点在{}以前软删除的数据,删除数量为{},"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(from),num);
        return num;
    }
}
