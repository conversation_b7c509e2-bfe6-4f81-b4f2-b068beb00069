package com.xiaomi.dayu.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.dao.NacosHistoryConfigMapper;
import com.xiaomi.dayu.model.dto.NacosHistoryConfigPageDTO;
import com.xiaomi.dayu.mybatis.entity.NacosHistoryConfig;
import com.xiaomi.dayu.mybatis.example.NacosHistoryConfigExample;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.nutz.dao.entity.annotation.Comment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Type NacosHistoryService.java
 * @Desc
 * @date 2025/3/31 15:10
 */
@Service
public class NacosHistoryConfigService {
	@Autowired
	private NacosHistoryConfigMapper nacosHistoryConfigMapper;

	public NacosHistoryConfigPageDTO listConfigHistory(String dataId, String group, String tenant, String appName, Integer pageNo, Integer pageSize) {

		NacosHistoryConfigExample example = new NacosHistoryConfigExample();
		NacosHistoryConfigExample.Criteria criteria = example.createCriteria();
		criteria.andDataIdEqualTo(dataId);
		if(StringUtils.isNotBlank(group)){
			criteria.andGroupIdEqualTo(group);
		}
		if(StringUtils.isNotBlank(tenant)){
			criteria.andTenantIdEqualTo(tenant);
		}
		if(StringUtils.isNotBlank(appName)){
			criteria.andAppNameEqualTo(appName);
		}
		example.setOrderByClause("gmt_create desc");
		PageRowBounds pageRowBounds = new PageRowBounds((pageNo-1)*pageSize,pageSize);
		Page<NacosHistoryConfig> pageData = (Page)nacosHistoryConfigMapper.selectByExample(example, pageRowBounds);
		NacosHistoryConfigPageDTO nacosHistoryConfigPageDTO = new NacosHistoryConfigPageDTO();
		if(CollectionUtils.isNotEmpty(pageData)){
			for (NacosHistoryConfig nacosHistoryConfig : pageData.getResult()) {
				nacosHistoryConfig.setGmtModified(nacosHistoryConfig.getGmtCreate());
				nacosHistoryConfig.setId(nacosHistoryConfig.getNid());
				nacosHistoryConfig.setNid(null);
			}
			List<NacosHistoryConfig> result = pageData.getResult();
			nacosHistoryConfigPageDTO.setPageItems(result);
			nacosHistoryConfigPageDTO.setPagesAvailable(pageData.getPages());
			nacosHistoryConfigPageDTO.setPageNumber(pageData.getPages());
			nacosHistoryConfigPageDTO.setTotalCount(pageData.getTotal());
		}else{
			nacosHistoryConfigPageDTO.setPageItems(null);
			nacosHistoryConfigPageDTO.setPagesAvailable(0);
			nacosHistoryConfigPageDTO.setPageNumber(pageData.getPages());
			nacosHistoryConfigPageDTO.setTotalCount(0);
		}
		return nacosHistoryConfigPageDTO;
	}

	public NacosHistoryConfig getConfigHistoryInfo(Long nid, String dataId, String group, String tenant) {
		NacosHistoryConfigExample example = new NacosHistoryConfigExample();
		NacosHistoryConfigExample.Criteria criteria = example.createCriteria();
		criteria.andDataIdEqualTo(dataId);
		if(StringUtils.isNotBlank(group)){
			criteria.andGroupIdEqualTo(group);
		}
		if(StringUtils.isNotBlank(tenant)){
			criteria.andTenantIdEqualTo(tenant);
		}
		if(nid!=null){
			criteria.andNidEqualTo(nid);
		}
		List<NacosHistoryConfig> nacosHistoryConfigs = nacosHistoryConfigMapper.selectByExampleWithBLOBs(example);
		if(CollectionUtils.isNotEmpty(nacosHistoryConfigs)){
			NacosHistoryConfig nacosHistoryConfig = nacosHistoryConfigs.get(0);
			nacosHistoryConfig.setGmtModified(nacosHistoryConfigs.get(0).getGmtCreate());
			return nacosHistoryConfig;
		}else{
			return null;
		}
	}
}
