/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service;

import com.xiaomi.dayu.model.dto.*;

import java.util.List;

public interface OverrideService {

    void saveOverride(DynamicConfigDTO override,String type);

    void updateOverride(DynamicConfigDTO override,String type);

    void deleteOverride(String id,String side,String type);

    void enableOverride(String id,String side,String type);

    void disableOverride(String id,String side,String type);

    DynamicConfigDTO findOverride(String id);

    DynamicConfigDTO findOverride(BaseDTO baseDTO);

    void saveWeight(WeightDTO weightDTO);

    void updateWeight(WeightDTO weightDTO);

    void deleteWeight(String id);

    WeightDTO findWeight(String id);

    void saveBalance(BalancingDTO balancingDTO);

    void updateBalance(BalancingDTO balancingDTO);

    void deleteBalance(String id);

    BalancingDTO findBalance(String id);

    List<BalancingDTO> findAllBalancing();

    List<DynamicConfigDTO> findAllOverrides();

    List<DynamicConfigDTO> queryDynamicConfig(String application);

    List<WeightDTO> findAllWeight();

    List<WeightDTO> queryWeight(String application);

    String getConfigPath(BaseDTO baseDTO);

    String getConfigPath(String scope, String application, String service, String group, String version);

    DynamicConfigDTO findOverride(String service, String appName, String service1, String group, String version);
}
