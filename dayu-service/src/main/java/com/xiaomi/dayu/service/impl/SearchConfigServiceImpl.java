package com.xiaomi.dayu.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.SentinelRulesReq;
import com.xiaomi.dayu.common.ConfigTypeEnum;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.dao.ConfigInfoMapper;
import com.xiaomi.dayu.model.dto.ConfigInfoAndExtendDTO;
import com.xiaomi.dayu.model.requests.SearchConfigReq;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import com.xiaomi.dayu.mybatis.example.ConfigInfoExample;
import com.xiaomi.dayu.service.SearchConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SearchConfigServiceImpl implements SearchConfigService {

    @Autowired
    private ConfigInfoMapper configInfoMapper;

    @Override
    public List<ConfigInfo> searchConfigByType( ConfigTypeEnum configTypeEnum) {
        return searchConfigByTypeAndApp( configTypeEnum,null);
    }
    @Override
    public List<ConfigInfo> searchConfigByTypeAndApp( ConfigTypeEnum configTypeEnum,String application) {
        if(StringUtils.isBlank(application)){
            List<String> appNameList = UserInfoThreadLocal.getUserInfo().getApplicationNames();
            if(CollectionUtils.isNotEmpty(appNameList)){
                return configInfoMapper.fuzzySearchConfig(appNameList, configTypeEnum.getConfigName());
            }
            return null;
        }else{
            return configInfoMapper.fuzzySearchConfig(Lists.list(application), configTypeEnum.getConfigName());
        }
    }

    @Override
    public List<ConfigInfo> queryConfigByIds(List<Long> ids) {
        ConfigInfoExample example = new ConfigInfoExample();
        ConfigInfoExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        return configInfoMapper.selectByExample(example);
    }

    @Override
    public List<ConfigInfoAndExtendDTO> queryConfigAndExtendListByAppName(String appName) {
        return configInfoMapper.queryConfigAndExtendListByAppName(appName);
    }

    @Override
    public List<ConfigInfoAndExtendDTO> searchConfigAndExtend(SearchConfigReq request) {
        ConfigInfoExample example = request.toExample();
        ConfigInfoExample.Criteria criteria = request.defaultCriteria(example);
        this.processSearchConfigCriteria(request, criteria);
        return configInfoMapper.searchConfigAndExtend(example);
    }
    @Override
    public Long searchConfigAndExtendTotal(SearchConfigReq request) {
        ConfigInfoExample example = request.toExample();
        ConfigInfoExample.Criteria criteria = request.defaultCriteria(example);
        this.processSearchConfigCriteria(request, criteria);
        return configInfoMapper.searchConfigAndExtendTotal(example);
    }

    private void processSearchConfigCriteria(SearchConfigReq request, ConfigInfoExample.Criteria criteria) {
        if (StringUtils.isNotBlank(request.getDataId())) {
            criteria.andCDataIdLike(request.getDataId() + "%");
        }
        if (StringUtils.isNotBlank(request.getEnvId())) {
            criteria.addCriterion("env_id=", request.getEnvId(), "envId");
        }
        if (request.getConfigType() != null) {
            criteria.addCriterion("config_type=", request.getConfigType(), "configType");
        }
    }

    public long countTotalConfigs(SearchConfigReq request) {
        ConfigInfoExample example = request.toExample();
        ConfigInfoExample.Criteria criteria = request.defaultCriteria(example);

        if (request.getConfigType() != null || StringUtils.isNotBlank(request.getEnvId())) {
            this.processSearchConfigCriteria(request, criteria);
            return this.configInfoMapper.countConfigAndExtend(example);
        }
        if (StringUtils.isNotBlank(request.getDataId())) {
            criteria.andDataIdLike(request.getDataId() + "%");
        }
        return this.configInfoMapper.countByExample(example);
    }

    @Override
    public int queryCountByAppNameNotEmpty() {
        return configInfoMapper.queryCountByAppNameNotEmpty();
    }

    @Override
    public PageResult<String> querySentinelAllRules(SentinelRulesReq sentinelRulesReq) {
        ConfigInfoExample example = new ConfigInfoExample();
        ConfigInfoExample.Criteria criteria = example.createCriteria();
        criteria.andDataIdLike("%"+sentinelRulesReq.getSuffix());
        PageHelper.startPage(sentinelRulesReq.getPageNo(),sentinelRulesReq.getPageSize());
        Page<ConfigInfo> page = (Page)configInfoMapper.selectByExampleWithBlob(example);
        PageResult<String> pageResult = new PageResult<>(page.getTotal(),page.getPageNum(),page.getPageSize());
        if(CollectionUtils.isNotEmpty(page.getResult())){
            List<String> data = page.getResult().stream().map(ConfigInfo::getContent).collect(Collectors.toList());
            pageResult.setData(data);
        }
        return pageResult;
    }

    @Override
    public ConfigInfo searchConfig(String tenant, String group, String dataId) {
        ConfigInfoExample example = new ConfigInfoExample();
        ConfigInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(tenant);
        criteria.andGroupIdEqualTo(group);
        criteria.andDataIdEqualTo(dataId);
        List<ConfigInfo> configInfoS = configInfoMapper.searchConfig(example);
        return CollectionUtils.isNotEmpty(configInfoS) ? configInfoS .get(0): null;
    }

}


