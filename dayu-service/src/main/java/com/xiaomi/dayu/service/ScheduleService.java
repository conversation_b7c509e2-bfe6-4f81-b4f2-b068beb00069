package com.xiaomi.dayu.service;

import com.alibaba.nacos.api.exception.NacosException;
import com.xiaomi.dayu.model.requests.response.DubboService;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.List;

public interface ScheduleService {
    public Result<List<DubboService>> loadDubboApiServices(String serviceName) throws NacosException;
    public Result<List<String>> getServiceMethod(String serviceName) throws NacosException;
}
