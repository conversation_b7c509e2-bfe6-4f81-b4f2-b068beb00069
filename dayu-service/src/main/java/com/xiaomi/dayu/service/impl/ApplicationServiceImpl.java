package com.xiaomi.dayu.service.impl;

import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.service.ApplicationService;
import com.xiaomi.youpin.hermes.bo.ApplicationListBo;
import com.xiaomi.youpin.hermes.entity.Project;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
@Service
public class ApplicationServiceImpl implements ApplicationService {
    @Autowired
    private AccountServiceRpc accountServiceRpc;
    @Override
    public List<String> getApplicationNames(String userName) {
        if (UserInfoThreadLocal.getUserInfo() != null && UserInfoThreadLocal.getUserInfo().getApplicationNames() != null) {
            return UserInfoThreadLocal.getUserInfo().getApplicationNames();
        }
        ApplicationListBo applicationListBo = this.accountServiceRpc.queryApplicationNamesByUsername(userName);

        if (applicationListBo == null || applicationListBo.getApplicationList() == null) {
            return new ArrayList<>();
        }
        return applicationListBo.getApplicationList().stream().map(Project::getName).collect(Collectors.toList());
    }
}
