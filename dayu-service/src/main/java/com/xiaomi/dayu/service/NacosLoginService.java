package com.xiaomi.dayu.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.gson.Gson;
import com.xiaomi.data.push.nacos.NacosNaming;
import com.xiaomi.dayu.model.bo.NacosLoginInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
@Slf4j
@Service
public class NacosLoginService {
    @NacosValue("${nacos.username}")
    private String username;

    @NacosValue("${nacos.password}")
    private String password;
    /**
     * 每5分钟更新nacos的accessToken
     */
    private ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    private String nacosAccessToken = "";

    public static final Gson gson = new Gson();


    @Autowired
    private NacosNaming namingService;
    //nacos token有效时间是30分钟
    @PostConstruct
    public void init() {
        executorService.scheduleAtFixedRate(this::doNacosLogin, 0, 10, TimeUnit.MINUTES);
    }

    public void doNacosLogin() {
        try {
            NacosLoginInfo stNacosLoginInfo = gson.fromJson(namingService.login(username, password), NacosLoginInfo.class);
            if (null != stNacosLoginInfo && StringUtils.isNotEmpty(stNacosLoginInfo.getAccessToken())) {
                nacosAccessToken = stNacosLoginInfo.getAccessToken();
            }
        } catch (Throwable ex) {
            log.error(ex.getMessage());
        }
    }

    public String getNacosAccessToken(){
        if(StringUtils.isBlank(nacosAccessToken)){
            synchronized (nacosAccessToken){
                if(StringUtils.isBlank(nacosAccessToken)){
                    doNacosLogin();
                }
            }
        }
        return nacosAccessToken;
    }

}
