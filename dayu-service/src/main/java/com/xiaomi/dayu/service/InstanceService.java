package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.naming.NamingService;
import com.google.common.collect.Maps;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.Tool;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.dto.docs.NamingInstanceOfflineDTO;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.mybatis.example.NamingInstanceExample;
import com.xiaomi.mone.current.threadpool.MoneThreadPoolExecutor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.json.Json;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
@Slf4j
@Service
public class InstanceService {
    private final static String path = "/nacos/v1/ns/instance/onlineOrOffline";
    private final static String path2 = "/nacos/v1/ns/instance";
    @Resource
    private NamingInstanceService namingInstanceService;

    @Resource
    private ProviderService providerService;
    @Resource
    private MoneThreadPoolExecutor moneThreadPoolExecutor;

    @SneakyThrows
    public List<String> onlineOrOfflineOneByOne(String appName, Integer projectId, Boolean enabled, List<String> ips, String operator, Enums.ChannelType channelType) {
        log.warn("渠道{}，用户{}，针对项目名{}，机房{},进行{}操作", channelType.name(), operator, appName, ips, (enabled ? "上线" : "下线"));
        List<String> operateServiceNameFailList = new ArrayList();
        if (namingInstanceService.getIsQueryNaming4DB()) {
            NamingInstanceExample example = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andDelEqualTo(false);
            criteria.andApplicationEqualTo(appName);
            criteria.andIpIn(ips);
            criteria.andSideEqualTo(Constants.PROVIDER_SIDE);
            List<NamingInstance> namingInstances = namingInstanceService.selectByExample(example);
            if (CollectionUtils.isNotEmpty(namingInstances)) {
                List<CompletableFuture> list = new ArrayList<>();
                for (NamingInstance namingInstance : namingInstances) {
                    CompletableFuture<Boolean> completableFuture = CompletableFuture.supplyAsync(() -> {
                        NamingInstanceOfflineDTO namingInstanceOfflineDTO = new NamingInstanceOfflineDTO();
                        String[] split = namingInstance.getServiceName().split("@@");
                        namingInstanceOfflineDTO.setServiceName(split.length>1 ? split[1]:split[0]);
                        namingInstanceOfflineDTO.setClusterName(namingInstance.getClusterName());
                        namingInstanceOfflineDTO.setGroupName(namingInstance.getGroupName());
                        namingInstanceOfflineDTO.setIp(namingInstance.getIp());
                        namingInstanceOfflineDTO.setPort(namingInstance.getPort());
                        namingInstanceOfflineDTO.setEphemeral(namingInstance.getEphemeral());
                        namingInstanceOfflineDTO.setWeight(namingInstance.getWeight());
                        namingInstanceOfflineDTO.setEnabled(enabled);
                        namingInstanceOfflineDTO.setMetadata(namingInstance.getMetadata());
                        Map<String, String> params;
                        try {
                            params = BeanUtils.describe(namingInstanceOfflineDTO);
                            HttpClient.HttpResult response = HttpClient.request(HttpClient.HTTP + HttpClient.NACOS_URL + path2, null, params, null, HttpClient.UTF_8, RequestMethod.PUT.name());
                            if (response.code == 200) {
                                return true;
                            }
                        } catch (Exception e) {
                            log.error("渠道:{}，用户:{}，针对项目名:{}，ip:{},服务名:{}进行{}操作，执行时报异常！",channelType.name(),operator,appName,namingInstance.getIp(),namingInstance.getServiceName(),(enabled ? "上线":"下线"));
                            throw new RuntimeException(e);
                        }
                        operateServiceNameFailList.add(namingInstance.getFullService()+":"+namingInstance.getIp()+":"+namingInstance.getPort());
                        return false;
                    },moneThreadPoolExecutor);
                    list.add(completableFuture);
                }
                CompletableFuture.allOf(list.toArray(new CompletableFuture[0])).get(120, TimeUnit.SECONDS);
            }
        }
        return operateServiceNameFailList;
    }

    public List<String> onlineOrOffline(String appName, Integer projectId, Boolean enabled, List<String> ips, String operator, Enums.ChannelType channelType){
        log.warn("渠道{}，用户{}，针对项目名{}，机房{},进行{}操作",channelType.name(),operator,appName,ips,(enabled ? "上线":"下线"));
        List<String> serviceNameList =null;
        Map<String, List<String>> serviceNameToIpsMap = new HashMap<>();
        if(namingInstanceService.getIsQueryNaming4DB()){
            NamingInstanceExample example = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andDelEqualTo(false);
            criteria.andApplicationEqualTo(appName);
            criteria.andIpIn(ips);
            criteria.andSideEqualTo(Constants.PROVIDER_SIDE);
            List<NamingInstance> namingInstances = namingInstanceService.selectByExample(example);
            if(CollectionUtils.isNotEmpty(namingInstances)){
                namingInstances.forEach(namingInstance->{
                    if(!serviceNameToIpsMap.containsKey(namingInstance.getServiceName())){
                        serviceNameToIpsMap.put(namingInstance.getServiceName(),new ArrayList<>());
                    }
                    serviceNameToIpsMap.get(namingInstance.getServiceName()).add(namingInstance.getIp()+Constants.COLON+namingInstance.getPort());
                });

                serviceNameList = namingInstances.stream().map(instance -> instance.getServiceName()).distinct().collect(Collectors.toList());
            }
        }else{
            List<Provider> byApplication = providerService.findByApplication(appName);
            if(CollectionUtils.isNotEmpty(byApplication)){
                serviceNameList = byApplication.stream().filter(provider -> ips.contains(provider.getIp())).map(provider ->
                {
                    String service = provider.getService();
                    String anInterface = Tool.getInterface(service);
                    String group = Tool.getGroup(service);
                    String version = Tool.getVersion(service);
                    StringBuilder builder = new StringBuilder("DEFAULT_GROUP@@providers:");
                    builder.append(anInterface);
                    if(StringUtils.isNotBlank(version)){
                        builder.append(":").append(version);
                    }
                    if(StringUtils.isNotBlank(group)){
                        builder.append(":").append(group);
                    }
                    String providerName = builder.toString();
                    if(!serviceNameToIpsMap.containsKey(providerName)){
                        serviceNameToIpsMap.put(providerName,new ArrayList<>());
                    }
                    serviceNameToIpsMap.get(providerName).add(provider.getIp()+Constants.COLON+provider.getPort());
                    return builder.toString();
                }).distinct().collect(Collectors.toList());
            }
            //throw new ServiceException("此模式不支持下线功能！");
        }
        List<String> operateServiceNameFailList = new ArrayList();
        if(CollectionUtils.isNotEmpty(serviceNameList)){
            List<CompletableFuture<Void>> asyncList = new ArrayList<>();
            serviceNameList.forEach(serviceName->{
                CompletableFuture<Void> runAsync = CompletableFuture.runAsync(() -> {
                    boolean ret = this.onlineOrOfflineByServiceName(serviceName, null, null, JSON.toJSONString(serviceNameToIpsMap.get(serviceName)), enabled.toString(), operator, channelType);
                    if(!ret){
                        operateServiceNameFailList.add(serviceName);
                    }
                });
                asyncList.add(runAsync);
            });
            try {
                CompletableFuture.allOf(asyncList.toArray(new CompletableFuture[asyncList.size()])).get(2, TimeUnit.MINUTES);
            } catch (InterruptedException | ExecutionException |TimeoutException e) {
                e.printStackTrace();
                log.error("渠道{}，用户{}，针对项目名{}，机房{},进行{}操作，执行时报异常！",channelType.name(),operator,appName,ips,(enabled ? "上线":"下线"));
            }
        }else{
            log.error("渠道{}，用户{}，针对项目名{}，机房{},进行{}操作，由于没有provider数据，执行完毕！",channelType.name(),operator,appName,ips,(enabled ? "上线":"下线"));
        }
        return operateServiceNameFailList;
    }

   public boolean onlineOrOfflineByServiceName(String serviceName,String namespaceId,String clusterName,String ips,String enabled,String operator, Enums.ChannelType channelType){
       Boolean aBoolean = Boolean.valueOf(enabled);
       log.warn((aBoolean ? "上线":"下线")+"服务执行开始，onlineOrOffline，ips={}，serviceName={}，enabled={}，operator={}，channelType={}",ips,serviceName,enabled,operator,channelType.name());
       HashMap<String, String> params = new HashMap<>();
       params.put("serviceName",serviceName);
       params.put("namespaceId",namespaceId);
       params.put("clusterName",clusterName);
       params.put("ips",ips);
       params.put("enabled",enabled);
       //调用nacos 进行上下线操作
       HttpClient.HttpResult response = HttpClient.request(HttpClient.HTTP+HttpClient.NACOS_URL + path, null, params, null, HttpClient.UTF_8, RequestMethod.PUT.name());

       List<NamingInstance> namingInstances = null;
       if(response.code ==200){
           //走数据库逻辑时，需要判断数据是否同步成功
           if(namingInstanceService.getIsQueryNaming4DB()){
               List<String> addressList = JSON.parseObject(ips, new TypeReference<List<String>>() {});
               List<String> ipList = addressList.stream().map(address -> address.split(Constants.COLON)[0]).collect(Collectors.toList());

               List<String> newIPList = new ArrayList<>(ipList);
               //补偿
//                List<Instance> nacosAllInstances = namingService.getAllInstances(serviceName.contains(Constants.NACOS_DEFAULT_GROUP) ? serviceName.split("@@")[1] : serviceName,false);
               //从nacos获取数据
               String interf = serviceName.contains(Constants.NACOS_DEFAULT_GROUP) ? serviceName.split("@@")[1] : serviceName;

               long startTime = new Date().getTime();
               long endTime = startTime+10*1000;
               while(new Date().getTime() < endTime && CollectionUtils.isNotEmpty(newIPList)){
                   //从数据库查询数据
                   NamingInstanceExample namingInstanceExample = new NamingInstanceExample();
                   NamingInstanceExample.Criteria criteria = namingInstanceExample.createCriteria();
                   criteria.andDelEqualTo(false);
                   criteria.andServiceNameEqualTo(serviceName.contains(Constants.NACOS_DEFAULT_GROUP)? serviceName:Constants.NACOS_DEFAULT_GROUP+serviceName);
                   criteria.andIpIn(newIPList);
                   namingInstances = namingInstanceService.selectByExample(namingInstanceExample);

                   for (NamingInstance namingInstance : namingInstances) {
                       if(aBoolean.equals(namingInstance.getEnabled())){
                           newIPList.remove(namingInstance.getIp());
                           log.warn((aBoolean ? "上线":"下线")+"服务执行正常结束，onlineOrOffline，ip={}，serviceName={}，operator={}，channelType={}",ips,serviceName,operator,channelType.name());
                       }else{
                           log.warn((aBoolean ? "上线":"下线")+"服务执行中，onlineOrOffline，ip={}，serviceName={}，reponse={}",namingInstance.getIp(),serviceName,namingInstance.getEnabled());
                       }
                   }
                   if(CollectionUtils.isEmpty(newIPList)){
                       log.warn((aBoolean ? "上线":"下线")+"服务执行该serviceName处理完毕，onlineOrOffline，ips={}，serviceName={}，enabled={}，operator={}，channelType={}",ips,serviceName,enabled,operator,channelType.name());
                   }
                   try {
                       Thread.sleep(1000L);
                   } catch (InterruptedException e) {
                       e.printStackTrace();
                   }

               }
               for (NamingInstance namingInstance : namingInstances) {
                   NamingInstance record = new NamingInstance();
                   record.setEnabled(aBoolean);
                   NamingInstanceExample example = new NamingInstanceExample();
                   NamingInstanceExample.Criteria criteria1 = example.createCriteria();
                   criteria1.andIdEqualTo(namingInstance.getId());
                   namingInstanceService.updateByExampleSelective(record,example);
                   log.warn((aBoolean ? "上线":"下线")+"服务执行状态同步到数据库，onlineOrOffline，ip={}，serviceName={}，enabled={}",namingInstance.getIp(),serviceName,enabled);
               }
           }
           log.warn((aBoolean ? "上线":"下线")+"服务执行处理完毕，onlineOrOffline，ips={}，serviceName={}，enabled={}",ips,serviceName,enabled);
           return true;
       }else{
           log.error((aBoolean ? "上线":"下线")+"服务异常，onlineOrOffline，params={}，reponse={}",JSON.toJSONString(params),JSON.toJSONString(response));
           return false;
       }
   }
}
