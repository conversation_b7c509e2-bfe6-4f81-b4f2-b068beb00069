/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.GetTagRuleListReq;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.dto.*;
import swimlane.bo.TagRuleInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * RouteService
 *
 */
public interface RouteService {

    void createConditionRoute(ConditionRouteDTO conditionRoute);

    void updateConditionRoute(ConditionRouteDTO newConditionRoute);

    void deleteConditionRoute(String id);

    void deleteAccess(String id);

    void createAccess(AccessDTO accessDTO);

    AccessDTO findAccess(String id);

    void updateAccess(AccessDTO accessDTO);

    void enableConditionRoute(String id);


    void disableConditionRoute(String id);


    ConditionRouteDTO findConditionRoute(ConditionRouteDTO crDTO);

    ConditionRouteDTO findConditionRoute(String id);

    void createTagRoute(TagRouteDTO tagRoute);

    void createMqTagRoute(MqTagRouteDTO tagRoute);

    void updateTagRoute(TagRouteDTO tagRoute);

    void updateMqTagRoute(MqTagRouteDTO mqTagRoute);

    void removeTagForInstance(TagInstanceDTO tagInstanceDTO,boolean allowed);

    void removeMqTagForInstance(TagInstanceDTO tagInstanceDTO);

    void createTagForInstance(TagInstanceDTO dto,String opUsername,int createType);

    void createMqTagForInstance(TagInstanceDTO dto,String opUsername,int createType);

    void deleteTagRoute(String id);


    void enableTagRoute(String id);


    void disableTagRoute(String id);


    TagRouteDTO findTagRoute(String id);


    MqTagRouteDTO findMqTagRoute(String id);


    List<ConditionRouteDTO> findAllConditionRoute();

    List<AccessDTO> findAllAccess();

    List<TagRouteDTO> findAllTagRoute();

    AppTagRouteDTO findRouteIndexByApp(String application);

    void createTagRule(TagRuleInfoDTO tagRuleInfoDTO);

    void updateTagRule(TagRuleInfoDTO tagRuleInfoDTO);

    void deleteTagRule(int id);

    PageResult<TagRuleInfoDTO> listTagRule(GetTagRuleListReq req);
}
