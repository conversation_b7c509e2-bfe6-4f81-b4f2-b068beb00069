/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.xiaomi.dayu.api.bo.GetTagRuleListReq;
import com.xiaomi.dayu.common.ConfigTypeEnum;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.util.*;
import com.xiaomi.dayu.dao.TagRuleInfoMapper;
import com.xiaomi.dayu.model.domain.MqTag;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.domain.Route;
import com.xiaomi.dayu.model.domain.Tag;
import com.xiaomi.dayu.model.dto.*;
import com.xiaomi.dayu.model.store.MqTagRoute;
import com.xiaomi.dayu.model.store.RoutingRule;
import com.xiaomi.dayu.model.store.TagRoute;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import com.xiaomi.dayu.mybatis.entity.TagRuleInfo;
import com.xiaomi.dayu.mybatis.entity.TagRuleInfoExample;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.dayu.rpc.OpenApiServiceRpc;
import com.xiaomi.dayu.service.ConfigParams;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.RouteService;
import com.xiaomi.dayu.service.SearchConfigService;
import com.xiaomi.youpin.gwdash.service.OpenApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import swimlane.bo.ConditionPairBo;
import swimlane.bo.TagRuleInfoDTO;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.nutz.dao.pager.Pager.DEFAULT_PAGE_SIZE;

@Component
@Slf4j
public class RouteServiceImpl extends AbstractService implements RouteService {

    private String prefix = Constants.CONFIG_KEY;

    public static final Gson gson = new Gson();

//    @DubboReference(check = false, interfaceClass = OpenApiService.class, group = "${ref.gwdash.service.group}", version = "${ref.gwdash.service.version:}", timeout = 10000)
//    private OpenApiService gwdashOpenApiService;
    @Autowired
    private OpenApiServiceRpc openApiServiceRpc;
    @Autowired
    private ProviderService providerService;

    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;

    @Autowired
    private SearchConfigService searchConfigService;

    @Autowired
    private ValidationPermissionService validationPermissionService;

    @Autowired
    private TagRuleInfoMapper tagRuleInfoMapper;

    @Override
    public void createConditionRoute(ConditionRouteDTO conditionRoute) {
        String id = ConvertUtil.getIdFromDTO(conditionRoute);
        String path = getPath(id, Constants.CONDITION_ROUTE);
        String existConfig = dynamicConfiguration.getConfig(path);
        RoutingRule existRule = null;
        if (existConfig != null) {
            existRule = YamlParser.loadObject(existConfig, RoutingRule.class);
        }
        existRule = RouteUtils.insertConditionRule(existRule, conditionRoute);
        //register2.7
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(existRule), validationPermissionService.getAppName(conditionRoute));

        //register2.6
        if (StringUtils.isNotEmpty(conditionRoute.getService())) {
            for (Route old : convertRouteToOldRoute(conditionRoute)) {
                registry.register(old.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
            }
        }

    }

    @Override
    public void updateConditionRoute(ConditionRouteDTO newConditionRoute) {
        String id = ConvertUtil.getIdFromDTO(newConditionRoute);
        String path = getPath(id, Constants.CONDITION_ROUTE);
        String existConfig = dynamicConfiguration.getConfig(path);
        if (existConfig == null) {
            throw new ResourceNotFoundException("no existing condition route for path: " + path);
        }
        RoutingRule routingRule = YamlParser.loadObject(existConfig, RoutingRule.class);
        ConditionRouteDTO oldConditionRoute = RouteUtils.createConditionRouteFromRule(routingRule);
        routingRule = RouteUtils.insertConditionRule(routingRule, newConditionRoute);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(routingRule), validationPermissionService.getAppName(newConditionRoute));

        //for 2.6
        if (StringUtils.isNotEmpty(newConditionRoute.getService())) {
            for (Route old : convertRouteToOldRoute(oldConditionRoute)) {
                registry.unregister(old.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
            }
            for (Route updated : convertRouteToOldRoute(newConditionRoute)) {
                registry.register(updated.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
            }
        }
    }

    @Override
    public void deleteConditionRoute(String id) {
        String path = getPath(id, Constants.CONDITION_ROUTE);

        String config = dynamicConfiguration.getConfig(path);
        if (config == null) {
            //throw exception
        }
        RoutingRule route = YamlParser.loadObject(config, RoutingRule.class);
        List<String> blackWhiteList = RouteUtils.filterBlackWhiteListFromConditions(route.getConditions());
        if (blackWhiteList.size() != 0) {
            route.setConditions(blackWhiteList);
            dynamicConfiguration.setConfig(path, YamlParser.dumpObject(route), validationPermissionService.getAppNameById(route.getScope(), id));
        } else {
            dynamicConfiguration.deleteConfig(path);
        }

        //for 2.6
        if (Constants.SERVICE.equals(route.getScope())) {
            RoutingRule originRule = YamlParser.loadObject(config, RoutingRule.class);
            ConditionRouteDTO conditionRouteDTO = RouteUtils.createConditionRouteFromRule(originRule);
            for (Route old : convertRouteToOldRoute(conditionRouteDTO)) {
                registry.unregister(old.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
            }
        }
    }

    @Override
    public void deleteAccess(String id) {
        String path = getPath(id, Constants.CONDITION_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            RoutingRule ruleDTO = YamlParser.loadObject(config, RoutingRule.class);
            List<String> blackWhiteList = RouteUtils.filterBlackWhiteListFromConditions(ruleDTO.getConditions());
            List<String> conditions = RouteUtils.filterConditionRuleFromConditions(ruleDTO.getConditions());
            if (conditions.size() == 0) {
                dynamicConfiguration.deleteConfig(path);
            } else {
                ruleDTO.setConditions(conditions);
                dynamicConfiguration.setConfig(path, YamlParser.dumpObject(ruleDTO), validationPermissionService.getAppNameById(ruleDTO.getScope(), id));
            }
            //2.6
            if (Constants.SERVICE.equals(ruleDTO.getScope()) && blackWhiteList.size() > 0) {
                Route route = RouteUtils.convertBlackWhiteListtoRoute(blackWhiteList, Constants.SERVICE, id);
                registry.unregister(route.toUrl());
            }
        }
    }

    @Override
    public void createAccess(AccessDTO accessDTO) {
        String id = ConvertUtil.getIdFromDTO(accessDTO);
        String path = getPath(id, Constants.CONDITION_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        List<String> blackWhiteList = RouteUtils.convertToBlackWhiteList(accessDTO);
        RoutingRule ruleDTO;
        if (config == null) {
            ruleDTO = new RoutingRule();
            ruleDTO.setEnabled(true);
            if (StringUtils.isNoneEmpty(accessDTO.getApplication())) {
                ruleDTO.setScope(Constants.APPLICATION);
            } else {
                ruleDTO.setScope(Constants.SERVICE);
            }
            ruleDTO.setKey(id);
            ruleDTO.setConditions(blackWhiteList);
        } else {
            ruleDTO = YamlParser.loadObject(config, RoutingRule.class);
            if (ruleDTO.getConditions() == null) {
                ruleDTO.setConditions(blackWhiteList);
            } else {
                ruleDTO.getConditions().addAll(blackWhiteList);
            }
        }
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(ruleDTO), validationPermissionService.getAppName(accessDTO));

        //for 2.6
        if (ruleDTO.getScope().equals("service")) {
            Route route = RouteUtils.convertAccessDTOtoRoute(accessDTO);
            registry.register(route.toUrl());
        }

    }

    @Override
    public AccessDTO findAccess(String id) {
        String path = getPath(id, Constants.CONDITION_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            RoutingRule ruleDTO = YamlParser.loadObject(config, RoutingRule.class);
            List<String> blackWhiteList = RouteUtils.filterBlackWhiteListFromConditions(ruleDTO.getConditions());
            AccessDTO accessDTO = RouteUtils.convertToAccessDTO(blackWhiteList, ruleDTO.getScope(), ruleDTO.getKey());
            accessDTO.setId(id);
            if (Constants.SERVICE.equals(ruleDTO.getScope())) {
                ConvertUtil.detachIdToService(id, accessDTO);
            }
            return accessDTO;
        }
        return null;
    }

    @Override
    public void updateAccess(AccessDTO accessDTO) {
        String key = ConvertUtil.getIdFromDTO(accessDTO);
        String path = getPath(key, Constants.CONDITION_ROUTE);
        List<String> blackWhiteList = RouteUtils.convertToBlackWhiteList(accessDTO);
        String config = dynamicConfiguration.getConfig(path);
        List<String> oldList = null;
        if (config != null) {
            RoutingRule ruleDTO = YamlParser.loadObject(config, RoutingRule.class);
            oldList = RouteUtils.filterBlackWhiteListFromConditions(ruleDTO.getConditions());
            List<String> conditions = RouteUtils.filterConditionRuleFromConditions(ruleDTO.getConditions());
            conditions.addAll(blackWhiteList);
            ruleDTO.setConditions(conditions);
            dynamicConfiguration.setConfig(path, YamlParser.dumpObject(ruleDTO), validationPermissionService.getAppName(accessDTO));
        }

        //2.6
        if (StringUtils.isNotEmpty(accessDTO.getService())) {
            Route oldRoute = RouteUtils.convertBlackWhiteListtoRoute(oldList, Constants.SERVICE, key);
            Route newRoute = RouteUtils.convertAccessDTOtoRoute(accessDTO);
            registry.unregister(oldRoute.toUrl());
            registry.register(newRoute.toUrl());
        }
    }

    @Override
    public void enableConditionRoute(String id) {
        String path = getPath(id, Constants.CONDITION_ROUTE);

        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            RoutingRule ruleDTO = YamlParser.loadObject(config, RoutingRule.class);

            if (Constants.SERVICE.equals(ruleDTO.getScope())) {
                //for2.6
                for (Route oldRoute : convertRouteToOldRoute(RouteUtils.createConditionRouteFromRule(ruleDTO))) {
                    URL oldURL = oldRoute.toUrl();
                    registry.unregister(oldURL);
                    oldURL = oldURL.addParameter("enabled", true);
                    registry.register(oldURL);
                }
            }

            //2.7
            ruleDTO.setEnabled(true);
            dynamicConfiguration.setConfig(path, YamlParser.dumpObject(ruleDTO), validationPermissionService.getAppNameById(ruleDTO.getScope(), id));
        }

    }

    @Override
    public void disableConditionRoute(String id) {
        String path = getPath(id, Constants.CONDITION_ROUTE);

        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            RoutingRule routeRule = YamlParser.loadObject(config, RoutingRule.class);

            if (Constants.SERVICE.equals(routeRule.getScope())) {
                //for 2.6
                for (Route oldRoute : convertRouteToOldRoute(RouteUtils.createConditionRouteFromRule(routeRule))) {
                    URL oldURL = oldRoute.toUrl();
                    registry.unregister(oldURL);
                    oldURL = oldURL.addParameter("enabled", false);
                    registry.register(oldURL);
                }
            }

            //2.7
            routeRule.setEnabled(false);
            dynamicConfiguration.setConfig(path, YamlParser.dumpObject(routeRule), validationPermissionService.getAppNameById(routeRule.getScope(), id));
        }

    }

    @Override
    public ConditionRouteDTO findConditionRoute(ConditionRouteDTO crDTO) {
        return findConditionRoute(ConvertUtil.getIdFromDTO(crDTO));
    }

    @Override
    public ConditionRouteDTO findConditionRoute(String id) {
        String path = getPath(id, Constants.CONDITION_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            RoutingRule routingRule = YamlParser.loadObject(config, RoutingRule.class);
            ConditionRouteDTO conditionRouteDTO = RouteUtils.createConditionRouteFromRule(routingRule);
            String service = conditionRouteDTO.getService();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(service)) {
                conditionRouteDTO.setService(service.replace("*", "/"));
            }
            String[] detachResult = ConvertUtil.detachId(id);
            if (detachResult.length > 1) {
                conditionRouteDTO.setServiceVersion(detachResult[1]);
            }
            if (detachResult.length > 2) {
                conditionRouteDTO.setServiceGroup(detachResult[2]);
            }
            conditionRouteDTO.setId(id);
            return conditionRouteDTO;
        }
        return null;
    }

    @Override
    public void createTagRoute(TagRouteDTO tagRoute) {
        log.info("createTagRoute，tagRoute={}",JSON.toJSONString(tagRoute));
        String id = tagRoute.getApplication();
        String path = getPath(id, Constants.TAG_ROUTE);
        TagRoute store = RouteUtils.convertTagroutetoStore(tagRoute);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(store), validationPermissionService.getAppName(tagRoute));
    }

    @Override
    public void createMqTagRoute(MqTagRouteDTO mqTagRouteDTO) {
        String id = mqTagRouteDTO.getApplication();
        String path = getPath(id, Constants.MQ_TAG_ROUTE);
        MqTagRoute store = RouteUtils.convertMqTagroutetoStore(mqTagRouteDTO);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(store), validationPermissionService.getAppName(mqTagRouteDTO));
    }

    @Override
    public void updateTagRoute(TagRouteDTO tagRoute) {
        log.info("updateTagRoute，tagRoute={}",JSON.toJSONString(tagRoute));
        String id = tagRoute.getApplication();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd ");
        Date date = new Date(System.currentTimeMillis());
        tagRoute.setUpdateTime(formatter.format(date));
        String path = getPath(id, Constants.TAG_ROUTE);
        if (dynamicConfiguration.getConfig(path) == null) {
            throw new ResourceNotFoundException("can not find tagroute: " + id);
        }
        TagRoute store = RouteUtils.convertTagroutetoStore(tagRoute);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(store), validationPermissionService.getAppName(tagRoute));
    }

    @Override
    public void updateMqTagRoute(MqTagRouteDTO mqTagRoute) {
        log.info("updateMqTagRoute，mqTagRoute={}",JSON.toJSONString(mqTagRoute));
        String id = mqTagRoute.getApplication();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd ");
        Date date = new Date(System.currentTimeMillis());
        mqTagRoute.setUpdateTime(formatter.format(date));
        String path = getPath(id, Constants.MQ_TAG_ROUTE);
        if (dynamicConfiguration.getConfig(path) == null) {
            throw new ResourceNotFoundException("can not find mq tag route: " + id);
        }
        MqTagRoute store = RouteUtils.convertMqTagroutetoStore(mqTagRoute);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(store), validationPermissionService.getAppName(mqTagRoute));
    }

    @Override
    public void removeTagForInstance(TagInstanceDTO tagInstanceDTO, boolean allowed) {
        log.info("removeTagForInstance，tagInstanceDTO={}，allowed={}",JSON.toJSONString(tagInstanceDTO),allowed);
        TagRouteDTO tagRoute = findTagRoute(tagInstanceDTO.getApplication());
        if (Objects.isNull(tagRoute)) {
            throw new ParamValidationException("app tag route is not exist!");
        }
        tagRoute.getTags().forEach(tag -> {
            if (tag.getName().equals(tagInstanceDTO.getTagName())) {
                //泳道标签不允许自行删除
                if (tag.getCreateType() == Constants.TAG_CREATE_TYPE_SWIMLANE && !allowed) {
                    throw new ParamValidationException("can not remove swim lane tag");
                }
                tagInstanceDTO.getAddresses().forEach(addrToBeRemove -> {
                    tag.getAddresses().remove(addrToBeRemove);
                });
            }
        });
        tagRoute.setTags(tagRoute.getTags().stream().filter(tag -> !tag.getAddresses().isEmpty()).collect(Collectors.toList()));
        tagRoute.setMd5(tagRoute.getNewMd5());
        updateTagRoute(tagRoute);
    }

    @Override
    public void removeMqTagForInstance(TagInstanceDTO tagInstanceDTO) {
        log.info("removeMqTagForInstance，tagInstanceDTO={}",JSON.toJSONString(tagInstanceDTO));
        MqTagRouteDTO mqTagRoute = findMqTagRoute(tagInstanceDTO.getApplication());
        if (Objects.isNull(mqTagRoute)) {
            throw new ParamValidationException("app tag route is not exist!");
        }
        mqTagRoute.getMqTags().forEach(tag -> {
            if (tag.getName().equals(tagInstanceDTO.getTagName())) {
                tagInstanceDTO.getAddresses().forEach(addrToBeRemove -> {
                    tag.getAddresses().remove(addrToBeRemove);
                });
            }
        });
        mqTagRoute.setMqTags(mqTagRoute.getMqTags().stream().filter(tag -> !tag.getAddresses().isEmpty()).collect(Collectors.toList()));
        updateMqTagRoute(mqTagRoute);
    }

    @Override
    public void createTagForInstance(TagInstanceDTO dto, String opUsername, int createType) {
        log.info("createTagForInstance，dto={}，opUsername={}，createType={}",JSON.toJSONString(dto),opUsername,createType);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd ");
        Date date = new Date(System.currentTimeMillis());
        TagRouteDTO tagRoute = findTagRoute(dto.getApplication());
        if (Objects.isNull(tagRoute)) {
            //不存在动态标签配置，则新增
            TagRouteDTO tagRouteDTO = new TagRouteDTO();
            tagRouteDTO.setApplication(dto.getApplication());
            List<Tag> tags = new ArrayList<>();
            tagRouteDTO.setTags(tags);
            tags.add(new Tag(dto.getTagName(), createType, opUsername, formatter.format(date), dto.getAddresses()));
            tagRouteDTO.setMd5(tagRouteDTO.getNewMd5());
            createTagRoute(tagRouteDTO);
        } else {
            //已存在则更新
            if (!tagRoute.getTags().stream().map(Tag::getName).collect(Collectors.toList()).contains(dto.getTagName())) {
                //不存在该tag
                Tag tag = new Tag(dto.getTagName(), createType, opUsername, formatter.format(date), dto.getAddresses());
                tagRoute.getTags().add(tag);
            } else {
                tagRoute.getTags().forEach(tag -> {
                    if (tag.getName().equals(dto.getTagName())) {
                        tag.setUpdateTime(formatter.format(date));
                        dto.getAddresses().forEach(addrToBeAdd -> {
                            if (!tag.getAddresses().contains(addrToBeAdd)) {
                                tag.getAddresses().add(addrToBeAdd);
                            }
                        });
                    }
                });
            }
            tagRoute.setMd5(tagRoute.getNewMd5());
            updateTagRoute(tagRoute);
        }
    }

    @Override
    public void createMqTagForInstance(TagInstanceDTO dto, String opUsername, int createType) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(System.currentTimeMillis());
        MqTagRouteDTO mqTagRoute = findMqTagRoute(dto.getApplication());
        if (Objects.isNull(mqTagRoute)) {
            //不存在动态标签配置，则新增
            MqTagRouteDTO mqTagRouteDTO = new MqTagRouteDTO();
            mqTagRouteDTO.setApplication(dto.getApplication());
            if (dto.getAppTopicToGroups() != null) {
                mqTagRouteDTO.setAppTopicToGroups(dto.getAppTopicToGroups());
            } else {
                mqTagRouteDTO.setAppTopicToGroups("");
            }
            List<MqTag> mqTags = new ArrayList<>();
            mqTagRouteDTO.setMqTags(mqTags);
            if (dto.isEnable()) {
                mqTags.add(new MqTag(dto.getTagName(), dto.getMqTopicList(), opUsername, formatter.format(date), dto.getAddresses()));
            }
            createMqTagRoute(mqTagRouteDTO);
        } else {
            //已存在则更新
            if (!mqTagRoute.getMqTags().stream().map(MqTag::getName).collect(Collectors.toList()).contains(dto.getTagName()) && dto.isEnable()) {
                //不存在该tag
                MqTag mqTag = new MqTag(dto.getTagName(), dto.getMqTopicList(), opUsername, formatter.format(date), dto.getAddresses());
                mqTagRoute.getMqTags().add(mqTag);
            } else if (dto.isEnable()) {
                mqTagRoute.getMqTags().forEach(mqTag -> {
                    if (mqTag.getName().equals(dto.getTagName())) {
                        mqTag.setUpdateTime(formatter.format(date));
                        dto.getAddresses().forEach(addrToBeAdd -> {
                            if (!mqTag.getAddresses().contains(addrToBeAdd)) {
                                mqTag.getAddresses().add(addrToBeAdd);
                            }
                        });
                    }
                });
            }
            updateMqTagRoute(mqTagRoute);
        }
    }

    @Override
    public void deleteTagRoute(String id) {
        String path = getPath(id, Constants.TAG_ROUTE);
        dynamicConfiguration.deleteConfig(path);
    }

    @Override
    public void enableTagRoute(String id) {
        String path = getPath(id, Constants.TAG_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            TagRoute tagRoute = YamlParser.loadObject(config, TagRoute.class);
            tagRoute.setEnabled(true);
            dynamicConfiguration.setConfig(path, YamlParser.dumpObject(tagRoute), id);
        }

    }

    @Override
    public void disableTagRoute(String id) {
        String path = getPath(id, Constants.TAG_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            TagRoute tagRoute = YamlParser.loadObject(config, TagRoute.class);
            tagRoute.setEnabled(false);
            dynamicConfiguration.setConfig(path, YamlParser.dumpObject(tagRoute), id);
        }

    }

    @Override
    public TagRouteDTO findTagRoute(String id) {
        String path = getPath(id, Constants.TAG_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {

            TagRoute tagRoute = YamlParser.loadObject(config, TagRoute.class);
            return RouteUtils.convertTagroutetoDisplay(tagRoute);
        }
        return null;
    }

    @Override
    public MqTagRouteDTO findMqTagRoute(String id) {
        String path = getPath(id, Constants.MQ_TAG_ROUTE);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            MqTagRoute mqTagRoute = YamlParser.loadObject(config, MqTagRoute.class);
            return RouteUtils.convertMqTagroutetoDisplay(mqTagRoute);
        }
        return null;
    }

    @Override
    public List<ConditionRouteDTO> findAllConditionRoute() {
        List<ConfigInfo> configInfos = searchConfigService.searchConfigByType(ConfigTypeEnum.ConditionRoutes);
        if (CollectionUtils.isNotEmpty(configInfos)) {
            return configInfos.stream().map(configInfo -> {
                RoutingRule routingRule = YamlParser.loadObject(configInfo.getContent(), RoutingRule.class);
                ConditionRouteDTO conditionRouteDTO = RouteUtils.createConditionRouteFromRule(routingRule);
                String service = conditionRouteDTO.getService();
                BaseVO baseVO = ConvertUtil.parseString2BaseVO(ConfigTypeEnum.ConditionRoutes, configInfo.getDataId());
                ConvertUtil.assign(baseVO, conditionRouteDTO);
                return conditionRouteDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<AccessDTO> findAllAccess() {
        List<ConfigInfo> configInfos = searchConfigService.searchConfigByType(ConfigTypeEnum.Accesses);
        if (CollectionUtils.isNotEmpty(configInfos)) {
            return configInfos.stream().map(configInfo -> {
                String config = configInfo.getContent();
                if (config != null) {
                    RoutingRule ruleDTO = YamlParser.loadObject(config, RoutingRule.class);
                    List<String> blackWhiteList = RouteUtils.filterBlackWhiteListFromConditions(ruleDTO.getConditions());
                    AccessDTO accessDTO = RouteUtils.convertToAccessDTO(blackWhiteList, ruleDTO.getScope(), ruleDTO.getKey());
                    BaseVO baseVO = ConvertUtil.parseString2BaseVO(ConfigTypeEnum.Accesses, configInfo.getDataId());
                    ConvertUtil.assign(baseVO, accessDTO);
                    return accessDTO;
                }
                return null;
            }).collect(Collectors.toList());

        }
        return null;
    }

    @Override
    public List<TagRouteDTO> findAllTagRoute() {
        List<ConfigInfo> configInfos = searchConfigService.searchConfigByType(ConfigTypeEnum.TagRoutes);
        if (CollectionUtils.isNotEmpty(configInfos)) {
            return configInfos.stream().map(configInfo -> {
                TagRoute tagRoute = YamlParser.loadObject(configInfo.getContent(), TagRoute.class);
                return RouteUtils.convertTagroutetoDisplay(tagRoute);
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public AppTagRouteDTO findRouteIndexByApp(String application) {
        AppTagRouteDTO result = new AppTagRouteDTO();
        List<EnvInstancesDTO> envInstancesDTOS = new ArrayList<>();
        result.setEnvInstancesDTOS(envInstancesDTOS);
        //标签配置
        TagRouteDTO tagRoute = findTagRoute(application);
        //改为 ip ——>providers
        Map<String, List<Provider>> providersMap = new HashMap<>();
        //mione中获取的环境实例列表
        Map<String, List<String>> envIpList = openApiServiceRpc.envMachinesByAppName(application, ConfigParams.getMilineEnv().getName());
        envIpList = ConvertUtil.clearEmptyEnv(envIpList);
        List<String> mioneIPs = new ArrayList<>();
        //nacos上的providers列表
        List<Provider> providers = providerService.findByApplication(application);

        if (providers != null && providers.size() != 0 && providers.get(0) != null) {
            try {
                //faas mesh项目下，同一个ip可能存在不同端口的 provider
                providers.forEach(provider -> {
                    String ip = provider.getAddress().substring(0, provider.getAddress().lastIndexOf(":"));
                    if (providersMap.get(ip) != null) {
                        providersMap.get(ip).add(provider);
                    } else {
                        providersMap.put(ip, Lists.newArrayList(provider));
                    }
                });
            } catch (Exception e) {
                logger.error("findRouteIndexByApp for each provider error:{}", e);
            }
        }
        for (Map.Entry<String, List<String>> e : envIpList.entrySet()) {
            EnvInstancesDTO dto = new EnvInstancesDTO();
            List<Instance> instances = new ArrayList<>();
            //部署环境
            dto.setEnv(e.getKey());
            dto.setInstancesList(instances);
            //实例
            e.getValue().forEach(ip -> {
                //这里ip是部署平台获取的实际ip，真实运行的实例
                mioneIPs.add(ip);
                //provider中存在该地址
                if (providersMap.containsKey(ip)) {
                    List<Provider> providerList = providersMap.get(ip);
                    Set<String> tmpAddressCache = new HashSet<>();
                    providerList.forEach(provider -> {
                        if (!tmpAddressCache.contains(provider.getAddress())){
                            Instance instance = new Instance();
                            instance.setAddress(provider.getAddress());
                            //是否有静态标?
                            Map<String, String> params = parseUrlParams(provider.getParameters());

                            if (params != null && params.containsKey(CommonConstants.TAG_KEY)) {
                                instance.setStaticTag(params.get(CommonConstants.TAG_KEY));
                            }
                            //解析动态标
                            result.setAppName(application);
                            if (tagRoute != null) {
                                result.setEnabled(tagRoute.isEnabled());
                                result.setUpdateTime(tagRoute.getUpdateTime());

                                //若配置中不存在该地址，则该地址无动态标
                                if (getAddresses(tagRoute.getTags()).contains(provider.getAddress())) {
                                    //存在动态标
                                    List<Tag> tagList = new ArrayList<>();
                                    tagRoute.getTags().forEach(tag -> {
                                        if (tag.getAddresses().contains(provider.getAddress())) {
                                            tagList.add(tag);
                                        }
                                    });
                                    instance.setTags(tagList);
                                }
                            }
                            instances.add(instance);
                            tmpAddressCache.add(provider.getAddress());
                        }
                    });
                }
            });
            envInstancesDTOS.add(dto);
        }
        //不在mione启动的实例，如本地启动
        EnvInstancesDTO localDTO = new EnvInstancesDTO();
        List<Instance> instances = new ArrayList<>();
        //部署环境
        localDTO.setEnv(Constants.LOCAL_ENV);
        localDTO.setInstancesList(instances);

        providersMap.keySet().forEach(providerIp -> {
            if (!mioneIPs.contains(providerIp)) {
                Instance instance = new Instance();
                if (providersMap.get(providerIp) != null && providersMap.get(providerIp).size() != 0) {
                    Provider provider = providersMap.get(providerIp).get(0);
                    instance.setAddress(provider.getAddress());
                    //是否有静态标?
                    Map<String, String> params = parseUrlParams(provider.getParameters());
                    if (params.containsKey(CommonConstants.TAG_KEY)) {
                        instance.setStaticTag(params.get(CommonConstants.TAG_KEY));
                    }

                    //动态标
                    //若配置中不存在该地址，则该地址无动态标
                    if (tagRoute != null) {
                        if (getAddresses(tagRoute.getTags()).contains(provider.getAddress())) {
                            //存在动态标
                            List<Tag> tagList = new ArrayList<>();
                            tagRoute.getTags().forEach(tag -> {
                                if (tag.getAddresses().contains(provider.getAddress())) {
                                    tagList.add(tag);
                                }
                            });
                            instance.setTags(tagList);
                        }
                    }
                    instances.add(instance);
                }
            }
        });

        if (!localDTO.getInstancesList().isEmpty()) {
            envInstancesDTOS.add(localDTO);
        }
        return result;
    }

    @Override
    public void createTagRule(TagRuleInfoDTO tagRuleInfoDTO) throws ParamValidationException {
        this.checkTagRuleInfoDTOParam(tagRuleInfoDTO);
        TagRuleInfo tagRuleInfo = tagRuleTransfer2Info(tagRuleInfoDTO);
        tagRuleInfoMapper.insert(tagRuleInfo);
    }

    @Override
    public void updateTagRule(TagRuleInfoDTO tagRuleInfoDTO) throws ParamValidationException {
        this.checkTagRuleInfoDTOParam(tagRuleInfoDTO);

        TagRuleInfo tagRuleInfo = tagRuleInfoMapper.selectByPrimaryKey((long) tagRuleInfoDTO.getRuleId());
        if (tagRuleInfo == null) {
            throw new ParamValidationException("自定义标签不存在");
        }
        tagRuleInfo.setUpdater(tagRuleInfo.getUpdater());
        tagRuleInfo.setUtime(System.currentTimeMillis());
        tagRuleInfo.setRouteTag(tagRuleInfoDTO.getRouteTag());
        //transfer expr
        tagRuleInfoDTO.getConditionList().forEach(conditionPairBo -> conditionPairBo.setParseExpr(ConvertUtil.adapt2RealParseExpr(conditionPairBo.getOriParseExpr())));

        tagRuleInfo.setConditionList(gson.toJson(tagRuleInfoDTO.getConditionList()));
        tagRuleInfo.setAppId((long) tagRuleInfoDTO.getAppId());
        tagRuleInfo.setAppName(tagRuleInfoDTO.getAppName());

        tagRuleInfoMapper.updateByPrimaryKeyWithBLOBs(tagRuleInfo);
    }

    @Override
    public void deleteTagRule(int id) throws ParamValidationException {
        if (id == 0) {
            throw new ParamValidationException("无效的id");
        }
        TagRuleInfo tagRuleInfo = tagRuleInfoMapper.selectByPrimaryKey((long) id);
        if (tagRuleInfo == null) {
            throw new ParamValidationException("该标签记录不存在");
        }
        tagRuleInfoMapper.deleteByPrimaryKey((long) id);
    }

    @Override
    public PageResult<TagRuleInfoDTO> listTagRule(GetTagRuleListReq req) {
        if (req.getPage() <= 0) {
            req.setPage(1);
        }
        if (req.getPageSize() <= 0) {
            req.setPageSize(DEFAULT_PAGE_SIZE);
        }
        int offset = (req.getPage() - 1) * req.getPageSize();
        TagRuleInfoExample totalExp = new TagRuleInfoExample();
        TagRuleInfoExample.Criteria totalExpCriteria = totalExp.createCriteria();

        TagRuleInfoExample listExp = new TagRuleInfoExample();
        TagRuleInfoExample.Criteria listExpCriteria = listExp.createCriteria();

        if (req.getAppId() != 0) {
            totalExpCriteria.andAppIdEqualTo((long) req.getAppId());
            listExpCriteria.andAppIdEqualTo((long) req.getAppId());
        }
        if (StringUtils.isNotEmpty(req.getCreator())) {
            totalExpCriteria.andCreatorLike("%" + req.getCreator() + "%");
            listExpCriteria.andCreatorLike("%" + req.getCreator() + "%");
        }
        if (StringUtils.isNotEmpty(req.getRouteTag())) {
            totalExpCriteria.andRouteTagEqualTo(req.getRouteTag());
            listExpCriteria.andRouteTagEqualTo(req.getRouteTag());
        }
        listExp.setOrderByClause("id desc limit " + req.getPageSize() + " offset " + offset);

        PageResult<TagRuleInfoDTO> pageResult;
        List<TagRuleInfo> infoList = tagRuleInfoMapper.selectByExampleWithBLOBs(listExp);
        if (infoList == null || infoList.size() == 0) {
            pageResult = new PageResult<>(Lists.newArrayList(), 0, req.getPageSize(), req.getPage());
        } else {
            List<TagRuleInfoDTO> dtoList = infoList.stream().map(RouteServiceImpl::tagRuleTransfer2DTO).collect(Collectors.toList());
            pageResult = new PageResult<>(dtoList, (int) tagRuleInfoMapper.countByExample(totalExp), req.getPageSize(), req.getPage());
        }
        return pageResult;
    }

    private String getPath(String key, String type) {
        key = key.replace("/", "*");
        if (type.equals(Constants.CONDITION_ROUTE)) {
            return prefix + Constants.PATH_SEPARATOR + key + Constants.CONDITION_RULE_SUFFIX;
        } else if (type.equals(Constants.MQ_TAG_ROUTE)) {
            return prefix + Constants.PATH_SEPARATOR + key + Constants.MQ_TAG_ROUTE_SUFFIX;
        } else {
            return prefix + Constants.PATH_SEPARATOR + key + Constants.TAG_RULE_SUFFIX;
        }
    }

    private String parseCondition(String condition) {
        StringBuilder when = new StringBuilder();
        StringBuilder then = new StringBuilder();
        condition = condition.trim();
        if (condition.contains("=>")) {
            String[] array = condition.split("=>", 2);
            String consumer = array[0].trim();
            String provider = array[1].trim();
            if (consumer.length() != 0) {
                if (when.length() != 0) {
                    when.append(" & ").append(consumer);
                } else {
                    when.append(consumer);
                }
            }
            if (provider.length() != 0) {
                if (then.length() != 0) {
                    then.append(" & ").append(provider);
                } else {
                    then.append(provider);
                }
            }
        }
        return (when.append(" => ").append(then)).toString();
    }

    private List<Route> convertRouteToOldRoute(ConditionRouteDTO route) {
        List<Route> oldList = new LinkedList<Route>();
        for (String condition : route.getConditions()) {
            Route old = new Route();
            old.setService(route.getService());
            old.setEnabled(route.isEnabled());
            old.setForce(route.isForce());
            old.setRuntime(route.isRuntime());
            old.setPriority(route.getPriority());
            String rule = parseCondition(condition);
            old.setRule(rule);
            oldList.add(old);
        }
        return oldList;
    }

    public List<String> getAddresses(List<Tag> tags) {
        return tags.stream()
                .filter(tag -> org.apache.dubbo.common.utils.CollectionUtils.isNotEmpty(tag.getAddresses()))
                .flatMap(tag -> tag.getAddresses().stream())
                .collect(Collectors.toList());
    }

    private Map<String, String> parseUrlParams(String paramsUrl) {
        String[] kvsStr = paramsUrl.split("&");
        if (paramsUrl.contains("&") || paramsUrl.contains("=")) {
            Map<String, String> params = new HashMap<>(kvsStr.length);
            Arrays.stream(kvsStr).forEach(kv -> {
                String[] kAndV = kv.split("=", 2);
                params.putIfAbsent(kAndV[0], kAndV[1]);
            });
            return params;
        } else {
            try {
                return JSON.parseObject(paramsUrl, new TypeReference<Map<String, String>>() {
                });
            } catch (Exception e) {
            }
        }
        return Maps.newHashMap();
    }

    private void checkTagRuleInfoDTOParam(TagRuleInfoDTO tagRuleInfoDTO) throws ParamValidationException {
        if (tagRuleInfoDTO.getRouteTag() == null) {
            throw new ParamValidationException("无效的标签");
        }
        if (tagRuleInfoDTO.getAppId() == 0) {
            throw new ParamValidationException("无效的所属应用");
        }
        if (tagRuleInfoDTO.getConditionList() == null) {
            throw new ParamValidationException("无效的规则配置");
        }
    }

    public static TagRuleInfo tagRuleTransfer2Info(TagRuleInfoDTO tagRuleInfoDTO) {
        TagRuleInfo tagRuleInfo = new TagRuleInfo();
        tagRuleInfo.setId(tagRuleInfo.getId());
        tagRuleInfo.setRouteTag(tagRuleInfoDTO.getRouteTag());
        tagRuleInfo.setConditionType(tagRuleInfoDTO.getConditionType());
        tagRuleInfo.setCreator(tagRuleInfoDTO.getCreator());
        tagRuleInfo.setUpdater(tagRuleInfoDTO.getCreator());
        long now = System.currentTimeMillis();
        tagRuleInfo.setCtime(now);
        tagRuleInfo.setUtime(now);
        //transfer expr
        tagRuleInfoDTO.getConditionList().forEach(conditionPairBo -> conditionPairBo.setParseExpr(ConvertUtil.adapt2RealParseExpr(conditionPairBo.getOriParseExpr())));

        tagRuleInfo.setConditionList(gson.toJson(tagRuleInfoDTO.getConditionList()));
        tagRuleInfo.setAppId((long) tagRuleInfoDTO.getAppId());
        tagRuleInfo.setAppName(tagRuleInfoDTO.getAppName());
        return tagRuleInfo;
    }

    public static TagRuleInfoDTO tagRuleTransfer2DTO(TagRuleInfo tagRuleInfo) {
        TagRuleInfoDTO tagRuleInfoDTO = new TagRuleInfoDTO();
        tagRuleInfoDTO.setRuleId(Math.toIntExact(tagRuleInfo.getId()));
        tagRuleInfoDTO.setRouteTag(tagRuleInfo.getRouteTag());
        tagRuleInfoDTO.setConditionType(tagRuleInfo.getConditionType());
        tagRuleInfoDTO.setCreator(tagRuleInfo.getCreator());
        tagRuleInfoDTO.setUpdater(tagRuleInfo.getUpdater());
        long now = System.currentTimeMillis();
        tagRuleInfoDTO.setCtime(now);
        tagRuleInfoDTO.setUtime(now);
        if (tagRuleInfo.getConditionList() != null) {
            try {
                List<ConditionPairBo> conditionPairBoList = gson.fromJson(tagRuleInfo.getConditionList(), new TypeToken<List<ConditionPairBo>>() {
                }.getType());
                tagRuleInfoDTO.setConditionList(conditionPairBoList);
            } catch (JsonSyntaxException e) {
                log.error("tagRuleTransfer2DTO failed,cause by:", e);
                tagRuleInfoDTO.setConditionList(Lists.newArrayList());
            }
        }
        tagRuleInfoDTO.setAppId(Math.toIntExact(tagRuleInfo.getAppId()));
        tagRuleInfoDTO.setAppName(tagRuleInfo.getAppName());
        return tagRuleInfoDTO;
    }


}
