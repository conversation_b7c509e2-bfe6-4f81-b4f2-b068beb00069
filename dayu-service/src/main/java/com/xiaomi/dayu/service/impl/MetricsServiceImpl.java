/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.model.dto.RelationDTO;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.service.MetricsService;
import com.xiaomi.dayu.service.NamingInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
@Slf4j
@Component
public class MetricsServiceImpl implements MetricsService {

    private static Cache<String, RelationDTO> localRelationCache = CacheBuilder.newBuilder().expireAfterWrite(120, TimeUnit.SECONDS)
            .maximumSize(100).build();
    private static Cache<String, List<NamingInstance>> localNamingServiceCache = CacheBuilder.newBuilder().expireAfterWrite(180, TimeUnit.SECONDS)
            .maximumSize(2).build();

    @Autowired
    private NamingInstanceService namingInstanceService;

    private final static List<String> excludeService = Lists.newArrayList("org.apache.dubbo.apidocs.core.providers.IDubboDocProvider");

    @Override
    public RelationDTO getApplicationRelation() {
        return getRelation();
/*        try {
            UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
            return localRelationCache.get(userInfo.isAdmin() ? "ADMIN":userInfo.getUserName(),()-> getRelation());
        } catch (ExecutionException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }*/
    }
    public RelationDTO getRelation() {
        final List<NamingInstance>[] list = new List[2];
        getAllNamingInstance(list);

        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        boolean isAdmin = userInfo.isAdmin();
        List<String> applicationNames = userInfo.getApplicationNames();
        int index = 0;
        // collect all service
        Set<String> serviceSet = new HashSet<>();

        // collect consumer's nodes map <application, node>
        Map<String, RelationDTO.Node> consumerNodeMap = new HashMap<>();
        // collect consumer's service and applications map <service, set<application>>
        Map<String, Set<String>> consumerServiceApplicationMap = new HashMap<>();
        for (NamingInstance consumer : list[0]) {
            String service = consumer.getFullService();
            if(excludeService.contains(service)){
                continue;
            }
            String application = consumer.getApplication();
            if (!consumerNodeMap.keySet().contains(application)) {
                RelationDTO.Node node = new RelationDTO.Node(index, application, RelationDTO.CONSUMER_CATEGORIES.getIndex(), applicationNames.contains(application));
                consumerNodeMap.put(application, node);
                index++;
            }
            serviceSet.add(service);
            consumerServiceApplicationMap.computeIfAbsent(service, s -> new HashSet<>()).add(application);
        }
        // collect provider's nodes
        Map<String, RelationDTO.Node> providerNodeMap = new HashMap<>();
        // collect provider's service and applications map <service, set<application>>
        Map<String, Set<String>> providerServiceApplicationMap = new HashMap<>();
        for (NamingInstance provider : list[1]) {
            String service = provider.getFullService();
            if(excludeService.contains(service)){
                continue;
            }
            String application = provider.getApplication();
            if (!providerNodeMap.keySet().contains(application)) {
                RelationDTO.Node node = new RelationDTO.Node(index, application, RelationDTO.PROVIDER_CATEGORIES.getIndex(), applicationNames.contains(application));
                providerNodeMap.put(application, node);
                index++;
            }
            serviceSet.add(service);
            providerServiceApplicationMap.computeIfAbsent(service, s -> new HashSet<>()).add(application);
        }
        // merge provider's nodes and consumer's nodes
        Map<String, RelationDTO.Node> nodeMap = new HashMap<>(consumerNodeMap);
        for (Map.Entry<String, RelationDTO.Node> entry : providerNodeMap.entrySet()) {
            if (nodeMap.get(entry.getKey()) != null) {
                nodeMap.get(entry.getKey()).setCategory(RelationDTO.CONSUMER_AND_PROVIDER_CATEGORIES.getIndex());
            } else {
                nodeMap.put(entry.getKey(), entry.getValue());
            }
        }
        // build link by same service
        Set<RelationDTO.Link> linkSet = new HashSet<>();
        Map<RelationDTO.Link,List<String>> consumerProviderLinkMap= new HashMap<>();
        Map<Integer,Set<RelationDTO.Link>> indexLinkSetMap = new HashMap<>();
        for (String service : serviceSet) {
            Set<String> consumerApplicationSet = consumerServiceApplicationMap.get(service);
            Set<String> providerApplicationSet = providerServiceApplicationMap.get(service);
            if (CollectionUtils.isNotEmpty(consumerApplicationSet) && CollectionUtils.isNotEmpty(providerApplicationSet)) {
                for (String providerApplication : providerApplicationSet) {
                    for (String consumerApplication : consumerApplicationSet) {
                        if (nodeMap.get(consumerApplication) != null && nodeMap.get(providerApplication) != null) {
                            Integer consumerIndex = nodeMap.get(consumerApplication).getIndex();
                            Integer providerIndex = nodeMap.get(providerApplication).getIndex();
                            RelationDTO.Link link = new RelationDTO.Link(consumerIndex, providerIndex);
                            List<String> serviceList = link.getServiceList();
                            if(!consumerProviderLinkMap.containsKey(link)){
                                linkSet.add(link);
                                consumerProviderLinkMap.put(link,serviceList);
                            }else{
                                serviceList = consumerProviderLinkMap.get(link);
                            }
                            serviceList.add(service);
                            if(!isAdmin){
                                indexLinkSetMap.computeIfAbsent(consumerIndex, s -> new HashSet<>()).add(link);
                                indexLinkSetMap.computeIfAbsent(providerIndex, s -> new HashSet<>()).add(link);
                            }
                        }
                    }
                }
            }
        }
        List<RelationDTO.Node> nodeList = null;
        if(!isAdmin){
            if(CollectionUtils.isNotEmpty(applicationNames)){
                TreeSet<Integer> indexTree = new TreeSet<>();
                nodeMap.forEach((app,node)->{
                    if(applicationNames.contains(app)){
                        indexTree.add(node.getIndex());
                    }
                });
                Set<RelationDTO.Link> newlinkSet = new HashSet<>();
                for (Integer idx : indexTree) {
                    if(indexLinkSetMap.get(idx) != null){
                        newlinkSet.addAll(indexLinkSetMap.get(idx));
                    }
                }
                linkSet = newlinkSet;
                Set<Integer> relationIndexSet = new HashSet<>();
                linkSet.forEach(link -> {
                    relationIndexSet.add(link.getSource());
                    relationIndexSet.add(link.getTarget());
                });
                nodeList = nodeMap.values().stream().filter(node -> relationIndexSet.contains(node.getIndex())).sorted(Comparator.comparingInt(RelationDTO.Node::getIndex)).collect(Collectors.toList());
            }else{
                nodeList = new ArrayList<>();
            }
        }else{
            // sort node by index
            nodeList = nodeMap.values().stream().sorted(Comparator.comparingInt(RelationDTO.Node::getIndex)).collect(Collectors.toList());
        }
        return new RelationDTO(nodeList, new ArrayList<>(linkSet));
    }

    private void getAllNamingInstance(List<NamingInstance>[] list) {
        try {
            CompletableFuture<Void> completableFuture1 = CompletableFuture.runAsync(()->{
                try {
                    list[0] = localNamingServiceCache.get(CommonConstants.CONSUMER_SIDE,()->
                        namingInstanceService.findAllSimpleInfoWithSide(CommonConstants.CONSUMER_SIDE)
                   );
                } catch (ExecutionException e) {
                    e.printStackTrace();
                }
            });
            CompletableFuture<Void> completableFuture2 = CompletableFuture.runAsync(() -> {
                try {
                    list[1] = localNamingServiceCache.get(CommonConstants.PROVIDER_SIDE,()->
                            namingInstanceService.findAllSimpleInfoWithSide(CommonConstants.PROVIDER_SIDE)
                    );
                } catch (ExecutionException e) {
                    e.printStackTrace();
                }
            });
            CompletableFuture.allOf(completableFuture1,completableFuture2).get();
        }catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
    }
    //flag 1:取source 2:取target

    private void getLinked2(Set<RelationDTO.Link> newlinkSet,Map<Integer,Set<RelationDTO.Link>> indexLinkSetMap,int index){
        Set<RelationDTO.Link> links = indexLinkSetMap.get(index);
        if(links != null){
            for (RelationDTO.Link link : links) {
                if(link.getSource() != link.getTarget()){
                    if(link.getSource() == index && newlinkSet.add(link)){
                        getLinked2(newlinkSet,indexLinkSetMap, link.getTarget());
                    }
                    if(link.getTarget() == index && newlinkSet.add(link)){
                        getLinked2(newlinkSet,indexLinkSetMap, link.getSource());
                    }
                }else{
                    newlinkSet.add(link);
                }
            }

        }
    }
}
