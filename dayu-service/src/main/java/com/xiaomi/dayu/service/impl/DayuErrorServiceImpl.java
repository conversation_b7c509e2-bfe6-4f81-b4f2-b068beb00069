package com.xiaomi.dayu.service.impl;

import com.xiaomi.dayu.api.service.DayuErrorService;
import com.xiaomi.dayu.rpc.ErrorScopeServiceRpc;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.dayu.rpc.PolicyServiceRpc;
import com.xiaomi.mone.drizzleapi.bo.ErrorScopeBo;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.hermes.enums.PrivilegeEnum;
import com.xiaomi.youpin.hermes.enums.ResourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/5/30
 */
@Slf4j
@DubboService(interfaceClass = DayuErrorService.class, group = "${provider.group}")
public class DayuErrorServiceImpl implements DayuErrorService {

    @Autowired
    private IProjectServiceRpc projectService;

    @Autowired
    private ErrorScopeServiceRpc errorScopeService;

    @Autowired
    private PolicyServiceRpc policyService;

    public ErrorScopeBo getScopeByAppName(String appName) {
        String logPrefix = "DayuErrorService getScopeByAppName ";
        checkArgument(StringUtils.isNotBlank(appName), logPrefix + "empty app name input");
        ProjectBo project = this.projectService.getProjectByName(appName);

        if (project == null || project.getId() == null || 0 == project.getId()) {
            log.info(logPrefix + "app name not found, {}", appName);
            return null;
        }
        List<String> sids = this.policyService.searchResources(
                Collections.singletonList(String.valueOf(project.getId())),
                ResourceTypeEnum.SCOPE, PrivilegeEnum.READ.getValue()
        );
        if (sids.isEmpty()) {
            return null;
        }
        List<ErrorScopeBo> details = this.errorScopeService.scopeDetails(
                sids.stream().map(Integer::valueOf).collect(Collectors.toList())
        );
        return details.isEmpty() ? null : details.get(0);
    }
}
