package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSONObject;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface NacosConfigExtendService {
    ConfigInfoExtend queryByConfigInfoId(Long id);
    List<ConfigInfoExtend> queryByConfigInfoIds(List<Long> ids);
    int insert(ConfigInfoExtend configInfoExtend);
    ResultResponse<Object> publishConfig(HttpServletRequest request, PublishConfigReq requestBody);

    ResultResponse<Object> publishConfig(PublishConfigReq requestBody);

    void extendDataById(JSONObject data, Long id);

    boolean deleteConfig(String relateInfo);

//    ResultResponse<Object> queryConfig(QueryConfigReq requestBody);
}
