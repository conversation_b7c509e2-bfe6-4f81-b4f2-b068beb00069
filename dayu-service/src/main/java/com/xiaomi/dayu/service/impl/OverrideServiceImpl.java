/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service.impl;

import com.xiaomi.dayu.common.ConfigTypeEnum;
import com.xiaomi.dayu.common.exception.ResourceNotFoundException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.ConvertUtil;
import com.xiaomi.dayu.common.util.OverrideUtils;
import com.xiaomi.dayu.common.util.YamlParser;
import com.xiaomi.dayu.model.adapter.DynamicConfigDTO2OverrideDTOAdapter;
import com.xiaomi.dayu.model.adapter.LoadBalance2OverrideAdapter;
import com.xiaomi.dayu.model.adapter.WeightToOverrideAdapter;
import com.xiaomi.dayu.model.domain.LoadBalance;
import com.xiaomi.dayu.model.domain.Override;
import com.xiaomi.dayu.model.domain.Weight;
import com.xiaomi.dayu.model.dto.*;
import com.xiaomi.dayu.model.store.OverrideConfig;
import com.xiaomi.dayu.model.store.OverrideDTO;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import com.xiaomi.dayu.service.OverrideService;
import com.xiaomi.dayu.service.SearchConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OverrideServiceImpl extends AbstractService implements OverrideService {
    private String prefix = Constants.CONFIG_KEY;

    @Autowired
    private SearchConfigService searchConfigService;
    @Autowired
    private ValidationPermissionService validationPermissionService;

    @java.lang.Override
    public void saveOverride(DynamicConfigDTO override,String type) {
        String id = ConvertUtil.getIdFromDTO(override);
        String path = getPath(id);
//        List<OverrideConfig> configs = new ArrayList<>();
//        OverrideDTO existOverride = new DynamicConfigDTO2OverrideDTOAdapter(override);
//        String exitConfig = dynamicConfiguration.getConfig(path);
        List<OverrideConfig> updateConfigs = override.getConfigs();
        /*if (exitConfig != null) {
            existOverride = YamlParser.loadObject(exitConfig, OverrideDTO.class);
            if (existOverride.getConfigs() != null) {
                List<OverrideConfig> nacosConfigs;
                if(type.equals("general")){
                    updateConfigs = override.getConfigs().stream().filter(config -> StringUtils.isBlank(config.getType()) || config.getType().equals(type)).collect(Collectors.toList());
                    nacosConfigs = existOverride.getConfigs().stream().filter(config -> StringUtils.isNotBlank(config.getType()) && !config.getType().equals(type)).collect(Collectors.toList());
                }else{
                    updateConfigs = override.getConfigs().stream().filter(config -> StringUtils.isNotBlank(config.getType()) && !config.getType().equals("general")).collect(Collectors.toList());
                    nacosConfigs =existOverride.getConfigs().stream().filter(config -> StringUtils.isBlank(config.getType()) || config.getType().equals("general")).collect(Collectors.toList());
                }
                override.getConfigs().clear();
                override.getConfigs().addAll(updateConfigs);
                override.getConfigs().addAll(nacosConfigs);
            }
        }*/
        for (OverrideConfig updateConfig : updateConfigs) {
            if(updateConfig.getParameters() == null){
                updateConfig.setParameters(new HashMap<>());
            }
            if(updateConfig.getParameters().size() == 0){
                updateConfig.getParameters().put("res","m");
            }
            updateConfig.setSide("provider");
            updateConfig.setEnabled(true);
        }
        override.setKey(override.getService());
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(override),override.getApplication());
    }

    @java.lang.Override
    public void updateOverride(DynamicConfigDTO override,String type) {
        override.setScope("service");
//        String id = ConvertUtil.getIdFromDTO(update);
        String path = getConfigPath(override);
        String exitConfig = dynamicConfiguration.getConfig(path);
        if (exitConfig == null) {
            throw new ResourceNotFoundException("Unknown ID!");
        }else{
            OverrideDTO existOverride = YamlParser.loadObject(exitConfig, OverrideDTO.class);
            if (existOverride.getConfigs() != null) {
                List<OverrideConfig> updateConfigs;
                List<OverrideConfig> nacosConfigs;
                if(type.equals("general")){
                    updateConfigs = override.getConfigs().stream().filter(config -> StringUtils.isBlank(config.getType()) || config.getType().equals(type)).collect(Collectors.toList());
                    nacosConfigs = existOverride.getConfigs().stream().filter(config -> StringUtils.isNotBlank(config.getType()) && !config.getType().equals(type)).collect(Collectors.toList());
                }else{
                    updateConfigs = override.getConfigs().stream().filter(config -> StringUtils.isNotBlank(config.getType()) && !config.getType().equals("general")).collect(Collectors.toList());
                    nacosConfigs =existOverride.getConfigs().stream().filter(config -> StringUtils.isBlank(config.getType()) || config.getType().equals("general")).collect(Collectors.toList());
                }
                override.getConfigs().clear();
                for (OverrideConfig updateConfig : updateConfigs) {
                    if(updateConfig.getParameters() == null){
                        updateConfig.setParameters(new HashMap<>());
                    }
                    if(updateConfig.getParameters().size() == 0){
                        updateConfig.getParameters().put("res","m");
                    }
                    updateConfig.setSide("provider");
                    updateConfig.setEnabled(true);
                }
                override.getConfigs().addAll(updateConfigs);
                override.getConfigs().addAll(nacosConfigs);

            }
        }
        override.setKey(override.getService());
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(override),override.getApplication());
    }

    public static void main(String[] args) {
        OverrideDTO overrideDTO = new OverrideDTO();
        overrideDTO.setConfigVersion("");
        overrideDTO.setService("service");
        overrideDTO.setConfigs(new ArrayList<>());
        System.err.println(YamlParser.dumpObject(overrideDTO));

    }
    private String getKey(BaseDTO baseDTO){
        if("application".equals(baseDTO.getScope())){
            return baseDTO.getApplication();
        }else{
            StringBuilder stringBuilder = new StringBuilder();
            if(StringUtils.isNotBlank(baseDTO.getServiceGroup())){
                stringBuilder.append(baseDTO.getServiceGroup()).append("/");
            }
            stringBuilder.append(baseDTO.getService());
            if(StringUtils.isNotBlank(baseDTO.getServiceVersion())){
                stringBuilder.append(":").append(baseDTO.getServiceVersion());
            }
            return stringBuilder.toString();
        }

    }


    @java.lang.Override
    public void deleteOverride(String id,String side,String type) {
        if (StringUtils.isEmpty(id)) {
            // throw exception
        }
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        if (config == null) {
            //throw exception
        }
        OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
        DynamicConfigDTO old = OverrideUtils.createFromOverride(overrideDTO);
        List<OverrideConfig> newConfigs = new ArrayList<>();
        if (overrideDTO.getConfigs() != null && overrideDTO.getConfigs().size() > 0) {
            for (OverrideConfig overrideConfig : overrideDTO.getConfigs()) {
                if (Constants.CONFIGS.contains(overrideConfig.getType())) {
                    newConfigs.add(overrideConfig);
                }

            }
            if (newConfigs.size() == 0) {
                dynamicConfiguration.deleteConfig(path);
            } else {
                overrideDTO.setConfigs(newConfigs);
                dynamicConfiguration.setConfig(path, YamlParser.dumpObject(overrideDTO),validationPermissionService.getAppNameById(overrideDTO.getScope(),id));
            }
        } else {
            dynamicConfiguration.deleteConfig(path);
        }

        //for 2.6
       /* if (Constants.SERVICE.equals(overrideDTO.getScope())) {
            List<Override> overrides = convertDTOtoOldOverride(old);
            for (Override o : overrides) {
                registry.unregister(o.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
            }
        }*/
    }

    @java.lang.Override
    public void enableOverride(String id,String side,String type) {
        if (StringUtils.isEmpty(id)) {
            //throw exception
        }
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        if (config == null) {
            //throw exception
        }
        OverrideDTO override = YamlParser.loadObject(config, OverrideDTO.class);
        DynamicConfigDTO old = OverrideUtils.createFromOverride(override,false);
        List<OverrideConfig> configs = old.getConfigs();
        if(CollectionUtils.isNotEmpty(configs)){
            for (OverrideConfig overrideConfig : configs) {
                if(side.equals(overrideConfig.getSide())){
                    if(type != null && overrideConfig.getType().equals(type)){
                        overrideConfig.setEnabled(true);
                    }
                }
            }
        }
        //override.setEnabled(true);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(override),validationPermissionService.getAppNameById(override.getScope(),id));

        //2.6
        /*if (Constants.SERVICE.equals(override.getScope())) {
            List<Override> overrides = convertDTOtoOldOverride(old);
            for (Override o : overrides) {
                o.setEnabled(false);
                registry.unregister(o.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
                o.setEnabled(true);
                registry.register(o.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
            }
        }*/
    }

    @java.lang.Override
    public void disableOverride(String id,String side,String type) {
        if (StringUtils.isEmpty(id)) {
            //throw exception
        }
        String path = getPath(id);
        if (dynamicConfiguration.getConfig(path) == null) {
            //throw exception
        }
        String config = dynamicConfiguration.getConfig(path);
        OverrideDTO override = YamlParser.loadObject(config, OverrideDTO.class);
        DynamicConfigDTO old = OverrideUtils.createFromOverride(override,false);
        List<OverrideConfig> configs = old.getConfigs();
        if(CollectionUtils.isNotEmpty(configs)){
            for (OverrideConfig overrideConfig : configs) {
                if(side.equals(overrideConfig.getSide())){
                    if(type != null && overrideConfig.getType().equals(type)){
                        overrideConfig.setEnabled(false);
                    }
                }
            }
        }
        //override.setEnabled(false);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(override),validationPermissionService.getAppNameById(override.getScope(),id));

        //for 2.6
        if (Constants.SERVICE.equals(override.getScope())) {
            List<Override> overrides = convertDTOtoOldOverride(old);
            for (Override o : overrides) {
                o.setEnabled(true);
                registry.unregister(o.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
                o.setEnabled(false);
                registry.register(o.toUrl().addParameter(Constants.COMPATIBLE_CONFIG, true));
            }
        }
    }

    @java.lang.Override
    public DynamicConfigDTO findOverride(String id) {
        if (StringUtils.isEmpty(id)) {
            //throw exception
        }
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            DynamicConfigDTO dynamicConfigDTO = OverrideUtils.createFromOverride(overrideDTO,false);
            if (dynamicConfigDTO != null) {
                dynamicConfigDTO.setId(id);
                if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                    ConvertUtil.detachIdToService(id, dynamicConfigDTO);
                }
            }
            return dynamicConfigDTO;
        }
        return null;
    }
    @java.lang.Override
    public DynamicConfigDTO findOverride(BaseDTO baseDTO) {
        return findOverride(baseDTO.getService(),baseDTO.getApplication(),baseDTO.getService(),baseDTO.getServiceGroup(),baseDTO.getServiceVersion());
    }
    @java.lang.Override
    public DynamicConfigDTO findOverride(String service, String appName, String service1, String group, String version) {
        String path = getConfigPath( service,  appName,  service1,  group,  version);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            try{
                OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
                DynamicConfigDTO dynamicConfigDTO = OverrideUtils.createFromOverride(overrideDTO,false);
                if (dynamicConfigDTO != null) {
                    if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                        //   ConvertUtil.detachIdToService(id, dynamicConfigDTO);
                    }
                }
                return dynamicConfigDTO;
            }catch (Exception e){
                log.error("findOverride,config={}",config,e );
            }
        }
        return null;
    }

    @java.lang.Override
    public void saveWeight(WeightDTO weightDTO) {
        String id = ConvertUtil.getIdFromDTO(weightDTO);
        String scope = ConvertUtil.getScopeFromDTO(weightDTO);
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        OverrideConfig overrideConfig = OverrideUtils.weightDTOtoConfig(weightDTO);
        OverrideDTO overrideDTO = insertConfig(config, overrideConfig, id, scope, Constants.WEIGHT);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(overrideDTO),validationPermissionService.getAppNameById(overrideDTO.getScope(),id));

        //for 2.6
        if (Constants.SERVICE.equals(scope)) {
            registerWeight(weightDTO);
        }

    }

    @java.lang.Override
    public void updateWeight(WeightDTO weightDTO) {
        String id = ConvertUtil.getIdFromDTO(weightDTO);
        String scope = ConvertUtil.getScopeFromDTO(weightDTO);
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        WeightDTO oldWeight = null;
        if (config != null) {
            OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            List<OverrideConfig> configs = overrideDTO.getConfigs();
            if (configs != null && configs.size() > 0) {
                for (OverrideConfig overrideConfig : configs) {
                    if (Constants.WEIGHT.equals(overrideConfig.getType())) {
                        if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                            oldWeight = OverrideUtils.configtoWeightDTO(overrideConfig, scope, id);
                        }
                        int index = configs.indexOf(overrideConfig);
                        OverrideConfig newConfig = OverrideUtils.weightDTOtoConfig(weightDTO);
                        configs.set(index, newConfig);
                        break;
                    }
                }
                dynamicConfiguration.setConfig(path, YamlParser.dumpObject(overrideDTO),validationPermissionService.getAppNameById(oldWeight.getScope(),id));
            } else {
                //throw exception
            }
        } else {
            //throw exception
        }


        //for 2.6
        if (oldWeight != null) {
            unregisterWeight(oldWeight);
            registerWeight(weightDTO);
        }

    }

    @java.lang.Override
    public void deleteWeight(String id) {
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        OverrideConfig oldConfig = null;
        if (config != null) {
            OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            List<OverrideConfig> configs = overrideDTO.getConfigs();
            if (configs != null) {
                for (OverrideConfig overrideConfig : configs) {
                    if (Constants.WEIGHT.equals(overrideConfig.getType())) {
                        if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                            oldConfig = overrideConfig;
                        }
                        configs.remove(overrideConfig);
                        break;
                    }
                }
                if (configs.size() == 0) {
                    dynamicConfiguration.deleteConfig(path);
                } else {
                    dynamicConfiguration.setConfig(path, YamlParser.dumpObject(overrideDTO),validationPermissionService.getAppNameById(overrideDTO.getScope(),id));
                }

            }

            //for 2.6
            if (oldConfig != null) {
                String key = overrideDTO.getKey();
                WeightDTO weightDTO = OverrideUtils.configtoWeightDTO(oldConfig, overrideDTO.getScope(), key);
                unregisterWeight(weightDTO);
            }
        }
    }

    @java.lang.Override
    public WeightDTO findWeight(String id) {
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            List<OverrideConfig> configs = overrideDTO.getConfigs();
            if (configs != null) {
                for (OverrideConfig overrideConfig : configs) {
                    if (Constants.WEIGHT.equals(overrideConfig.getType())) {
                        WeightDTO weightDTO = OverrideUtils.configtoWeightDTO(overrideConfig, overrideDTO.getScope(), id);
                        weightDTO.setId(id);
                        if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                            ConvertUtil.detachIdToService(id, weightDTO);
                        }
                        return weightDTO;
                    }
                }
            }
        }
        return null;
    }

    @java.lang.Override
    public void saveBalance(BalancingDTO balancingDTO) {
        String id = ConvertUtil.getIdFromDTO(balancingDTO);
        String scope = ConvertUtil.getScopeFromDTO(balancingDTO);
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        OverrideConfig overrideConfig = OverrideUtils.balancingDTOtoConfig(balancingDTO);
        OverrideDTO overrideDTO = insertConfig(config, overrideConfig, id, scope, Constants.BALANCING);
        dynamicConfiguration.setConfig(path, YamlParser.dumpObject(overrideDTO),validationPermissionService.getAppName(balancingDTO));

        //for 2.6

        if (Constants.SERVICE.equals(scope)) {
            registerBalancing(balancingDTO);
        }
    }


    @java.lang.Override
    public void updateBalance(BalancingDTO balancingDTO) {
        String id = ConvertUtil.getIdFromDTO(balancingDTO);
        String scope = ConvertUtil.getScopeFromDTO(balancingDTO);
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        BalancingDTO oldBalancing = null;
        if (config != null) {
            OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            List<OverrideConfig> configs = overrideDTO.getConfigs();
            if (configs != null && configs.size() > 0) {
                for (OverrideConfig overrideConfig : configs) {
                    if (Constants.BALANCING.equals(overrideConfig.getType())) {
                        if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                            oldBalancing = OverrideUtils.configtoBalancingDTO(overrideConfig, Constants.SERVICE, overrideDTO.getKey());
                        }
                        int index = configs.indexOf(overrideConfig);
                        OverrideConfig newConfig = OverrideUtils.balancingDTOtoConfig(balancingDTO);
                        configs.set(index, newConfig);
                        break;
                    }
                }
                dynamicConfiguration.setConfig(path, YamlParser.dumpObject(overrideDTO),validationPermissionService.getAppName(balancingDTO));
            } else {
                //throw exception
            }
        } else {
            //throw exception
        }

        //for 2.6
        if (oldBalancing != null) {
            unregisterBalancing(oldBalancing);
            registerBalancing(balancingDTO);
        }
    }

    @java.lang.Override
    public void deleteBalance(String id) {
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        OverrideConfig oldConfig = null;
        if (config != null) {
            OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            List<OverrideConfig> configs = overrideDTO.getConfigs();
            if (configs != null) {
                for (OverrideConfig overrideConfig : configs) {
                    if (Constants.BALANCING.equals(overrideConfig.getType())) {
                        if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                            oldConfig = overrideConfig;
                        }
                        configs.remove(overrideConfig);
                        break;
                    }
                }
                if (configs.size() == 0) {
                    dynamicConfiguration.deleteConfig(path);
                } else {
                    dynamicConfiguration.setConfig(path, YamlParser.dumpObject(overrideDTO),validationPermissionService.getAppNameById(overrideDTO.getScope(),id));
                }
            }
            //for 2.6
            if (oldConfig != null) {
                String key = overrideDTO.getKey();
                BalancingDTO balancingDTO = OverrideUtils.configtoBalancingDTO(oldConfig, Constants.SERVICE, key);
                unregisterBalancing(balancingDTO);
            }
        }
    }

    @java.lang.Override
    public BalancingDTO findBalance(String id) {
        String path = getPath(id);
        String config = dynamicConfiguration.getConfig(path);
        if (config != null) {
            OverrideDTO overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            List<OverrideConfig> configs = overrideDTO.getConfigs();
            if (configs != null) {
                for (OverrideConfig overrideConfig : configs) {
                    if (Constants.BALANCING.equals(overrideConfig.getType())) {
                        BalancingDTO balancingDTO = OverrideUtils.configtoBalancingDTO(overrideConfig, overrideDTO.getScope(), id);
                        balancingDTO.setId(id);
                        if (Constants.SERVICE.equals(overrideDTO.getScope())) {
                            ConvertUtil.detachIdToService(id, balancingDTO);
                        }
                        return balancingDTO;
                    }
                }
            }
        }
        return null;
    }

    @java.lang.Override
    public List<BalancingDTO> findAllBalancing() {
        List<ConfigInfo> configInfos = searchConfigService.searchConfigByType(ConfigTypeEnum.LoadBalance);
        if(CollectionUtils.isNotEmpty(configInfos)) {
            ArrayList<BalancingDTO> list = new ArrayList<>();
            for (ConfigInfo configInfo : configInfos) {
                OverrideDTO overrideDTO = YamlParser.loadObject(configInfo.getContent(), OverrideDTO.class);
                List<OverrideConfig> configs = overrideDTO.getConfigs();
                BaseVO baseVO = ConvertUtil.parseString2BaseVO(ConfigTypeEnum.LoadBalance, configInfo.getDataId());
                if (configs != null) {
                    for (OverrideConfig overrideConfig : configs) {
                        if (Constants.BALANCING.equals(overrideConfig.getType())) {
                            BalancingDTO balancingDTO = OverrideUtils.configtoBalancingDTO(overrideConfig, overrideDTO.getScope(),null );
                            ConvertUtil.assign(baseVO,balancingDTO);
                            list.add(balancingDTO);
                        }
                    }
                }
            }
            return list;
        }
        return null;
    }

    @java.lang.Override
    public List<DynamicConfigDTO> findAllOverrides() {
        return queryDynamicConfig(null);
    }
    @java.lang.Override
    public List<DynamicConfigDTO> queryDynamicConfig(String application) {
        List<ConfigInfo> configInfos = searchConfigService.searchConfigByTypeAndApp(ConfigTypeEnum.Overrides,application);
        if(CollectionUtils.isNotEmpty(configInfos)) {
            return configInfos.stream().map(configInfo -> {
                OverrideDTO overrideDTO = YamlParser.loadObject(configInfo.getContent(), OverrideDTO.class);
                DynamicConfigDTO dynamicConfigDTO = OverrideUtils.createFromOverride(overrideDTO,false);
                /*if (dynamicConfigDTO != null) {
                    BaseVO baseVO = ConvertUtil.parseString2BaseVO(ConfigTypeEnum.Overrides, overrideDTO);
                    ConvertUtil.assign(baseVO, dynamicConfigDTO);
                }*/
                return dynamicConfigDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @java.lang.Override
    public List<WeightDTO> findAllWeight() {
        return queryWeight(null);
    }
    @java.lang.Override
    public List<WeightDTO> queryWeight(String application) {
        List<ConfigInfo> configInfos = searchConfigService.searchConfigByTypeAndApp(ConfigTypeEnum.Overrides,application);
        if(CollectionUtils.isNotEmpty(configInfos)) {
            ArrayList<WeightDTO> weightDTOs = new ArrayList<>();
            for (ConfigInfo configInfo : configInfos) {
                OverrideDTO overrideDTO = YamlParser.loadObject(configInfo.getContent(), OverrideDTO.class);
                List<OverrideConfig> configs = overrideDTO.getConfigs();
                BaseVO baseVO = ConvertUtil.parseString2BaseVO(ConfigTypeEnum.Overrides, configInfo.getDataId());
                if (configs != null) {
                    for (OverrideConfig overrideConfig : configs) {
                        if (Constants.WEIGHT.equals(overrideConfig.getType())) {
                            WeightDTO weightDTO = OverrideUtils.configtoWeightDTO(overrideConfig, overrideDTO.getScope(), null);
                           // weightDTO.setId(id);
                            ConvertUtil.assign(baseVO,weightDTO);
                            weightDTOs.add(weightDTO);
                        }
                    }
                }
            }
            return weightDTOs;
        }
        return null;
    }

    private OverrideDTO insertConfig(String config, OverrideConfig overrideConfig, String key, String scope, String configType) {
        OverrideDTO overrideDTO = null;
        if(config == null) {
            overrideDTO = new OverrideDTO();
            overrideDTO.setKey(key);
            overrideDTO.setScope(scope);
            List<OverrideConfig> configs = new ArrayList<>();
            configs.add(overrideConfig);
            overrideDTO.setConfigs(configs);
        } else {
            overrideDTO = YamlParser.loadObject(config, OverrideDTO.class);
            List<OverrideConfig> configs = overrideDTO.getConfigs();
            if (configs != null) {
                for (OverrideConfig o : configs) {
                    if (configType.equals(o.getType())) {
                        configs.remove(o);
                        break;
                    }
                }
                configs.add(overrideConfig);
            } else {
                configs = new ArrayList<>();
                configs.add(overrideConfig);
            }
            overrideDTO.setConfigs(configs);
        }
        return overrideDTO;
    }

    private void overrideDTOToParams(Override override, OverrideConfig config) {
        Map<String, Object> parameters = config.getParameters();
        StringBuilder params = new StringBuilder();

        if (parameters != null) {
            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                String value = entry.getKey() + "=" + entry.getValue();
                params.append(value).append("&");
            }
        }
        if (StringUtils.isNotEmpty(params)) {
            int length = params.length();
            if (params.charAt(length - 1) == '&') {
                params.deleteCharAt(length - 1);
            }
        }
        override.setParams(params.toString());
    }
    private List<Override> convertDTOtoOldOverride(DynamicConfigDTO overrideDTO) {
        List<Override> result = new ArrayList<>();
        List<OverrideConfig> configs = overrideDTO.getConfigs();
        for (OverrideConfig config : configs) {
            if (Constants.CONFIGS.contains(config.getType())) {
                continue;
            }
            List<String> apps = config.getApplications();
            List<String> addresses = config.getAddresses();
            for (String address : addresses) {
                if (apps != null && apps.size() > 0) {
                    for (String app : apps) {
                        Override override = new Override();
                        override.setService(overrideDTO.getService());
                        override.setAddress(address);
                        override.setEnabled(overrideDTO.isEnabled());
                        overrideDTOToParams(override, config);
                        override.setApplication(app);
                        result.add(override);
                    }
                } else {
                    Override override = new Override();
                    override.setService(overrideDTO.getService());
                    override.setAddress(address);
                    override.setEnabled(overrideDTO.isEnabled());
                    overrideDTOToParams(override, config);
                    result.add(override);
                }
            }
        }
        return result;
    }
    private String getPath(String key) {
        key = key.replace("/", "*");
        return prefix + Constants.PATH_SEPARATOR + key + Constants.CONFIGURATOR_RULE_SUFFIX;
    }
    @java.lang.Override
    public String getConfigPath(BaseDTO baseDTO){
        return getConfigPath(baseDTO.getScope(),baseDTO.getApplication(), baseDTO.getService(),baseDTO.getServiceGroup(),baseDTO.getServiceVersion());
    }
    @java.lang.Override
    public String getConfigPath(String scope, String application, String service, String group, String version){
        if("application".equals(scope)){
            return prefix + Constants.PATH_SEPARATOR+application + Constants.CONFIGURATOR_RULE_SUFFIX;
        }else{
            StringBuilder stringBuilder = new StringBuilder(prefix + Constants.PATH_SEPARATOR).append(service);
            if(StringUtils.isNotBlank(version)){
                stringBuilder.append(":").append(version);
            }
            if(StringUtils.isNotBlank(group)){
                stringBuilder.append(":").append(group);
            }
            stringBuilder.append(Constants.CONFIGURATOR_RULE_SUFFIX);

            return  stringBuilder.toString();
        }

    }



    private void unregisterWeight(WeightDTO weightDTO) {
        List<String> addresses = weightDTO.getAddresses();
        if (addresses != null) {
            Weight weight = new Weight();
            weight.setService(weightDTO.getService());
            weight.setWeight(weightDTO.getWeight());
            for (String address : addresses) {
                weight.setAddress(address);
                Override override = new WeightToOverrideAdapter(weight);
                registry.unregister(override.toUrl());
            }
        }
    }

    private void registerWeight(WeightDTO weightDTO) {
        List<String> addresses = weightDTO.getAddresses();
        if (addresses != null) {
            Weight weight = new Weight();
            weight.setService(weightDTO.getService());
            weight.setWeight(weightDTO.getWeight());
            for (String address : addresses) {
                weight.setAddress(address);
                Override override = new WeightToOverrideAdapter(weight);
                registry.register(override.toUrl());
            }
        }
    }

    private void unregisterBalancing(BalancingDTO balancingDTO) {
        LoadBalance loadBalance = new LoadBalance();
        loadBalance.setService(balancingDTO.getService());
        loadBalance.setMethod(balancingDTO.getMethodName());
        loadBalance.setStrategy(balancingDTO.getStrategy());
        registry.unregister(new LoadBalance2OverrideAdapter(loadBalance).toUrl());
    }

    private void registerBalancing(BalancingDTO balancingDTO) {
        LoadBalance loadBalance = new LoadBalance();
        loadBalance.setService(balancingDTO.getService());
        loadBalance.setMethod(balancingDTO.getMethodName());
        loadBalance.setStrategy(balancingDTO.getStrategy());
        registry.register(new LoadBalance2OverrideAdapter(loadBalance).toUrl());
    }

}
