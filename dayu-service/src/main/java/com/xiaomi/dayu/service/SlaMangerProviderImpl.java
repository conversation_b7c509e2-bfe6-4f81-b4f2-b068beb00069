package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.bo.PageResult;
import com.xiaomi.dayu.api.bo.SlaManageDTO;
import com.xiaomi.dayu.api.bo.SlaManageRequest;
import com.xiaomi.dayu.api.service.SlaMangerProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@Slf4j
@DubboService(interfaceClass = SlaMangerProvider.class, group = "${provider.group}",version = "1.0")
public class SlaMangerProviderImpl implements SlaMangerProvider {
    @Resource
    private SlaManageService slaManageService;
    @Override
    public Result<PageResult<SlaManageDTO>> queryList(SlaManageRequest request){
        return Result.success(slaManageService.queryList(request));
    }
}
