package com.xiaomi.dayu.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.rpc.GwdashApiServiceRpc;
import com.xiaomi.youpin.gwdash.bo.ProjectRoleBo;
import com.xiaomi.youpin.hermes.bo.response.Account;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @since 2022/5/13
 */
@Slf4j
@Service
public class ScopeReviewerService {

    @NacosValue(value = "${scopeReviewers}", autoRefreshed = true)
    private List<String> scopeReviewers;

    @Autowired
    private GwdashApiServiceRpc gwdashApiServiceRpc;

    @Autowired
    private AccountServiceRpc accountServiceRpc;

    private static Cache<String, Set<String>> scopeReviewerCache = newCache();

    private static Cache<String, Set<String>> getScopeReviewerCache() {
        if (scopeReviewerCache != null) {
            return scopeReviewerCache;
        }
        return scopeReviewerCache = newCache();
    }

    private static synchronized Cache<String, Set<String>> newCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(60, TimeUnit.MINUTES)
                .expireAfterAccess(10, TimeUnit.MINUTES)
                .build();
    }

    public boolean checkReviewer(String username) {
        if (this.scopeReviewers == null || this.scopeReviewers.isEmpty()) {
            return false;
        }
        return this.scopeReviewers.contains(username);
    }

    public List<String> getScopeReviewers() {
        // prevent mutation
        return new ArrayList<>(this.scopeReviewers);
    }

    // mutable return, please do not modify the result!
    public Map<String, Set<String>> scopeReviewerAppNames(List<String> usernames) {
        Map<String, Set<String>> reviewerMap = getScopeReviewerCache().asMap();

        if (reviewerMap.isEmpty()) {
            log.info("scope reviewers cache is empty");
            this.syncScopeReviewers(usernames);
            return reviewerMap;
        }
        Set<String> toSyncNames = new HashSet<>();
        usernames.forEach(username -> {
            if (!reviewerMap.containsKey(username)) {
                toSyncNames.add(username);
            }
        });
        if (!toSyncNames.isEmpty()) {
            log.info("scope reviewers cache needs to update");
            this.syncScopeReviewers(new ArrayList<>(toSyncNames));
        }
        return reviewerMap;
    }

    private void syncScopeReviewers(List<String> usernames) {
        log.info("syncing scope reviewers cache of size " + usernames.size());
        List<Account> accounts = this.accountServiceRpc.queryUsersByUserNames(usernames);
        List<Integer> userIds = accounts.stream().map(account -> account.getId().intValue()).collect(Collectors.toList());
        List<ProjectRoleBo> projectRoles = this.gwdashApiServiceRpc.getProjectsByAccountIds(userIds);

        projectRoles.forEach(role -> {
            String username = role.getUserName();
            long projectId = role.getProjectId();
            Set<String> pidSet = new HashSet<>();

            if (StringUtils.isBlank(username)) {
                if (role.getAccountId() > 0) {
                    log.error("ScopeReviewerService find empty username for account id {}, project id {}",
                            role.getAccountId(), projectId);
                }
                return;
            }
            if (getScopeReviewerCache().asMap().containsKey(username)) {
                pidSet = Optional.ofNullable(getScopeReviewerCache().getIfPresent(username)).orElse(new HashSet<>());
            }
            pidSet.add(String.valueOf(projectId));
            getScopeReviewerCache().put(username, pidSet);
        });
    }
}
