package com.xiaomi.dayu.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xiaomi.dayu.service.InstrumentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkArgument;
import static com.xiaomi.dayu.common.HttpClient.ARTHAS_URL;
import static com.xiaomi.dayu.common.constants.ArthasConsts.LIST_AGENTS_API;

/**
 * <AUTHOR> (yang<PERSON><PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/4/8
 */
@Slf4j
@Service
public class ArthasServiceImpl implements InstrumentService {

    @Autowired
    private RestTemplate restTemplate;

    public List<Object> listAgents(String appName) {
        checkArgument(StringUtils.isNotBlank(appName), "ArthasServiceImpl listAgents empty appName");
        try {
            ResponseEntity<Object> resp = this.restTemplate.getForEntity(ARTHAS_URL + LIST_AGENTS_API + "?app=" + appName, Object.class);
            return this.parseListAgentsResp(resp.getBody());
        } catch (Exception e) {
            log.error("ArthasServiceImpl listAgents throws exception for appName " + appName, e);
        }
        return new ArrayList<>();
    }

    private List<Object> parseListAgentsResp(Object data) {
        List<Object> list = new ArrayList<>();

        if (data == null) {
            return list;
        }
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.convertValue(data, JsonNode.class);
        Iterator<Map.Entry<String, JsonNode>> it = root.fields();

        while (it.hasNext()) {
            Map.Entry<String, JsonNode> entry = it.next();
            JsonNode val = entry.getValue();
            ObjectNode obj = (ObjectNode) val;

            obj.put("id", entry.getKey());
            list.add(obj);
        }
        return list;
    }
}
