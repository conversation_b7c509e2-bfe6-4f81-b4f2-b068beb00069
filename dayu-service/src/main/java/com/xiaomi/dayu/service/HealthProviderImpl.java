package com.xiaomi.dayu.service;

import com.xiaomi.dayu.api.service.HealthProvider;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @Type HealthProviderImpl.java
 * @Desc
 * @date 2025/4/21 14:27
 */
@DubboService(interfaceClass = HealthProvider.class, group = "${provider.group}")
public class HealthProviderImpl implements HealthProvider {
	@Override
	public String testHealth() {
		return "success";
	}
}
