/*
 * Copyright 2020 XiaoMi.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at the following link.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.dayu.dao.TagRuleInfoMapper;
import com.xiaomi.dayu.mybatis.entity.TagRuleInfo;
import com.xiaomi.dayu.mybatis.entity.TagRuleInfoExample;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import swimlane.bo.SwimLaneGroupInfoBo;
import swimlane.bo.TagRuleInfoDTO;
import swimlane.service.TagRulesInfoService;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by dongzhenxing on 2023/3/17 8:27 PM
 */
@Slf4j
@DubboService(interfaceClass = TagRulesInfoService.class, group = "${rpc.tagrule.group}")
public class TagRulesInfoServiceImpl implements TagRulesInfoService {
    private static Cache<String,List<TagRuleInfoDTO>> localCache = CacheBuilder.newBuilder().expireAfterWrite(15, TimeUnit.SECONDS)
            .maximumSize(10).build();

    @Autowired
    private TagRuleInfoMapper tagRuleInfoMapper;


    @Override
    public Result<List<TagRuleInfoDTO>> getTagRuleInfoList() {
        try {
            List<TagRuleInfoDTO> tagRuleInfoDTOList = localCache.get("tagrule", () -> {
                TagRuleInfoExample example = new TagRuleInfoExample();
                example.createCriteria().andIdIsNotNull();
                List<TagRuleInfo> tagRuleInfoList = tagRuleInfoMapper.selectByExampleWithBLOBs(example);
                return tagRuleInfoList.stream().map(RouteServiceImpl::tagRuleTransfer2DTO).collect(Collectors.toList());
            });
            return Result.success(tagRuleInfoDTOList);
        } catch (Exception e) {
            log.error("TagRulesInfoServiceImpl getTagRuleInfoList failed,cause by:",e);
            return Result.fail(GeneralCodes.InternalError,"getTagRuleInfoList failed");
        }
    }
}
