package com.xiaomi.dayu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.api.bo.DataIdReq;
import com.xiaomi.dayu.api.bo.DataIdResp;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.constants.ApproveStatusEnum;
import com.xiaomi.dayu.common.constants.ApproveTypeEnum;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.DateUtils;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.common.util.TemplateUtils;
import com.xiaomi.dayu.dao.ApprovalMapper;
import com.xiaomi.dayu.dao.ConfigInfoMapper;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.model.approval.ApprovalVO;
import com.xiaomi.dayu.mybatis.entity.Approval;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend;
import com.xiaomi.dayu.mybatis.example.ApprovalExample;
import com.xiaomi.dayu.mybatis.example.ConfigInfoExample;
import com.xiaomi.dayu.service.ApplicationService;
import com.xiaomi.dayu.service.ApprovalService;
import com.xiaomi.dayu.service.FeishuService;
import com.xiaomi.dayu.service.NacosConfigExtendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
@Slf4j
@Service
public class ApprovalServiceImpl implements ApprovalService {
    @Autowired
    private ApprovalMapper approvalMapper;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private NacosConfigExtendService nacosConfigExtendService;
    @Autowired
    private ConfigInfoMapper configInfoMapper;
    @Autowired
    private FeishuService feishuService;

    private static final String APPROVAL_URL_PRO = "http://dayu.be.mi.com/approval/list";
    private static final String APPROVAL_URL_TEST = "http://dayu.test.mi.com/approval/list";

    @Value("${env.name}")
    private String envName;


    @Override
    public PageResult<ApprovalVO> queryByPage(String appName, Integer approveType, Integer status, Integer pageNum, Integer pageSize) {
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        ApprovalExample approvalExample = new ApprovalExample();
        ApprovalExample.Criteria criteria = approvalExample.createCriteria();
        if(StringUtils.isNotBlank(appName)){
            criteria.andAppNameEqualTo(appName);
        }else if(!userInfo.isAdmin()){
            List<String> applicationNames = applicationService.getApplicationNames(userInfo.getUserName());
            if(CollectionUtils.isEmpty(applicationNames)){
                return new PageResult<>(null, 0,pageSize,pageNum);
            }else{
                criteria.andAppNameIn(applicationNames);
            }
        }
        if(approveType != null){
            criteria.andApproveTypeEqualTo(approveType);
        }
        if(status != null){
            criteria.andStatusEqualTo(status);
        }

        approvalExample.setOrderByClause(Approval.Column.createTime.desc());

        PageRowBounds pageRowBounds = new PageRowBounds((pageNum-1)*pageSize,pageSize);
        Page<Approval> approvals = (Page)approvalMapper.selectByExampleWithRowbounds(approvalExample, pageRowBounds);
        List<ApprovalVO> collect = approvals.stream().map(approval -> {
            ApprovalVO approvalVO = new ApprovalVO();
            BeanUtils.copyProperties(approval, approvalVO);
            handler(approvalVO);
            return approvalVO;
        }).collect(Collectors.toList());
        return new PageResult<>(collect, (int)approvals.getTotal(),pageSize,pageNum);
    }

    private void handler(ApprovalVO approvalVO) {
        if(approvalVO.getApproveType() == ApproveTypeEnum.NACOS_CONFIG.getCode()){
            approvalVO.setKey(approvalVO.getRelateKey().split("#")[0]);
     /*       if(approvalVO.getOperateType() == Enums.OperateType.CREATE.getValue() || approvalVO.getOperateType()==Enums.OperateType.UPDATE.getValue()){
                if(StringUtils.isNotBlank(approvalVO.getNewData())){
                    PublishConfigReq publishConfigReq = JSON.parseObject(approvalVO.getNewData(), PublishConfigReq.class);
                    PublishConfigReq publishConfigReqNew = new PublishConfigReq();
                    publishConfigReqNew.setDataId(publishConfigReq.getDataId());
                    publishConfigReqNew.setGroup(publishConfigReq.getGroup());
                    publishConfigReqNew.setContent(publishConfigReq.getContent());
                    publishConfigReqNew.setConfigType(publishConfigReq.getConfigType());
                    publishConfigReqNew.setAppName(publishConfigReq.getAppName());
                    publishConfigReqNew.setEnvName(publishConfigReq.getEnvName());
                    approvalVO.setNewData(JSON.toJSONString(publishConfigReqNew));
                }
            }
            if(approvalVO.getOperateType() == Enums.OperateType.UPDATE.getValue() || approvalVO.getOperateType()==Enums.OperateType.DELETE.getValue()){
                if(StringUtils.isNotBlank(approvalVO.getOldData())){
                    PublishConfigReq publishConfigReqOld = JSON.parseObject(approvalVO.getOldData(), PublishConfigReq.class);
                    PublishConfigReq publishConfigReqOld = new PublishConfigReq();
                    publishConfigReqOld.setDataId(configInfo.getDataId());
                    publishConfigReqOld.setGroup(configInfo.getGroupId());
                    publishConfigReqOld.setContent(configInfo.getContent());
                    publishConfigReqOld.setType(configInfo.getType());
                    publishConfigReqOld.setAppName(configInfo.getAppName());
                    publishConfigReqOld.setEnvName(configInfo.getEnvName());
                    approvalVO.setOldData(JSON.toJSONString(publishConfigReqOld));
                }
            }*/
        }
    }

    @Override
    public boolean insert(Approval approval){
        int insert = approvalMapper.insert(approval);
        return insert > 0 ?  true :false;
    }
    @Override
    public boolean update(Approval approval, ApprovalExample example){
        int update = approvalMapper.updateByExample(approval,example);
        return update > 0 ?  true :false;
    }
    @Override
    public Approval selectByPrimaryKey(Integer id){
        return approvalMapper.selectByPrimaryKey(id);
    }

    @Override
    public Boolean updateStatus(Integer id, String appName, Integer status, String operateRemark) {
        Approval approvalDb = approvalMapper.selectByPrimaryKey(id);
        if(approvalDb == null){
            throw new ParamValidationException("当前审批项不存在");
        }
        if(approvalDb.getApproveType() != ApproveTypeEnum.NACOS_CONFIG.getCode()){
            throw new ParamValidationException("审批类型不正确");
        }
        String userName = UserInfoThreadLocal.getUserInfo().getUserName();
        boolean isAdmin = UserInfoThreadLocal.getUserInfo().isAdmin();

        if(approvalDb.getStatus() == ApproveStatusEnum.UN_APPROVE.getCode()){
            boolean result = false;
            if(status == ApproveStatusEnum.CANCEL.getCode() ){
                if(!userName.equals(approvalDb.getApplicant()) && !isAdmin){
                    throw new ServiceException("只能申请人可以取消审批流程");
                }
                result = true;
            }
            if(status == ApproveStatusEnum.PASS.getCode()|| status == ApproveStatusEnum.REFUSE.getCode()){
                List<String> approverList = Lists.list(approvalDb.getApprover().split(","));
                if(!approverList.contains(userName) && !isAdmin){
                    throw new ServiceException("当前操作人不是审批人");
                }
                if(status == ApproveStatusEnum.PASS.getCode()){
                    if(approvalDb.getApproveType() == ApproveTypeEnum.NACOS_CONFIG.getCode()){
                        if(Enums.OperateType.DELETE.getValue() == approvalDb.getOperateType()){
                            result = nacosConfigExtendService.deleteConfig(approvalDb.getRelateInfo());

                        }else{
                            ResultResponse<Object> response = nacosConfigExtendService.publishConfig(JSON.parseObject(approvalDb.getNewData(), PublishConfigReq.class));
                            String data = (String)response.getData();
                            result = Boolean.valueOf(data);
                        }

                    }else{
                        log.error("暂时只支持nacos配置走审批流程，id={}，ApproveType={}",id,approvalDb.getApproveType());
                        throw new ServiceException("暂时只支持nacos配置走审批流程，id="+id+"，ApproveType="+approvalDb.getApproveType());
                    }
                }else if(status == ApproveStatusEnum.REFUSE.getCode()){
                    result = true;
                }
            }
            if(result){
                Approval approvalUpdate = new Approval();
                approvalUpdate.setId(id);
                approvalUpdate.setStatus(status);
                approvalUpdate.setOperateRemark(operateRemark);
                approvalUpdate.setOperator(userName);
                approvalUpdate.setApproveTime(new Date());
                approvalUpdate.setUpdateTime(approvalUpdate.getApproveTime());
                approvalMapper.updateByPrimaryKeySelective(approvalUpdate);

                Approval approval = approvalMapper.selectByPrimaryKey(approvalDb.getId());
                String key="";
                if(approvalDb.getApproveType() == ApproveTypeEnum.NACOS_CONFIG.getCode()){
                    key = approval.getRelateKey().split("#")[0];
                }
                sendFeiShu(approval,key,"处理完成",approvalDb.getApprover());
            }else{
                log.error("updateStatus 更新状态失败，id={}，appName={}，status={}，operateRemark={}", id,  appName,  ApproveStatusEnum.getApproveStatus(status).getDesc(),  operateRemark);
                throw new ServiceException("当前审批操作失败，请联系研发人员。审批Id="+approvalDb.getId());
            }
        }else{
            throw new ServiceException("当前审批项状态为"+ ApproveStatusEnum.getApproveStatus(status).getDesc()+"不允许再变更状态");
        }
        return true;
    }

    @Override
    public List<DataIdResp> query(DataIdReq dataIdReq) {

        ApprovalExample approvalExample = new ApprovalExample();
        ApprovalExample.Criteria criteria = approvalExample.createCriteria();
        criteria.andRelateKeyEqualTo(dataIdReq.getDataId()+"#DEFAULT_GROUP#");
        criteria.andStatusEqualTo(ApproveStatusEnum.PASS.getCode());
        List<Approval> approvals = approvalMapper.selectByExample(approvalExample);
        if(CollectionUtils.isNotEmpty(approvals)){
            return approvals.stream().map(approval -> {
                DataIdResp dataIdResp = new DataIdResp();
                BeanUtils.copyProperties(approval,dataIdResp);
                JSONObject jsonObject = JSON.parseObject(approval.getNewData());
                dataIdResp.setContent(jsonObject.getString("content"));
                dataIdResp.setCreateTime(DateUtils.formatDateToString(approval.getCreateTime()));
                dataIdResp.setUpdateTime(DateUtils.formatDateToString(approval.getUpdateTime()));
                dataIdResp.setApproveTime(DateUtils.formatDateToString(approval.getApproveTime()));
                return dataIdResp;
            }).collect(Collectors.toList());
        }
        return null;
    }




    @Override
    public List<Approval> queryByTypeAndRelateKey(ApproveTypeEnum approveType, String relateKey) {
        ApprovalExample approvalExample = new ApprovalExample();
        ApprovalExample.Criteria criteria = approvalExample.createCriteria();
        criteria.andApproveTypeEqualTo(approveType.getCode());
        criteria.andRelateKeyEqualTo(relateKey);
        criteria.andStatusEqualTo(ApproveStatusEnum.UN_APPROVE.getCode());

        return approvalMapper.selectByExample(approvalExample);
    }

    @Override
    public void createApproval(PublishConfigReq publishConfigReq, Enums.OperateType operateType){
        //校验是否已存在审批流
        String relateKey = new StringBuilder(publishConfigReq.getDataId()).append("#")
                .append(publishConfigReq.getGroup()).append("#").append(publishConfigReq.getTenant()).toString();
        List<Approval> approvals = this.queryByTypeAndRelateKey(ApproveTypeEnum.NACOS_CONFIG, relateKey);
        if(CollectionUtils.isNotEmpty(approvals)){
            log.error("relateKey:{}已经存在审批流",relateKey);
            throw new ServiceException("当前配置dataId:"+publishConfigReq.getDataId()+"已经存在审批流，请先处理再申请");
        }
        Approval approval = new Approval();
        approval.setAppName(publishConfigReq.getAppName());
        approval.setApproveType(ApproveTypeEnum.NACOS_CONFIG.getCode());
        approval.setOperateType(operateType.getValue());
        approval.setRelateKey(relateKey);
        approval.setStatus(ApproveStatusEnum.UN_APPROVE.getCode());
        approval.setApplicant(UserInfoThreadLocal.getUserInfo().getUserName());
        approval.setApplyRemark(publishConfigReq.getApplyRemark());
        approval.setApprover(publishConfigReq.getApprover());
        approval.setOperator("");
        approval.setOperateRemark("");
        approval.setCreateTime(new Date());
        approval.setApproveTime(approval.getCreateTime());
        approval.setUpdateTime(approval.getCreateTime());
        approval.setDel(false);
        if(operateType == Enums.OperateType.CREATE || operateType == Enums.OperateType.UPDATE){
            approval.setNewData(JSON.toJSONString(publishConfigReq));
        }
        if(operateType == Enums.OperateType.UPDATE || operateType == Enums.OperateType.DELETE){
           // ConfigInfoExtend configInfoExtend = nacosConfigExtendService.queryByConfigInfoId(publishConfigReq.getId());
            ConfigInfoExample example = new ConfigInfoExample();
            ConfigInfoExample.Criteria criteria = example.createCriteria();
            criteria.andDataIdEqualTo(publishConfigReq.getDataId());
            criteria.andGroupIdEqualTo(publishConfigReq.getGroup());
            criteria.andTenantIdEqualTo(publishConfigReq.getTenant());

            List<ConfigInfo> configInfos = configInfoMapper.searchConfig(example);
//            ConfigInfo configInfo = configInfoMapper.selectByPrimaryKey(publishConfigReq.getId());
            if(CollectionUtils.isNotEmpty(configInfos)){
                ConfigInfo configInfo = configInfos.get(0);
                HashMap<String, String> map = new HashMap<>();
                map.put("dataId",configInfo.getDataId());
                map.put("group",configInfo.getGroupId());
                map.put("tenant",configInfo.getTenantId());
                map.put("appName",configInfo.getAppName());
                map.put("content",configInfo.getContent());
                map.put("type",configInfo.getType());

                ConfigInfoExtend configInfoExtend = nacosConfigExtendService.queryByConfigInfoId(publishConfigReq.getId());
                if(configInfoExtend != null){
                    map.put("envName",configInfoExtend.getEnvName());
                    map.put("configType",configInfoExtend.getConfigType()+"");
                }
                approval.setOldData(JSON.toJSONString(map));
            }
        }

        HashMap<String, String> map = new HashMap<>();
        map.put("dataId",publishConfigReq.getDataId());
        map.put("group",publishConfigReq.getGroup());
        map.put("tenant",publishConfigReq.getTenant());
        if(publishConfigReq.getId() !=null){
            map.put("id",publishConfigReq.getId()+"");
        }
        approval.setRelateInfo(JSON.toJSONString(map));

        approvalMapper.insert(approval);

        sendFeiShu(approval,publishConfigReq.getDataId(),"申请",approval.getApprover());
    }
    private void sendFeiShu(Approval approval,String key,String step,String sendUser){
        HashMap<String, String> params = new HashMap<>();
        params.put("step",step);
        params.put("id",approval.getId()+"");
        params.put("appName",approval.getAppName());
        params.put("key",key);
        params.put("approveType",ApproveTypeEnum.getApproveTypeEnum(approval.getApproveType()).getDesc());
        params.put("operateType", Enums.OperateType.getOperateType(approval.getOperateType()).getDesc());
        params.put("status", ApproveStatusEnum.getApproveStatus(approval.getStatus()).getDesc());
        params.put("applyRemark",approval.getApplyRemark());
        params.put("applicant",approval.getApplicant());
        params.put("approver",approval.getApprover());
        params.put("operator",approval.getOperator());
        params.put("url",envName.startsWith("pro")? APPROVAL_URL_PRO:APPROVAL_URL_TEST);
        String content = TemplateUtils.processTemplate(APPROVAL_CREATE_TEMPLATE, params);
        feishuService.sendCardByEmail(Lists.newArrayList(sendUser.split(",")),content);

    }
    private  static final String APPROVAL_CREATE_TEMPLATE= "{\"config\": {\"wide_screen_mode\": true},\n" +
            "\"header\": {\"title\": {\"tag\": \"plain_text\",\"content\": \"${approveType}${step}\"}},\n" +
            "\"elements\": [\n" +
            "\t{\"tag\": \"hr\"},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"应用名: ${appName}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"审批Id: ${id}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"key: ${key}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"操作类型: ${operateType}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"申请备注: ${applyRemark}\"}},\t\t\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"当前状态: ${status}\"}},\t\t\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"申请人: ${applicant}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"审批人: ${approver}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"操作人: ${operator}\"}},\n" +
            "\t{\"tag\": \"hr\"},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"lark_md\",\"content\": \"查看审批列表：<a>${url}</a>\"}}]}";
}
