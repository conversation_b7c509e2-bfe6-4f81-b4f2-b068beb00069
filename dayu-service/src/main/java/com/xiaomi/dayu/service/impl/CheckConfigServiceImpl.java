package com.xiaomi.dayu.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaomi.dayu.api.bo.PublishConfigReq;
import com.xiaomi.dayu.api.bo.QueryConfigReq;
import com.xiaomi.dayu.api.constants.Enums;
import com.xiaomi.dayu.common.HttpClient;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.PermissionDeniedException;
import com.xiaomi.dayu.common.util.Pair;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.dayu.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.google.common.base.Preconditions.checkArgument;
import static com.xiaomi.dayu.common.util.SerializeUtils.getLong;
import static com.xiaomi.dayu.common.util.SerializeUtils.getText;

/**
 * <AUTHOR> Yang (<EMAIL>)
 * @version 1.0
 * @since 2022/1/10
 */
@Slf4j
@Service
public class CheckConfigServiceImpl implements CheckConfigService {

    @Autowired
    private NacosConfigExtendService nacosConfigExtendService;

    @Autowired
    private UserService userService;

    @Autowired
    private AccountServiceRpc accountServiceRpc;
    @Autowired
    private SearchConfigService searchConfigService;
    @Autowired
    private ApplicationService applicationService;

    @Override
    public void checkConfigDefault(String tenant, String group, String dataId) {
        this.checkConfigDefault(tenant, group, dataId, UserInfoThreadLocal.getUserInfo().getUserName());
    }

    public void checkConfigDefault(String tenant, String group, String dataId, String userName) {
        Pair<JsonNode, Boolean> result = this.checkConfig(tenant, group, dataId, userName);

        if (!result.getValue()) {
            this.checkAppName(result.getKey(), Enums.OperateType.READ.getValue(), userName);
        }
    }

    @Override
    public void checkConfigByType(String tenant, String group, String dataId, int operateType) {
        this.checkConfigByType(tenant, group, dataId, operateType, UserInfoThreadLocal.getUserInfo().getUserName());
    }

    public void checkConfigByType(String tenant, String group, String dataId, int operateType, String userName) {
        Pair<JsonNode, Boolean> result = this.checkConfig(tenant, group, dataId, userName);

        if (!result.getValue()) {
            this.checkAppName(result.getKey(), operateType, userName);
        }
    }

    private Pair<JsonNode, Boolean> checkConfig(String tenant, String group, String dataId, String userName) {
        String logPrefix = this.getClass().getSimpleName() +
                String.format("checkConfig tenant %s, group %s, dataId %s: ", tenant, group, dataId);
        String nacosConfig = HttpClient.getNacosConfig(tenant, group, dataId);

        if (StringUtils.isBlank(nacosConfig)) {
            log.warn(logPrefix + "nacosConfig does not exist");
            throw new IllegalArgumentException("配置不存在");
        }
        JsonNode jsonNode;
        try {
            jsonNode = new ObjectMapper().readTree(nacosConfig);
        } catch (JsonProcessingException e) {
            log.error(logPrefix + "could not json parse nacosConfig");
            throw new IllegalStateException("nacos配置内容不正确");
        }
        if (this.userService.checkAdmin(userName)) {
            return new Pair<>(jsonNode, true);
        }
        String appName = getText(jsonNode, "appName").orElse("");

        if (StringUtils.isNotBlank(appName)) {
            if (!applicationService.getApplicationNames(userName).contains(appName)) {
                log.warn(logPrefix + "unauthorized to access appName " + appName);
                throw new PermissionDeniedException("用户没有权限访问该应用");
            }
            return new Pair<>(jsonNode, true);
        }
        return new Pair<>(jsonNode, false);
    }

    /**
     * 此函数用来校验全局配置权限，对于非全局配置，checkConfig就会返回校验结果；
     * */
    private boolean checkAppName(JsonNode jsonNode, int operateType, String userName) {
        if (operateType == Enums.OperateType.READ.getValue()) {
            return true;
        }
        Optional<Long> idOptional = getLong(jsonNode, "id");

        if (!idOptional.isPresent()) {
            log.error("could not json parse id");
            throw new IllegalStateException("应用归属appName为空且nacos配置内容不正确");
        }
        String logPrefix = this.getClass().getSimpleName() + String.format("checkAppName id %d: ", idOptional.get());
        ConfigInfoExtend extendObj = this.nacosConfigExtendService.queryByConfigInfoId(idOptional.get());

        if (extendObj == null || extendObj.getConfigType() != Enums.ConfigType.GLOBAL.getValue()) {
            log.warn(logPrefix + "appName is empty and configType is not global");
            throw new PermissionDeniedException("校验应用归属appName为空，用户需要完善配置");
        }
        if (operateType == Enums.OperateType.CREATE.getValue()) {
            // "理论上谁都可以" (大禹微服务平台共建 丁佩 1月5日 16:25:51)
            // so i don't restrict user role here for now
            return true;
        }
        String createUser = getText(jsonNode, "createUser").orElse("");

        if (StringUtils.isBlank(createUser) || !createUser.equals(userName)) {
            log.warn(logPrefix + "trying to modify a global config, operateType {}, user {}, createUser {}.",
                    operateType, userName, createUser);
            throw new PermissionDeniedException("用户没有权限更改别人创建的全局配置，请联系创建者" + createUser);
        }
        return true;
    }

    public boolean checkPublishConfig(PublishConfigReq publishConfigReq,Enums.ChannelType channelType) {
        int operateType = publishConfigReq.getOperateType();
        String appName = publishConfigReq.getAppName();
        Long id = publishConfigReq.getId();
        String envId = publishConfigReq.getEnvId();
        String envName = publishConfigReq.getEnvName();
        Integer configType = publishConfigReq.getConfigType();
        String type = publishConfigReq.getType();
        String tenant = publishConfigReq.getTenant();
        String group = publishConfigReq.getGroup();
        String dataId = publishConfigReq.getDataId();

        if (Enums.ChannelType.HTTP.equals(channelType)) {
            checkArgument(UserInfoThreadLocal.getUserInfo() != null, "publishConfig username is empty");
            publishConfigReq.setUsername(UserInfoThreadLocal.getUserInfo().getUserName());
        }
        String userName = publishConfigReq.getUsername();

        if (id != null || Enums.OperateType.UPDATE.getValue() == operateType) {
//            checkArgument(StringUtils.isBlank(appName), "归属应用不允许更新");
            checkArgument(StringUtils.isBlank(envId) && StringUtils.isBlank(envName),
                    "部署环境不允许更新");
            checkArgument(configType == null, "配置类型不允许更新，可以新建配置");

            if (operateType != Enums.OperateType.UPDATE.getValue()) {
                operateType = Enums.OperateType.UPDATE.getValue();
            }
            this.checkConfigByType(tenant, group, dataId, operateType, userName);
        } else {
            checkArgument(configType != null, "请传入配置类型参数");
            checkArgument(StringUtils.isNotBlank(type), "请传入配置格式参数");

            if (StringUtils.isNotBlank(HttpClient.getNacosConfig(tenant, group, dataId))) {
                throw new IllegalArgumentException("配置项已存在，请检查dataId是否重复");
            }
            if (Enums.ConfigType.GLOBAL.getValue() != configType) {
                checkArgument(StringUtils.isNotBlank(appName), "应用或业务配置类型缺失归属应用参数");

                if (!this.userService.checkAdmin(userName) && !applicationService.getApplicationNames(userName).contains(appName)) {
                    throw new PermissionDeniedException("用户没有权限访问该应用");
                }
            }
        }
        return true;
    }

    public void checkPublishConfig2(PublishConfigReq publishConfigReq, Enums.ChannelType channelType) {
        int operateType = publishConfigReq.getOperateType();
        String appName = publishConfigReq.getAppName();
        Long id = publishConfigReq.getId();
        String envId = publishConfigReq.getEnvId();
        String envName = publishConfigReq.getEnvName();
//        Integer configType = publishConfigReq.getConfigType();
        String type = publishConfigReq.getType();
        String tenant = publishConfigReq.getTenant();
        String group = publishConfigReq.getGroup();
        String dataId = publishConfigReq.getDataId();

        checkArgument(StringUtils.isNotBlank(dataId), "dataId不能为空");
        checkArgument(StringUtils.isNotBlank(group), "group不能为空");
        checkArgument(StringUtils.isNotBlank(appName), "appName不能为空");
//        checkArgument(configType != null, "请传入配置类型参数");
        checkArgument(StringUtils.isNotBlank(type), "请传入配置格式参数");

        if (Enums.ChannelType.HTTP.equals(channelType)) {
            checkArgument(UserInfoThreadLocal.getUserInfo() != null, "publishConfig username is empty");
            publishConfigReq.setUsername(UserInfoThreadLocal.getUserInfo().getUserName());
        }else if (Enums.ChannelType.DUBBO.equals(channelType)){
            checkArgument(StringUtils.isNotBlank(publishConfigReq.getUsername()), "用户名不能为空");
        }

        String userName = publishConfigReq.getUsername();

        checkPermission( userName, appName);

        ConfigInfo configInfo = searchConfigService.searchConfig(StringUtils.isBlank(tenant) ? "" : tenant, group, dataId);
        if(Enums.OperateType.CREATE.getValue() == operateType){
            checkArgument(id == null, "创建配置不能指定id");
            checkArgument(configInfo == null, "配置已经存在，不能再创建");
//            checkArgument(Enums.ConfigType.GLOBAL.getValue() != configType && StringUtils.isNotBlank(appName), "应用或业务配置类型缺失归属应用参数");

        }else if(Enums.OperateType.UPDATE.getValue() == operateType){
            checkArgument(configInfo != null, "配置不存在");
            checkArgument(appName.equals(configInfo.getAppName()), "归属应用不允许更新");
//            checkArgument(StringUtils.isBlank(envId) && StringUtils.isBlank(envName),"部署环境不允许更新");
        }
    }
    @Override
    public void checkPermission(String userName, String appName){
        if (!this.userService.checkAdmin(userName) && !applicationService.getApplicationNames(userName).contains(appName)) {
            throw new IllegalArgumentException(String.valueOf("没有权限访问"+appName+"应用下的配置"));
        }
    }

    @Override
    public void checkQueryConfig(QueryConfigReq requestBody, Enums.ChannelType dubbo) {
        String appName = requestBody.getAppName();
        String username = requestBody.getUsername();
        checkArgument(StringUtils.isNotBlank(appName), "appName不能为空");
        checkArgument(StringUtils.isNotBlank(username), "用户名不能为空");
        checkPermission( username, appName);
    }
}
