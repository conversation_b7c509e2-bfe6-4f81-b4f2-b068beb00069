package com.xiaomi.dayu.service;

import com.xiaomi.dayu.model.dto.ProjectEnvsDTO;
import com.xiaomi.dayu.model.dto.SwimLaneDTO;
import com.xiaomi.dayu.model.dto.SwimLaneGroupDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SwimLaneService {
    void createSwimLaneGroup(SwimLaneGroupDTO dto);

    void updateSwimLaneGroup(SwimLaneGroupDTO dto);

    void deleteSwimLaneGroup(int id);

    List<SwimLaneGroupDTO> listSwimLaneGroup();

    SwimLaneGroupDTO getSwimLaneGroup(int groupId);

    Set<String> loadProjectList();

    List<ProjectEnvsDTO> listProjectEnv(int groupId,String milineEnv);

    List<SwimLaneDTO> listSwimLane(int id);

    void createSwimLane(SwimLaneDTO dto);

    void updateSwimLane(SwimLaneDTO dto);

    void deleteSwimLane(int id);

    void enableSwimLane(int id,String opUsername);

    void disableSwimLane(int id);

    Map<String,Integer> getSwimLaneGroupIdByTag(List<String> tags);
}
