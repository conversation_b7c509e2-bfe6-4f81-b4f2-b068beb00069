package com.xiaomi.dayu.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.pojo.ListView;
import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.data.push.nacos.NacosNaming;
import com.xiaomi.dayu.common.util.MD5Utils;
import com.xiaomi.mone.current.threadpool.MoneThreadPoolExecutor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageConst;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Type RegistryServerSync2.java
 * @Desc
 * @date 2024/12/5 15:00
 */
@Slf4j
@Component
public class RegistryServerSync2 implements InitializingBean, DisposableBean {

	private volatile List<String> serviceNames = new ArrayList<>();

	private volatile Map<String, String> serviceNameAndMd5 = new ConcurrentHashMap<>();
	private static final int PAGINATION_SIZE = 100;
	private ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

	private Integer availableProcessors = Runtime.getRuntime().availableProcessors();
	@Resource
	private RocketMQTemplate rocketMQTemplate;
	@NacosValue(value = "${rocketmq.topic}")
	private String topic;
	@Autowired
	private NacosNaming nacosNaming;

	@Autowired
	private MoneThreadPoolExecutor moneThreadPoolExecutor;



	@NacosValue(value = "${pull.nacos.service:false}")
	private boolean pullNacosService;

	/**
	 * 在属性设置后执行的初始化方法
	 *
	 * @throws Exception 如果初始化过程中发生错误
	 */
	@Override
	public void afterPropertiesSet() throws Exception {
//		getAllSerivces();
//		getAllInstances();
		scheduledExecutorService.scheduleWithFixedDelay(() -> getAll(), 1, 2, TimeUnit.MINUTES);
	}

	@Override
	public void destroy() throws Exception {
		scheduledExecutorService.shutdown();
	}

	public void getAll(){
		if(!pullNacosService){
			log.info("不拉取实例列表");
			return;
		}
		getAllSerivces();
		syncAllInstances();
	}

	/**
	 * 获取所有服务列表并更新本地缓存
	 * <p>
	 * 该方法通过分页的方式从Nacos服务器拉取所有服务列表，并更新本地缓存的服务列表。
	 * 如果在拉取过程中出现异常，会记录错误日志。
	 */
	public void getAllSerivces() {
		try {
			log.info("开始拉取服务列表");
			List<String> tmpServiceNames = new ArrayList<>();
			int pageIndex = 1;
			int count =0;
			ListView<String> listView;
			Stopwatch stopwatch = Stopwatch.createStarted();
			do {
				listView = nacosNaming.getServicesOfServer(pageIndex, PAGINATION_SIZE);
				if (listView != null && CollectionUtils.isNotEmpty(listView.getData())) {
					count = listView.getCount();
					tmpServiceNames.addAll(listView.getData());
					if(listView.getData().size()<PAGINATION_SIZE || (count%PAGINATION_SIZE == 0 && count/PAGINATION_SIZE == pageIndex)){
						break;
					}
				}
				pageIndex++;
			} while (listView != null && CollectionUtils.isNotEmpty(listView.getData()) && pageIndex < 10000);
			stopwatch.stop();
			List<String> reomveFromLocalServiceNames = serviceNames.stream().filter(item -> !tmpServiceNames.contains(item)).collect(Collectors.toList());
			List<String> addServiceNames = tmpServiceNames.stream().filter(item -> !serviceNames.contains(item)).collect(Collectors.toList());
			log.info("结束拉取服务列表,耗时={},nacos现有数量={},nacos下线数量={},nacos上线数量={},serviceNames数量={},serviceNameAndMd5数量={}",
					stopwatch.elapsed(TimeUnit.MILLISECONDS),tmpServiceNames.size(), reomveFromLocalServiceNames.size(),addServiceNames.size(),serviceNames.size(),serviceNameAndMd5.size());
			serviceNames = tmpServiceNames;
			log.info("清理已经不存在的服务，服务列表大小reomveFromLocalServiceNames={}", reomveFromLocalServiceNames.size());
			serviceNameAndMd5.remove(reomveFromLocalServiceNames);
			long time = new Date().getTime();
			for (String diffServiceName : reomveFromLocalServiceNames) {
				sendToMq(new ArrayList<>(), diffServiceName, time);
			}
		} catch (Throwable e) {
			log.error("遍历拉取服务列表异常", e);
		}
	}


	/**
	 * 获取所有实例并处理服务列表的变化
	 * <p>
	 * 该方法首先清理已经不存在的服务，然后拉取所有服务的实例列表，并根据实例列表的变化发送消息到MQ。
	 *
	 * @throws Throwable 如果在遍历拉取实例列表时发生异常
	 */
	public void syncAllInstances() {
		try {
			long time = new Date().getTime();
			Stopwatch stopwatch = Stopwatch.createStarted();
			log.info("开始拉取所有实例列表");
			List<List<String>> partitions = Lists.partition(serviceNames, serviceNames.size() / availableProcessors*2);
			List<CompletableFuture> tasks = new ArrayList<>(partitions.size());
			for (List<String> partitionServiceNames : partitions) {
				CompletableFuture<Void> task = CompletableFuture.runAsync(
						() -> {
							for (String serviceName : partitionServiceNames) {
								try {
									log.info("拉取请求serviceName={}实例列表", serviceName);
									List<Instance> allInstances = nacosNaming.getAllInstances(serviceName);
									log.info("获取结果serviceName={}实例列表={}", serviceName, JSON.toJSONString(allInstances));
									if (CollectionUtils.isNotEmpty(allInstances)) {
										List<String> hostPortList = new ArrayList<>();
										for (Instance instance : allInstances) {
											checkAndFullInstanceId(instance);
											hostPortList.add(instance.getIp() + ":" + instance.getPort() + ":" + instance.isEnabled());
										}
										Collections.sort(hostPortList);
										String md5 = MD5Utils.getMD5(String.join(",", hostPortList).getBytes());
										if (!serviceNameAndMd5.containsKey(serviceName) || !md5.equals(serviceNameAndMd5.get(serviceName))) {
											serviceNameAndMd5.put(serviceName, md5);
											sendToMq(allInstances, serviceName, time);
//											log.info("实例列表不一致，发送rocketmq,serviceName={}", serviceName);
										}
									}else{
										serviceNameAndMd5.remove(serviceName);
										sendToMq(allInstances, serviceName, time);
									}
								} catch (Throwable e) {
									log.error("遍历拉取实例列表异常,serviceName={}", serviceName, e);
								}
							}
						},moneThreadPoolExecutor);
				tasks.add(task);
			}
			CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).get(5, TimeUnit.MINUTES);
			stopwatch.stop();
			log.info("拉取实例列表任务执行耗时："+stopwatch.elapsed(TimeUnit.MILLISECONDS) + "ms");
			log.info("结束拉取所有实例列表,耗时={},服务列表大小serviceNameAndMd5={}",stopwatch.elapsed(TimeUnit.MILLISECONDS), serviceNameAndMd5.size());
		} catch (Throwable e) {
			log.error("遍历拉取所有实例列表异常", e);
		}
	}

	private void checkAndFullInstanceId(Instance instance) {
		if(StringUtils.isEmpty(instance.getInstanceId()) || "null".equals(instance.getInstanceId())){
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(instance.getIp());
			stringBuilder.append("#").append(instance.getPort());
			stringBuilder.append("#").append(instance.getClusterName());
			stringBuilder.append("#").append("DEFAULT_GROUP@@").append(instance.getServiceName());
			instance.setInstanceId(stringBuilder.toString());
		}

	}

	private void sendToMq(List<Instance> instanceList, String serviceName, long time) {
		serviceName = serviceName.contains("DEFAULT_GROUP@@") ? serviceName : "DEFAULT_GROUP@@" + serviceName;
		List<InstanceBo> instanceBoList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(instanceList)) {
			for (Instance instance : instanceList) {
				instance.setServiceName(serviceName);
				InstanceBo instanceBo = new InstanceBo();
				BeanUtils.copyProperties(instance, instanceBo);
				instanceBo.setLastBeat(time);
				instanceBoList.add(instanceBo);
			}
		}
		Map<String, Object> params = new HashMap<>();
		params.put("cmd", "syncData");
		params.put("key", "com.alibaba.nacos.naming.iplist.ephemeral.public##" + serviceName);
		params.put("datum", new Instances(instanceBoList));
		Message<Map<String, Object>> message = MessageBuilder.withPayload(params)
				.setHeader(MessageConst.PROPERTY_KEYS, serviceName)
				.build();
		rocketMQTemplate.asyncSend(topic, message, new SendCallback() {
			@Override
			public void onSuccess(SendResult sendResult) {
//				log.info("发送rocketmq 成功，SendResult:{}", JSON.toJSONString(sendResult));
			}
			@Override
			public void onException(Throwable throwable) {
				log.error("发送rocketmq 失败，exception:", throwable);
			}
		});
	}

	class InstanceBo extends Instance {
		private long lastBeat = 0L;
		public InstanceBo() {
		}

		public long getLastBeat() {
			return lastBeat;
		}

		public void setLastBeat(long lastBeat) {
			this.lastBeat = lastBeat;
		}
	}

	@AllArgsConstructor
	class Instances {
		private List<InstanceBo> instanceList;

		public List<InstanceBo> getInstanceList() {
			return instanceList;
		}

		public void setInstanceList(List<InstanceBo> instanceList) {
			this.instanceList = instanceList;
		}
	}
}
