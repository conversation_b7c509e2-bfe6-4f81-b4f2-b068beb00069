package com.xiaomi.dayu.service.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.xiaomi.dayu.common.util.TemplateUtils;
import com.xiaomi.dayu.service.FeishuService;
import com.xiaomi.youpin.feishu.FeiShu;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;

@Service
public class FeishuServiceImpl implements FeishuService {
    private  static final String APPROVAL_CREATE_TEMPLATE= "{\"config\": {\"wide_screen_mode\": true},\n" +
            "\"header\": {\"title\": {\"tag\": \"plain_text\",\"content\": \"${approveType}&{current}\"}},\n" +
            "\"elements\": [\n" +
            "\t{\"tag\": \"hr\"},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"应用名: ${appName}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"审批类型: ${approveType}\"}},\t\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"操作类型: ${operateType}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"申请备注: ${applyRemark}\"}},\t\t\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"申请人: ${applicant}\"}},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"plain_text\",\"content\": \"审批人: ${approver}\"}},\n" +
            "\t{\"tag\": \"hr\"},\n" +
            "\t{\"tag\": \"div\",\"text\": {\"tag\": \"lark_md\",\"content\": \"查看审批列表：<a>${url}</a>\"}}]}";
    @NacosValue(value = "${feishu.id}")
    private String feishuId;
    @NacosValue(value = "${feishu.secret}")
    private String feishuSecret;

    public  FeiShu feishu = null;

    @PostConstruct
    public void init(){
        feishu = new FeiShu(feishuId,feishuSecret);
    }


    @Override
    public void sendCardByEmail(List<String> userNameList, String message){

        for (String userName : userNameList) {
            feishu.sendCardByEmail(userName +"@xiaomi.com", message);
        }
    }

    public static void main(String[] args) {
        FeishuServiceImpl feishuServiceImpl = new FeishuServiceImpl();
        HashMap<String, String> params = new HashMap<>();
        params.put("id","666");
        params.put("appName","dayu");
        params.put("key","config");
        params.put("approveType","nacos配置");
        params.put("operateType","更新");
        params.put("applyRemark","修改配置");
        params.put("applicant","liuchuankang");
        params.put("approver","kangting,wangyandong");
        params.put("url","http://dayu.test.mi.com/approval/list");
        String content = TemplateUtils.processTemplate(FeishuServiceImpl.APPROVAL_CREATE_TEMPLATE, params);
        boolean b = feishuServiceImpl.feishu.sendCardByEmail("liuchuankang" + "@xiaomi.com",content );
        System.err.println(b);
    }

}
