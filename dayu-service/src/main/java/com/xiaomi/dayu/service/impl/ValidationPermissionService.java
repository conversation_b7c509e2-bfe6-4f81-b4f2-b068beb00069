package com.xiaomi.dayu.service.impl;

import com.xiaomi.dayu.model.domain.Entity;
import com.xiaomi.dayu.service.NamingInstanceService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.AccessAuthorityException;
import com.xiaomi.dayu.common.exception.ExceptionEnum;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.dto.BaseDTO;
import com.xiaomi.dayu.service.ConsumerService;
import com.xiaomi.dayu.service.ProviderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.xiaomi.dayu.common.util.Constants.COLON;

@Component
public class ValidationPermissionService {
    @Autowired
    private ProviderService providerService;
    @Autowired
    private ConsumerService consumerService;

    @Autowired
    private NamingInstanceService namingInstanceService;
    public void checkPermission(BaseDTO baseDTO){
//        if(baseDTO instanceof AccessDTO || baseDTO instanceof ConditionRouteDTO){
//            checkPermission(Constants.CONSUMER_SIDE,baseDTO.getApplication(),baseDTO.getService(),baseDTO.getServiceGroup(),baseDTO.getServiceVersion());
//        }else{
          if(UserInfoThreadLocal.getUserInfo().isAdmin()){
             return ;
          }
          checkPermission(Constants.PROVIDER_SIDE,baseDTO.getScope(), baseDTO.getApplication(),baseDTO.getService(),baseDTO.getServiceGroup(),baseDTO.getServiceVersion());

//        }
    }
    public void checkPermission(String side,String application,String service,String group,String version){
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        if(!userInfo.isAdmin()){
            if(StringUtils.isNotEmpty(service)){
                this.checkServiceName(userInfo, service, group, version);
            }else{
                this.checkApplication(side, application,userInfo);
            }
        }
    }
    public void checkPermission(String side,String scope,String application,String service,String group,String version){
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        if(!userInfo.isAdmin()){
            if(scope.equals("service")){
                this.checkServiceName(userInfo, service, group, version);
            }else{
                this.checkApplication(side, application,userInfo);
            }
        }
    }

    private void checkApplication(String side, String application,UserInfo userInfo) {
        if(UserInfoThreadLocal.getUserInfo().isAdmin()){
            return ;
        }
        if(namingInstanceService.getIsQueryNaming4DB()){
            List<Entity> entities = namingInstanceService.queryByApplication(side, application);
            if (CollectionUtils.isEmpty(entities)){
                throw new AccessAuthorityException(ExceptionEnum.NO_EXISTS_APPLICATION);
            }
        }else{
            List<Provider> providerList = providerService.findAll();
            List<Provider> providerFilter = providerList.stream().filter(provider -> provider.getApplication().equalsIgnoreCase(application)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(providerFilter)){
                List<Consumer> consumerList = consumerService.findAll();
                List<Consumer> consumerFilter = consumerList.stream().filter(consumer -> consumer.getApplication().equalsIgnoreCase(application)).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(consumerFilter)){
                    throw new AccessAuthorityException(ExceptionEnum.NO_EXISTS_APPLICATION);
                }
            }
        }
//        if(Constants.PROVIDER_SIDE.equals(side)){
//            List<Provider> providerList = providerService.findAll();
//            List<Provider> providerFilter = providerList.stream().filter(provider -> provider.getApplication().equals(application)).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(providerFilter)){
//                throw new AccessAuthorityException(ExceptionEnum.NO_EXISTS_APPLICATION);
//            }
//        }else{
//            List<Consumer> consumerList = consumerService.findAll();
//            List<Consumer> consumerFilter = consumerList.stream().filter(consumer -> consumer.getApplication().equals(application)).collect(Collectors.toList());
//
//            if(CollectionUtils.isEmpty(consumerFilter)){
//                throw new AccessAuthorityException(ExceptionEnum.NO_EXISTS_APPLICATION);
//            }
//        }
        boolean anyMatch = userInfo.getApplicationNames().stream().anyMatch(app -> app.equalsIgnoreCase(application));
        if (!anyMatch){
            throw new AccessAuthorityException(ExceptionEnum.NO_PERMISSION_APPLICATION);
        }
    }

    public void checkServiceName(UserInfo userInfo,String serviceName,String group,String version){
        String serviceId = getServiceId(serviceName, group, version);
        checkByServiceId(userInfo,serviceId);
    }
    public void checkByServiceId(UserInfo userInfo,String serviceId){
        List<Entity> entities = namingInstanceService.queryByService(Constants.PROVIDER_SIDE, serviceId);
        //List<Provider> serviceList = providerService.findByService(serviceId);
        if(CollectionUtils.isEmpty(entities)){
            throw new AccessAuthorityException(ExceptionEnum.NO_EXISTS_SERVICE);
        }else{
            List<String> applicationNames = userInfo.getApplicationNames();
            for (Entity entity : entities) {
                Provider provider = (Provider)entity;
                boolean anyMatch = applicationNames.stream().anyMatch(app -> app.equalsIgnoreCase(provider.getApplication()));
                if(anyMatch){
                   return;
                }
            }
            throw new AccessAuthorityException(ExceptionEnum.NO_PERMISSION_APPLICATION);
        }
    }
    private String getServiceId(String serviceName,String group,String version){
        StringBuilder stringBuilder = new StringBuilder();
        if(StringUtils.isNotBlank(group)){
            stringBuilder.append(group).append(Constants.PATH_SEPARATOR);
        }
        stringBuilder.append(serviceName);
        if(StringUtils.isNotBlank(version)){
            stringBuilder.append(Constants.COLON).append(version);
        }
        return stringBuilder.toString();
    }
    public String getAppName(BaseDTO baseDTO){
        if(StringUtils.isNotEmpty(baseDTO.getApplication())){
            return baseDTO.getApplication();
        }else{
            String serviceId = getServiceId(baseDTO.getService(), baseDTO.getServiceGroup(), baseDTO.getServiceVersion());
            return getAppNameByServiceId(serviceId);
        }

    }

    public String getAppNameById(String scope,String id){
        if(Constants.APPLICATION.equals(scope)){
            return id;
        }else{
            String[] split = id.split(COLON);
            if(split.length==3){
                String serviceId = getServiceId(split[0], split[2], split[1]);
                return getAppNameByServiceId(serviceId);
            }
            throw new ServiceException("找不到service对应的applicationName");
        }
    }
    public String getAppNameByIdAndType(String scope,String id,String Type){
        if(Constants.APPLICATION.equals(scope)){
            return id;
        }else{
            String[] split = id.split(COLON);
            if(split.length==3){
                String serviceId = getServiceId(split[0], split[2], split[1]);
                return getAppNameByServiceId(serviceId);
            }
            throw new ServiceException("找不到service对应的applicationName");
        }
    }
    private String getAppNameByServiceId(String serviceId){
        List<Provider> serviceList = providerService.findByService(serviceId);
        if(CollectionUtils.isNotEmpty(serviceList)){
            List<String> applicationNames = UserInfoThreadLocal.getUserInfo().getApplicationNames();
            for (Provider provider : serviceList) {
                for (String applicationName : applicationNames) {
                    if(applicationName.equalsIgnoreCase(provider.getApplication())){
                        return applicationName;
                    }
                }
            }
        }
        throw new ServiceException("找不到service对应的applicationName");
    }
}
