package com.xiaomi.dayu.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.ConvertUtil;
import com.xiaomi.dayu.common.util.RedisUtil;
import com.xiaomi.dayu.dao.SwimLaneGroupMapper;
import com.xiaomi.dayu.dao.SwimLaneMapper;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.dto.*;
import com.xiaomi.dayu.mybatis.entity.SwimLane;
import com.xiaomi.dayu.mybatis.entity.SwimLaneExample;
import com.xiaomi.dayu.mybatis.entity.SwimLaneGroup;
import com.xiaomi.dayu.mybatis.entity.SwimLaneGroupExample;
import com.xiaomi.dayu.rpc.IProjectServiceRpc;
import com.xiaomi.dayu.rpc.OpenApiServiceRpc;
import com.xiaomi.dayu.service.ConfigParams;
import com.xiaomi.dayu.service.ProviderService;
import com.xiaomi.dayu.service.RouteService;
import com.xiaomi.dayu.service.SwimLaneService;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.gwdash.bo.openApi.ProjectEnvBo;
import com.xiaomi.youpin.gwdash.bo.openApi.ProjectEnvRequest;
import com.xiaomi.youpin.gwdash.service.OpenApiService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import swimlane.bo.AppEnvDTO;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SwimLaneServiceImpl implements SwimLaneService {

    @Autowired
    private SwimLaneGroupMapper swimLaneGroupMapper;
    @Autowired
    private SwimLaneMapper swimLaneMapper;

    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;

    @Autowired
    private RouteService routeService;

    @Autowired
    private ProviderService providerService;

//    @DubboReference(check = false, interfaceClass = OpenApiService.class, group = "${ref.gwdash.service.group}", version = "${ref.gwdash.service.version:}", timeout = 3000)
//    private OpenApiService openApiService;

    @Autowired
    private OpenApiServiceRpc openApiServiceRpc;

    @Autowired
    private RedisUtil redis;

    public static final Gson gson = new Gson();
    public static final String SWIM_LANE_GROUP_PREFIX = "group_";

    @Override
    public void createSwimLaneGroup(SwimLaneGroupDTO dto) {
        SwimLaneGroupExample example = new SwimLaneGroupExample();
        example.createCriteria().andNameEqualTo(dto.getName());
        if (!swimLaneGroupMapper.selectByExample(example).isEmpty()) {
            throw new ParamValidationException("该泳道名已存在");
        }
        SwimLaneGroup group = new SwimLaneGroup();
        BeanUtils.copyProperties(dto, group);
        Date date = new Date();
        Timestamp updateTime = new Timestamp(date.getTime());
        group.setDescp(dto.getDesc());
        group.setCreateTime(updateTime);
        group.setAppList(gson.toJson(dto.getAppList()));
        //目前默认只有标签类型泳道
        group.setType(Constants.SWIM_LANE_GROUP_TYPE_TAG);
        //泳道组唯一前缀
        group.setPrefixHeader(SWIM_LANE_GROUP_PREFIX + redis.increase(Constants.REDIS_SWIMLINE_GROUP_KEY));
        if (dto.getAppTopicToGroups() != null) {
            group.setAppTopicToGroups(gson.toJson(dto.getAppTopicToGroups()));
        }
        swimLaneGroupMapper.insert(group);
    }

    @Override
    public void updateSwimLaneGroup(SwimLaneGroupDTO dto) {

        SwimLaneGroup group = swimLaneGroupMapper.selectByPrimaryKey(dto.getId());
        if (Objects.isNull(group)) {
            throw new ParamValidationException("该泳道组不存在,id:" + dto.getId());
        }
        //校验应用列表
        dto.getAppList().forEach(newAppName -> {
            ProjectBo project = iProjectServiceRpc.getProjectByName(newAppName);
            if (Objects.isNull(project) || Objects.isNull(project.getId())) {
                throw new ParamValidationException("app dose not exist:" + newAppName);
            }
        });
        List<String> oldAppNames = gson.fromJson(group.getAppList(), new TypeToken<List<String>>() {
        }.getType());
        oldAppNames.forEach(oldAppName -> {
            if (!dto.getAppList().contains(oldAppName)) {
                //为去除某应用，应当校验下属泳道是否有引用
                List<SwimLane> swimLanes = swimLaneMapper.selectByExampleWithBLOBs(new SwimLaneExample());
                swimLanes.forEach(swimLane -> {
                    List<AppEnvDTO> appEnvDTOS = gson.fromJson(swimLane.getAppEnvJson(), new TypeToken<List<AppEnvDTO>>() {
                    }.getType());
                    appEnvDTOS.forEach(appEnvDTO -> {
                        if (oldAppName.equals(appEnvDTO.getAppName())) {
                            throw new ParamValidationException("泳道:" + swimLane.getName() + "引用了该应用，请先去除该泳道的引用");
                        }
                    });
                });
            }
        });

        if (!dto.getName().equals(group.getName())) {
            //改组名需要校验
            SwimLaneGroupExample example = new SwimLaneGroupExample();
            example.createCriteria().andNameEqualTo(dto.getName());
            if (!swimLaneGroupMapper.selectByExample(example).isEmpty()) {
                throw new ParamValidationException("该泳道组名已存在");
            }
            group.setName(dto.getName());
        }
        group.setAppList(gson.toJson(dto.getAppList()));
        group.setDescp(dto.getDesc());

        //app对应的mq的topic与consumer group 配置
        if (dto.getAppTopicToGroups() != null) {
            group.setAppTopicToGroups(gson.toJson(dto.getAppTopicToGroups()));
        }
        swimLaneGroupMapper.updateByPrimaryKey(group);
    }

    /**
     * 删除泳道组
     *
     * @param id
     */
    @Override
    @Transactional
    public void deleteSwimLaneGroup(int id) {
        SwimLaneExample example = new SwimLaneExample();
        example.createCriteria().andSwimLaneGroupIdEqualTo(id);
        List<SwimLane> swimLanes = swimLaneMapper.selectByExample(example);
        swimLanes.forEach(swimLane -> {
            this.deleteSwimLane(swimLane.getId());
        });
        swimLaneGroupMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<SwimLaneGroupDTO> listSwimLaneGroup() {
        List<SwimLaneGroupDTO> result = new ArrayList<>();
        SwimLaneGroupExample example = new SwimLaneGroupExample();
        example.createCriteria().andNameIsNotNull();
        List<SwimLaneGroup> groupList = swimLaneGroupMapper.selectByExample(example);
        groupList.forEach(group -> {
            SwimLaneGroupDTO dto = new SwimLaneGroupDTO();
            BeanUtils.copyProperties(group, dto);
            dto.setCreateTime(group.getCreateTime().toString());
            List<String> appNames = gson.fromJson(group.getAppList(), new TypeToken<List<String>>() {
            }.getType());
            dto.setAppList(appNames);
            result.add(dto);
        });
        return result;
    }

    @Override
    public SwimLaneGroupDTO getSwimLaneGroup(int groupId) {
        SwimLaneGroupDTO dto = new SwimLaneGroupDTO();
        SwimLaneGroup swimLaneGroup = swimLaneGroupMapper.selectByPrimaryKey(groupId);
        BeanUtils.copyProperties(swimLaneGroup, dto);
        dto.setCreateTime(swimLaneGroup.getCreateTime().toString());
        List<String> appNames = gson.fromJson(swimLaneGroup.getAppList(), new TypeToken<List<String>>() {
        }.getType());
        dto.setAppList(appNames);
        dto.setPrefix(swimLaneGroup.getPrefixHeader());
        dto.setDesc(swimLaneGroup.getDescp());
        return dto;
    }

    @Override
    public Set<String> loadProjectList() {
        return iProjectServiceRpc.getAllProjectName();
    }

    @Override
    public List<ProjectEnvsDTO> listProjectEnv(int groupId,String milineEnv) {
        log.info("SwimLaneService.listProjectEnv请求，groupId={},milineEnv={}",groupId,milineEnv);
        List<ProjectEnvsDTO> result = new ArrayList<>();
        SwimLaneGroup swimLaneGroup = swimLaneGroupMapper.selectByPrimaryKey(groupId);
        if (Objects.isNull(swimLaneGroup)) {
            throw new ParamValidationException("泳道组不存在");
        }
        List<String> appList = gson.fromJson(swimLaneGroup.getAppList(), new TypeToken<List<String>>() {
        }.getType());
        log.info("SwimLaneService.pipelineListByAppNames请求，appList={},milineEnv={}",JSON.toJSONString(appList),milineEnv);
        Map<String, List<ProjectEnvBo>> mapResult = openApiServiceRpc.pipelineListByAppNames(appList, milineEnv);
        log.info("SwimLaneService.pipelineListByAppNames响应，mapResult={}", JSON.toJSONString(mapResult));
        if(mapResult != null && MapUtils.isNotEmpty(mapResult)){
            mapResult.forEach((appName, projectEnvBos) -> {
                ProjectEnvsDTO dto = new ProjectEnvsDTO();
                List<EnvDTO> envs = new ArrayList<>();
                dto.setProjectName(appName);
                if (Objects.nonNull(projectEnvBos)) {
                    projectEnvBos.forEach(projectEnvBo -> {
                        EnvDTO envDTO = new EnvDTO((int) projectEnvBo.getId(), projectEnvBo.getName());
                        envs.add(envDTO);
                    });
                }
                dto.setEnvNames(envs);
                result.add(dto);
            });
        }
        return result;
    }

    @Override
    public List<SwimLaneDTO> listSwimLane(int id) {
        List<SwimLaneDTO> swimLaneDTOS = new ArrayList<>();

        SwimLaneExample example = new SwimLaneExample();
        example.createCriteria().andSwimLaneGroupIdEqualTo(id);
        List<SwimLane> swimLanes = swimLaneMapper.selectByExampleWithBLOBs(example);
        swimLanes.forEach(swimLane -> {
            SwimLaneDTO swimLaneDTO = new SwimLaneDTO();
            BeanUtils.copyProperties(swimLane, swimLaneDTO);
            swimLaneDTO.setAppEnvList(gson.fromJson(swimLane.getAppEnvJson(), new TypeToken<List<AppEnvDTO>>() {
            }.getType()));
            swimLaneDTO.setSwimLaneCondition(gson.fromJson(swimLane.getConditionJson(), new TypeToken<SwimLaneCondition>() {
            }.getType()));
            if (swimLane.getStatus()) {
                swimLaneDTO.setStatus(Constants.SWIM_LANE_ENABLE);
            } else {
                swimLaneDTO.setStatus(Constants.SWIM_LANE_DISABLE);
            }
            swimLaneDTOS.add(swimLaneDTO);
        });
        return swimLaneDTOS;
    }

    /**
     * 创建泳道步骤
     * 1、校验参数、泳道组
     * 2、状态默认关闭，不打标，启用时打标
     *
     * @param dto
     */
    @Override
    public void createSwimLane(SwimLaneDTO dto) {
        if (Objects.isNull(dto.getSwimLaneGroupId())) {
            throw new ParamValidationException("Unknow group ID");
        }
        SwimLaneGroup swimLaneGroup = swimLaneGroupMapper.selectByPrimaryKey(dto.getSwimLaneGroupId());
        if (Objects.isNull(swimLaneGroup)) {
            throw new ParamValidationException("泳道组不存在");
        }
        SwimLane swimLane = new SwimLane();
        BeanUtils.copyProperties(dto, swimLane);
        swimLane.setStatus(false);
        swimLane.setAppEnvJson(gson.toJson(dto.getAppEnvList()));
        dto.getSwimLaneCondition().getConditionPairs().forEach(conditionPair -> conditionPair.setParseExpr(ConvertUtil.adapt2RealParseExpr(conditionPair.getOriParseExpr())));
        swimLane.setConditionJson(gson.toJson(dto.getSwimLaneCondition()));
        swimLane.setFlowControlTag(swimLaneGroup.getPrefixHeader() + "_d-tag_" + redis.increase(Constants.REDIS_SWIMLINE_KEY));
        swimLaneMapper.insert(swimLane);
    }

    @Override
    public void updateSwimLane(SwimLaneDTO dto) {
        if (Objects.isNull(dto.getId())) {
            throw new ParamValidationException("Unknow lane ID");
        }
        SwimLane swimLane = swimLaneMapper.selectByPrimaryKey(dto.getId());
        if (Objects.isNull(swimLane)) {
            throw new ParamValidationException("泳道不存在");
        }
        if (swimLane.getStatus()) {
            throw new ParamValidationException("请先关闭该泳道再进行更新");
        }
        swimLane.setName(dto.getName());
        swimLane.setAppEnvJson(gson.toJson(dto.getAppEnvList()));
        dto.getSwimLaneCondition().getConditionPairs().forEach(conditionPair -> conditionPair.setParseExpr(ConvertUtil.adapt2RealParseExpr(conditionPair.getOriParseExpr())));
        // transfer expr
        swimLane.setConditionJson(gson.toJson(dto.getSwimLaneCondition()));
        swimLaneMapper.updateByPrimaryKeyWithBLOBs(swimLane);
    }

    /**
     * 禁用状态的泳道才可删除
     *
     * @param id
     */
    @Override
    public void deleteSwimLane(int id) {
        SwimLane swimLane = swimLaneMapper.selectByPrimaryKey(id);
        if (Objects.isNull(swimLane)) {
            throw new ParamValidationException("泳道不存在");
        }
        if (swimLane.getStatus()) {
            throw new ParamValidationException("请先关闭泳道再删除");
        }
        swimLaneMapper.deleteByPrimaryKey(id);
    }

    /**
     * 状态，启用泳道
     *
     * @param id
     */
    @Override
    public void enableSwimLane(int id, String opUsername) {

        SwimLane swimLane = swimLaneMapper.selectByPrimaryKey(id);
        if (Objects.isNull(swimLane)) {
            throw new ParamValidationException("泳道不存在");
        }
        if (swimLane.getStatus()) {
            throw new ParamValidationException("泳道已开启");
        }
        swimLane.setStatus(true);

        SwimLaneGroup swimLaneGroup = swimLaneGroupMapper.selectByPrimaryKey(swimLane.getSwimLaneGroupId());
        if (swimLaneGroup == null) {
            throw new ParamValidationException("泳道组不存在");
        }
        Map<String, String> appToTopicAndGroupMap = new HashMap<>(8);
        if (swimLaneGroup.getAppTopicToGroups() != null) {
            appToTopicAndGroupMap = gson.fromJson(swimLaneGroup.getAppTopicToGroups(), new TypeToken<Map<String, String>>() {
            }.getType());
        }
        List<AppEnvDTO> appEnvs = gson.fromJson(swimLane.getAppEnvJson(), new TypeToken<List<AppEnvDTO>>() {
        }.getType());

        //需要打标的dubbo服务实例列表<"Test0930",["127.0.0.1:20880"]>
        Map<String, List<String>> dubboAddressesTobeTag = new HashMap<>();

        //需要打标的 mq 实例列表<"mq-app",["127.0.0.1"]>
        Map<AppEnvDTO, List<String>> mqAddressesTobeTag = new HashMap<>();

        appEnvs.forEach(appEnvDTO -> {
            //mione中获取的环境实例ip列表 <"staging",[127.0.0.1]>
            log.info("envMachinesByAppName 请求,appEnvDTO.getAppName()={}",appEnvDTO.getAppName());
            Map<String, List<String>> mapResult = openApiServiceRpc.envMachinesByAppName(appEnvDTO.getAppName(),ConfigParams.getMilineEnv().getName());
            log.info("envMachinesByAppName 响应,mapResult={}",JSON.toJSONString(mapResult));

            Map<String, List<String>> envIpList = mapResult;
            envIpList = ConvertUtil.clearEmptyEnv(envIpList);

            //nacos上的providers列表 <127.0.O.1,obj>
            Map<String, List<Provider>> providersMap = new HashMap<>();
            List<Provider> providers = providerService.findByApplication(appEnvDTO.getAppName());
            providers.forEach(provider -> {
                if (provider != null && provider.getAddress() != null) {
                    String ip = provider.getAddress().substring(0, provider.getAddress().lastIndexOf(":"));
                    if (providersMap.get(ip) != null) {
                        providersMap.get(ip).add(provider);
                    } else {
                        providersMap.put(ip, Lists.newArrayList(provider));
                    }
                }
            });

            List<String> addrToBeAdd = new ArrayList<>();
            envIpList.getOrDefault(appEnvDTO.getEnvName(), new ArrayList<>()).forEach(mioneIP -> {
                if (providersMap.containsKey(mioneIP)) {
                    //说明是dubbo实例，添加到ip+port 到dubbo实例待打标列表
                    addrToBeAdd.addAll(providersMap.get(mioneIP).stream().map(Provider::getAddress).collect(Collectors.toSet()));
                }
            });
            if (!addrToBeAdd.isEmpty()) {
                dubboAddressesTobeTag.put(appEnvDTO.getAppName(), addrToBeAdd);
            }
            //mq app 若是mq应用，添加到 mq 实例待打标处
            if (appEnvDTO.isMqApp()) {
                mqAddressesTobeTag.put(appEnvDTO, envIpList.getOrDefault(appEnvDTO.getEnvName(), new ArrayList<>()));
            }
        });

        //dubbo 实例打标
        dubboAddressesTobeTag.forEach((appName, addresses) -> {
            TagInstanceDTO tagInstance = new TagInstanceDTO();
            tagInstance.setApplication(appName);
            tagInstance.setTagName(swimLane.getFlowControlTag());
            tagInstance.setAddresses(addresses);
            routeService.createTagForInstance(tagInstance, opUsername, Constants.SWIM_LANE_GROUP_TYPE_TAG);
        });

        //mq 实例打标
        Map<String, String> finalAppToTopicAndGroupMap = appToTopicAndGroupMap;
        mqAddressesTobeTag.forEach((appEnv, addresses) -> {
            TagInstanceDTO tagInstance = new TagInstanceDTO();
            tagInstance.setApplication(appEnv.getAppName());
            if (finalAppToTopicAndGroupMap.containsKey(appEnv.getAppName())) {
                tagInstance.setAppTopicToGroups(finalAppToTopicAndGroupMap.get(appEnv.getAppName()));
            }
            tagInstance.setTagName(swimLane.getFlowControlTag());
            tagInstance.setAddresses(addresses);
            tagInstance.setMqTopicList(appEnv.getMqConf().getTopicList());
            tagInstance.setEnable(appEnv.getMqConf().isEnable());
            routeService.createMqTagForInstance(tagInstance, opUsername, Constants.SWIM_LANE_GROUP_TYPE_TAG);
        });

        //更新状态，激活
        swimLaneMapper.updateByPrimaryKey(swimLane);
    }

    /**
     * 泳道，禁用
     *
     * @param id
     */
    @Override
    public void disableSwimLane(int id) {
        SwimLane swimLane = swimLaneMapper.selectByPrimaryKey(id);
        if (Objects.isNull(swimLane)) {
            throw new ParamValidationException("泳道不存在");
        }
        if (!swimLane.getStatus()) {
            throw new ParamValidationException("泳道已关闭");
        }
        swimLane.setStatus(false);

        List<AppEnvDTO> appEnvs = gson.fromJson(swimLane.getAppEnvJson(), new TypeToken<List<AppEnvDTO>>() {
        }.getType());

        //需要取消打标的实例列表<"Test0930",["127.0.0.1:20880"]>
        Map<String, List<String>> addressesTobeUnTag = new HashMap<>();

        //需要取消打标的 mq 实例列表<"mq-app",["127.0.0.1"]>
        Map<AppEnvDTO, List<String>> mqAddressesTobeUnTag = new HashMap<>();

        appEnvs.forEach(appEnvDTO -> {
            //mione中获取的环境实例ip列表
            Map<String, List<String>> envIpList = openApiServiceRpc.envMachinesByAppName(appEnvDTO.getAppName(), ConfigParams.getMilineEnv().getName());
            envIpList = ConvertUtil.clearEmptyEnv(envIpList);

            //nacos上的providers列表
            Map<String, List<Provider>> providersMap = new HashMap<>();
            List<Provider> providers = providerService.findByApplication(appEnvDTO.getAppName());
            providers.forEach(provider -> {
                String ip = provider.getAddress().substring(0, provider.getAddress().lastIndexOf(":"));
                if (providersMap.get(ip) != null) {
                    providersMap.get(ip).add(provider);
                } else {
                    providersMap.put(ip, Lists.newArrayList(provider));
                }
            });

            List<String> addrToBeRemove = new ArrayList<>();
            envIpList.getOrDefault(appEnvDTO.getEnvName(), new ArrayList<>()).forEach(mioneIP -> {
                if (providersMap.containsKey(mioneIP)) {
                    addrToBeRemove.addAll(providersMap.get(mioneIP).stream().map(Provider::getAddress).collect(Collectors.toSet()));
                }
            });
            if (!addrToBeRemove.isEmpty()) {
                addressesTobeUnTag.put(appEnvDTO.getAppName(), addrToBeRemove);
            }
            //mq app 若是mq应用，添加到 mq 实例待取消打标处
            if (appEnvDTO.isMqApp()) {
                mqAddressesTobeUnTag.put(appEnvDTO, envIpList.getOrDefault(appEnvDTO.getEnvName(), new ArrayList<>()));
            }
        });

        //取消打标
        addressesTobeUnTag.forEach((appName, addresses) -> {
            TagInstanceDTO tagInstance = new TagInstanceDTO();
            tagInstance.setApplication(appName);
            tagInstance.setTagName(swimLane.getFlowControlTag());
            tagInstance.setAddresses(addresses);
            routeService.removeTagForInstance(tagInstance, true);
        });

        //mq 实例取消打标
        mqAddressesTobeUnTag.forEach((appEnv, addresses) -> {
            TagInstanceDTO tagInstance = new TagInstanceDTO();
            tagInstance.setApplication(appEnv.getAppName());
            tagInstance.setTagName(swimLane.getFlowControlTag());
            tagInstance.setAddresses(addresses);
            try {
                routeService.removeMqTagForInstance(tagInstance);
            } catch (ParamValidationException e) {
                //mq泳道配置不存在，不影响整体泳道关闭
                log.warn("removeMqTagForInstance failed,cause by:", e);
            }
        });

        //更新状态,禁用
        swimLaneMapper.updateByPrimaryKey(swimLane);
    }

    @Override
    public Map<String, Integer> getSwimLaneGroupIdByTag(List<String> tags) {
        Map<String, Integer> result = new HashMap<>(tags.size());
        tags = tags.stream().filter(tag -> tag.startsWith("group_")).collect(Collectors.toList());
        List<String> condition = tags.stream().map(tag -> {
            String[] tagArr = tag.split("_", 3);
            return String.join("_", tagArr[0], tagArr[1]);
        }).collect(Collectors.toList());
        SwimLaneGroupExample example = new SwimLaneGroupExample();
        example.createCriteria().andPrefixHeaderIn(condition);
        List<SwimLaneGroup> groupList = swimLaneGroupMapper.selectByExample(example);
        groupList.forEach(swimLaneGroup -> result.putIfAbsent(swimLaneGroup.getPrefixHeader(), swimLaneGroup.getId()));
        return result;
    }
}
