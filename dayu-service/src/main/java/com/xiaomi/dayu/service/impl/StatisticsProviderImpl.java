package com.xiaomi.dayu.service.impl;

import com.xiaomi.dayu.api.bo.StatisticsInfo;
import com.xiaomi.dayu.api.service.StatisticsProvider;
import com.xiaomi.dayu.service.StatisticsService;
import com.xiaomi.youpin.infra.rpc.Result;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;


@DubboService(interfaceClass = StatisticsProvider.class, group = "${provider.statistics.group:}")
public class StatisticsProviderImpl implements StatisticsProvider {

    @Autowired
    private StatisticsService statisticsService;



    @Override
    public Result<StatisticsInfo> queryStatistics(Date date) {
        StatisticsInfo statisticsInfo = new StatisticsInfo();
        String format = DateFormatUtils.format(date != null ? date : new Date(), "yyyy-MM-dd");
        statisticsInfo.setProjectNum(statisticsService.queryProjectNums(format));
        statisticsInfo.setUserViewNum(statisticsService.queryUserView(format).size());
        return Result.success(statisticsInfo);
    }



}
