package com.xiaomi.dayu.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.api.bo.*;
import com.xiaomi.dayu.api.constants.SideEnum;
import com.xiaomi.dayu.api.service.DubboSearchService;
import com.xiaomi.dayu.dao.NamingInstanceMapper;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.mybatis.example.NamingInstanceExample;
import com.xiaomi.dayu.rpc.AccountServiceRpc;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@DubboService(interfaceClass = DubboSearchService.class, group = "${provider.group}")
public class DubboSearchServiceImpl implements DubboSearchService {
    @NacosValue(value = "${queryNamingPageFlag:false}",autoRefreshed = true)
    private Boolean queryNamingPageFlag;

    @Autowired
    private NamingInstanceMapper namingInstanceMapper;
    @Autowired
    private AccountServiceRpc accountServiceRpc;
    @Autowired
    private UserService userService;


    @Override
    public Result<PageResult<DubboServiceInfoRes>> searchService(DubboSearchReq request) {
        NamingInstanceExample example = new NamingInstanceExample();
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        if(request.getSide() == null){
            return Result.fail(GeneralCodes.InternalError, "code: 503, side不能为空");
        }
        criteria.andSideEqualTo(request.getSide().name());
        if(StringUtils.isNotBlank(request.getServiceName())){
            criteria.andServiceNameLike("%"+request.getServiceName()+"%");
        }
        if(StringUtils.isNotBlank(request.getApplication())){
            criteria.andApplicationEqualTo(request.getApplication());
        }
        List<DubboServiceInfoRes> result = null;
        long total =0;
        if(queryNamingPageFlag){
            PageRowBounds pageRowBounds = new PageRowBounds((request.getPageNo()-1)*request.getPageSize(),request.getPageSize());
            Page<DubboServiceInfoRes> dubboServiceInfoPage = (Page)namingInstanceMapper.selectServiceByExampleWithRowbounds(example, pageRowBounds);
            result = dubboServiceInfoPage.getResult();
            total = dubboServiceInfoPage.getTotal();
        }else{
            result = namingInstanceMapper.selectServiceByExampleWithNORowbounds(example);
            total = 6999;
        }
        PageResult<DubboServiceInfoRes> pageResult = new PageResult<>(result,total,request.getPageNo(),request.getPageSize());
        if(request.isIncludeInstance() && CollectionUtils.isNotEmpty(pageResult.getData())){
            Map<String, DubboServiceInfoRes> serviceInfoResMap = pageResult.getData().stream().collect(Collectors.toMap(DubboServiceInfoRes::getServiceName, Function.identity(), (v1, v2) -> v1));
            NamingInstanceExample exampleInstance = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteriaInstance = exampleInstance.createCriteria();
            criteriaInstance.andServiceNameIn(new ArrayList<>(serviceInfoResMap.keySet()));
            List<NamingInstance> namingInstances ;
            if(request.isIncludeMetadata()){
                namingInstances = namingInstanceMapper.selectByExample(exampleInstance);
            }else{
                namingInstances = namingInstanceMapper.selectByExampleNotWithBLOBs(exampleInstance);
            }
            namingInstances.forEach(namingInstance->{
                DubboServiceInfoRes dubboServiceInfoRes = serviceInfoResMap.get(namingInstance.getServiceName());
                if(dubboServiceInfoRes.getInstanceList() == null){
                    dubboServiceInfoRes.setInstanceList(new ArrayList<>());
                }
                DubboInstanceInfoRes dubboInstanceInfoRes = new DubboInstanceInfoRes();
                BeanUtils.copyProperties(namingInstance,dubboInstanceInfoRes);
                dubboServiceInfoRes.getInstanceList().add(dubboInstanceInfoRes);
            });

        }
        return Result.success(pageResult);
    }

    @Override
    public Result<Collection<DubboServiceAndAppInfoRes>> serviceInfo(DubboSearchReq request) {
        NamingInstanceExample example = new NamingInstanceExample();
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        if(request.getSide() != null){
            criteria.andSideEqualTo(request.getSide().name());
        }
        if(StringUtils.isNotBlank(request.getServiceName())){
            criteria.andServiceNameLike("%"+request.getServiceName()+"%");
        }else{
            return Result.fail(GeneralCodes.InternalError, "code: 503, serviceName不能为空");
        }
        if(StringUtils.isNotBlank(request.getApplication())){
            criteria.andApplicationEqualTo(request.getApplication());
        }
        List<NamingInstance> namingInstances = namingInstanceMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(namingInstances)){
            HashMap<String, DubboServiceAndAppInfoRes> map = new HashMap<>();
            for (NamingInstance namingInstance : namingInstances) {
                map.computeIfAbsent(namingInstance.getDubboService()+"_"+namingInstance.getSide(),service->{
                    DubboServiceAndAppInfoRes dubboServiceAndAppInfoRes = new DubboServiceAndAppInfoRes();
                    dubboServiceAndAppInfoRes.setDubboService(namingInstance.getDubboService());
                    dubboServiceAndAppInfoRes.setSide(namingInstance.getSide());
                    dubboServiceAndAppInfoRes.setApplication(namingInstance.getApplication());
                    List<UserInfoResult> userInfoResults = accountServiceRpc.queryUsersByAppName(namingInstance.getApplication());
                    if(CollectionUtils.isNotEmpty(userInfoResults)){
                        dubboServiceAndAppInfoRes.setDevelopers(userInfoResults.stream().map(userInfoResult -> userInfoResult.getUserName()).collect(Collectors.toList()));
                        String uid = userService.findUidByUserName(userInfoResults.get(0).getUserName());
                        dubboServiceAndAppInfoRes.setDepartment(userService.findDepartmentFullName(uid));
                    }
                    return dubboServiceAndAppInfoRes;
                });
            }
            return Result.success(map.values());
        }
        return null;
    }

    @Override
    public Result<PageResult<DubboServiceInfoRes>> searchServiceForHera(DubboSearchReq requestBo) {
        NamingInstanceExample example = new NamingInstanceExample();
        NamingInstanceExample.Criteria criteria = example.createCriteria();
        criteria.andSideEqualTo(requestBo.getSide().name());
        if(StringUtils.isNotBlank(requestBo.getFullService())){
            criteria.andFullServiceGreaterThan(requestBo.getFullService());
        }
        example.setOrderByClause("full_service");
        PageHelper.startPage(requestBo.getPageNo(),requestBo.getPageSize());
        Page<String> dubboServiceInfoPage = (Page)namingInstanceMapper.selectFullServiceForHera(example);
        PageResult<DubboServiceInfoRes> pageResult = new PageResult<>(null,dubboServiceInfoPage.getTotal(),requestBo.getPageNo(),requestBo.getPageSize());
        if(CollectionUtils.isNotEmpty(dubboServiceInfoPage.getResult())){
            List<String> fullServiceList = dubboServiceInfoPage.getResult();
            NamingInstanceExample example2 = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria2 = example2.createCriteria();
            criteria2.andSideEqualTo(requestBo.getSide().name());
            criteria2.andFullServiceIn(fullServiceList);
            List<DubboServiceInfoRes> data = namingInstanceMapper.selectDataForHera(example2);
            pageResult.setData(data);
        }
        return Result.success(pageResult);
    }

    @Override
    public Result<Map<String,Map<String,ServiceRelationDTO>>> queryServiceRelations(ServiceRelationReq relationReq) {
        if(relationReq.getProviderApplication().contains("\n")){
            relationReq.setProviderApplication(relationReq.getProviderApplication().replace("\n",""));
        }
        if(StringUtils.isBlank(relationReq.getProviderApplication()) && CollectionUtils.isEmpty(relationReq.getServiceList())){
            return Result.fail(GeneralCodes.InternalError, "code: 503, 应用名和服务列表不能同时为空");
        }
        List<NamingInstance> namingInstancesConsumer = null;
        List<String> providerList = null;
        if(CollectionUtils.isNotEmpty(relationReq.getServiceList())){
            providerList = relationReq.getServiceList();
            NamingInstanceExample example = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andSideEqualTo(SideEnum.consumer.name());
            criteria.andFullServiceIn(relationReq.getServiceList());
            criteria.andDelEqualTo(false);
            namingInstancesConsumer = namingInstanceMapper.selectByExample(example);
        }else{
            NamingInstanceExample example = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andSideEqualTo(SideEnum.provider.name());
            criteria.andApplicationEqualTo(relationReq.getProviderApplication());
            criteria.andDelEqualTo(false);
            List<NamingInstance> namingInstancesProvider = namingInstanceMapper.selectByExampleWithDistinctNORowbounds(example);
            if(CollectionUtils.isNotEmpty(namingInstancesProvider)){
                NamingInstanceExample example2 = new NamingInstanceExample();
                NamingInstanceExample.Criteria criteria2 = example2.createCriteria();
                criteria2.andSideEqualTo(SideEnum.consumer.name());
                providerList = namingInstancesProvider.stream().map(NamingInstance::getFullService).collect(Collectors.toList());
                criteria2.andFullServiceIn(providerList);
                criteria2.andDelEqualTo(false);
                namingInstancesConsumer = namingInstanceMapper.selectByExample(example2);
            }else{
                return Result.success(null);
            }
        }
        if(CollectionUtils.isNotEmpty(providerList)){
            //service--> app -->list
            HashMap<String,Map<String,ServiceRelationDTO>> dataMap = new HashMap<>();
            Map<String, List<NamingInstance>> fullServiceMap;
            if(CollectionUtils.isNotEmpty(namingInstancesConsumer)){
                fullServiceMap = namingInstancesConsumer.stream().collect(Collectors.groupingBy(NamingInstance::getFullService));
            }else {
                fullServiceMap = new HashMap<>();
            }
            for (String fullService : providerList) {
                dataMap.putIfAbsent(fullService,new HashMap<>());
                if(fullServiceMap.containsKey(fullService)){
                    for (NamingInstance namingInstance : fullServiceMap.get(fullService)) {
                        dataMap.get(fullService).putIfAbsent(namingInstance.getApplication()
                                ,ServiceRelationDTO.builder().service(namingInstance.getFullService()).consumerApp(namingInstance.getApplication()).consumerIps(new ArrayList<>())
                                        .developers(accountServiceRpc.queryUsersByAppNameList(namingInstance.getApplication())).build());
                        dataMap.get(namingInstance.getFullService()).get(namingInstance.getApplication()).getConsumerIps().add(namingInstance.getIp());
                    }
                }
            }
            return Result.success(dataMap);
        }
        return null;
    }
    @Override
    public Result<Map<String,Map<String,ServiceRelationDTO>>> queryConsumerRelations(ServiceRelationReq relationReq) {
        if(relationReq.getConsumerApplication().contains("\n")){
            relationReq.setConsumerApplication(relationReq.getConsumerApplication().replace("\n",""));
        }
        if(StringUtils.isBlank(relationReq.getConsumerApplication()) && CollectionUtils.isEmpty(relationReq.getServiceList())){
            return Result.fail(GeneralCodes.InternalError, "code: 503, 应用名和服务列表不能同时为空");
        }
        List<NamingInstance> namingInstancesProvider = null;
        List<String> consumerList = null;
        if(CollectionUtils.isNotEmpty(relationReq.getServiceList())){
            consumerList = relationReq.getServiceList();
            NamingInstanceExample example = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andSideEqualTo(SideEnum.consumer.name());
            criteria.andFullServiceIn(relationReq.getServiceList());
            criteria.andDelEqualTo(false);
            namingInstancesProvider = namingInstanceMapper.selectByExample(example);
        }else{
            NamingInstanceExample example = new NamingInstanceExample();
            NamingInstanceExample.Criteria criteria = example.createCriteria();
            criteria.andSideEqualTo(SideEnum.consumer.name());
            criteria.andApplicationEqualTo(relationReq.getConsumerApplication());
            criteria.andDelEqualTo(false);
            List<NamingInstance> namingInstancesConsumer = namingInstanceMapper.selectByExampleWithDistinctNORowbounds(example);
            if(CollectionUtils.isNotEmpty(namingInstancesConsumer)){
                NamingInstanceExample example2 = new NamingInstanceExample();
                NamingInstanceExample.Criteria criteria2 = example2.createCriteria();
                criteria2.andSideEqualTo(SideEnum.provider.name());
                consumerList = namingInstancesConsumer.stream().map(NamingInstance::getFullService).collect(Collectors.toList());
                criteria2.andFullServiceIn(consumerList);
                criteria2.andDelEqualTo(false);
                namingInstancesProvider = namingInstanceMapper.selectByExample(example2);
            }else{
                return Result.success(null);
            }
        }
        if(CollectionUtils.isNotEmpty(consumerList)){
            //service--> app -->list
            HashMap<String,Map<String,ServiceRelationDTO>> dataMap = new HashMap<>();
            Map<String, List<NamingInstance>> fullServiceMap;
            if(CollectionUtils.isNotEmpty(namingInstancesProvider)){
                fullServiceMap = namingInstancesProvider.stream().collect(Collectors.groupingBy(NamingInstance::getFullService));
            }else {
                fullServiceMap = new HashMap<>();
            }
            for (String fullService : consumerList) {
                dataMap.putIfAbsent(fullService,new HashMap<>());
                if(fullServiceMap.containsKey(fullService)){
                    for (NamingInstance namingInstance : fullServiceMap.get(fullService)) {
                        dataMap.get(fullService).putIfAbsent(namingInstance.getApplication()
                                ,ServiceRelationDTO.builder().service(namingInstance.getFullService()).consumerApp(namingInstance.getApplication()).consumerIps(new ArrayList<>())
                                        .developers(accountServiceRpc.queryUsersByAppNameList(namingInstance.getApplication())).build());
                        dataMap.get(namingInstance.getFullService()).get(namingInstance.getApplication()).getConsumerIps().add(namingInstance.getIp());
                    }
                }
            }
            return Result.success(dataMap);
        }
        return null;
    }
    @Override
    public Result<String> queryServiceRelationsMarkDown(ServiceRelationReq relationReq) {
        Result<Map<String, Map<String, ServiceRelationDTO>>> result = queryServiceRelations(relationReq);
        StringBuilder stringBuilder = new StringBuilder("| dubbo服务 | 消费端应用名 | 消费端实例 | 消费端开发人员 |\n");
        stringBuilder.append("|----------|------------|------------|------------|\n");
        if(result.getCode() ==0 ){
            Map<String, Map<String, ServiceRelationDTO>> dataMap = result.getData();
            if(MapUtils.isNotEmpty(dataMap)){
                dataMap.forEach((service,v1)->{
                    if(MapUtils.isNotEmpty(v1)){
                        v1.forEach((app,v2)->{
                            stringBuilder.append("| ").append(service);
                            stringBuilder.append("| ").append(app);
                            stringBuilder.append("| ").append(v2.getConsumerIps().toString());
                            stringBuilder.append("| ").append(v2.getDevelopers().toString());
                            stringBuilder.append("|\n");
                        });
                    }else{
                        stringBuilder.append("| ").append(service);
                        stringBuilder.append("| | | |\n");
                    }
                });
            }
        }
        return Result.success(stringBuilder.toString());
    }
}
