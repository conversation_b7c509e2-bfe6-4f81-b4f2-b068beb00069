package com.xiaomi.dayu.util;

import org.apache.rocketmq.spring.core.RocketMQTemplate;

/**
 * <AUTHOR>
 * @Type RocketMQTemplateUtil.java
 * @Desc
 * @date 2024/12/5 10:39
 */
public class RocketMQTemplateUtil {
	private volatile static RocketMQTemplate ROCKETMQTEMPLATE;
	private volatile static String TOPIC;
	public static void initInstance(String topic,RocketMQTemplate rocketMQTemplate){
		if(ROCKETMQTEMPLATE == null){
			synchronized (RocketMQTemplateUtil.class){
				if(ROCKETMQTEMPLATE == null){
					TOPIC =topic;
					ROCKETMQTEMPLATE =rocketMQTemplate;
				}
			}
		}
	}
	public static RocketMQTemplate getInstance(){
		return ROCKETMQTEMPLATE;
	}
	public static String getTopic(){
		return TOPIC;
	}
}
