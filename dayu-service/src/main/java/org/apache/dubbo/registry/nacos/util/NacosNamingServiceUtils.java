
package org.apache.dubbo.registry.nacos.util;

import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.utils.NamingUtils;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.logger.Logger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.apache.dubbo.common.utils.StringConstantFieldValuePredicate;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.registry.client.DefaultServiceInstance;
import org.apache.dubbo.registry.client.ServiceInstance;
import org.apache.dubbo.registry.nacos.NacosConnectionManager;
import org.apache.dubbo.registry.nacos.NacosNamingServiceWrapper;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class NacosNamingServiceUtils {
    private static final Logger logger = LoggerFactory.getLogger(NacosNamingServiceUtils.class);

    private static final String NACOS_GROUP_KEY = "nacos.group";

    private static final String NACOS_RETRY_KEY = "nacos.retry";

    private static final String NACOS_RETRY_WAIT_KEY = "nacos.retry-wait";

    private static final String NACOS_CHECK_KEY = "nacos.check";

    public NacosNamingServiceUtils() {
    }

    public static Instance toInstance(ServiceInstance serviceInstance) {
        Instance instance = new Instance();
        String instanceId = serviceInstance.getServiceName() + ":" + serviceInstance.getHost() + ":" + serviceInstance.getPort();

        instance.setInstanceId(instanceId);
        instance.setServiceName(serviceInstance.getServiceName());
        instance.setIp(serviceInstance.getHost());
        instance.setPort(serviceInstance.getPort());
        instance.setMetadata(serviceInstance.getMetadata());
        instance.setEnabled(serviceInstance.isEnabled());
        instance.setHealthy(serviceInstance.isHealthy());
        return instance;
    }

    public static ServiceInstance toServiceInstance(Instance instance) {
        DefaultServiceInstance serviceInstance = new DefaultServiceInstance(NamingUtils.getServiceName(instance.getServiceName()), instance.getIp(), instance.getPort(), null);
        serviceInstance.setMetadata(instance.getMetadata());
        serviceInstance.setEnabled(instance.isEnabled());
        serviceInstance.setHealthy(instance.isHealthy());
        return serviceInstance;
    }

    public static String getGroup(URL connectionURL) {
        return connectionURL.getParameter("nacos.group", "DEFAULT_GROUP");
    }

    public static NacosNamingServiceWrapper createNamingService(URL connectionURL) {
        boolean check = connectionURL.getParameter(NACOS_CHECK_KEY, true);
        int retryTimes = connectionURL.getPositiveParameter(NACOS_RETRY_KEY, 10);
        int sleepMsBetweenRetries = connectionURL.getPositiveParameter(NACOS_RETRY_WAIT_KEY, 10);
        NacosConnectionManager nacosConnectionManager =
                new NacosConnectionManager(connectionURL, check, retryTimes, sleepMsBetweenRetries);
        return new NacosNamingServiceWrapper(nacosConnectionManager, retryTimes, sleepMsBetweenRetries);
    }

    private static Properties buildNacosProperties(URL url) {
        Properties properties = new Properties();
        setServerAddr(url, properties);
        setProperties(url, properties);
        //TODO 添加登录信息
        setLoginInfo(url,properties);
        return properties;
    }

    private static void setLoginInfo(URL url, Properties properties) {
        if(StringUtils.isNotEmpty(url.getUsername())){
            properties.put(PropertyKeyConst.USERNAME,url.getUsername());
        }
        if(StringUtils.isNotEmpty(url.getPassword())){
            properties.put(PropertyKeyConst.PASSWORD,url.getPassword());

        }
    }

    private static void setServerAddr(URL url, Properties properties) {
        StringBuilder serverAddrBuilder = (new StringBuilder(url.getHost())).append(":").append(url.getPort());
        String backup = url.getParameter("backup");
        if (backup != null) {
            serverAddrBuilder.append(",").append(backup);
        }

        String serverAddr = serverAddrBuilder.toString();
        properties.put("serverAddr", serverAddr);
    }

    private static void setProperties(URL url, Properties properties) {
        putPropertyIfAbsent(url, properties, "com.alibaba.nacos.naming.log.filename");
        Map<String, String> parameters = url.getParameters(StringConstantFieldValuePredicate.of(PropertyKeyConst.class));
        properties.putAll(parameters);
        putPropertyIfAbsent(url, properties, "namingLoadCacheAtStart", "true");
    }

    private static void putPropertyIfAbsent(URL url, Properties properties, String propertyName) {
        String propertyValue = url.getParameter(propertyName);
        if (StringUtils.isNotEmpty(propertyValue)) {
            properties.setProperty(propertyName, propertyValue);
        }

    }

    private static void putPropertyIfAbsent(URL url, Properties properties, String propertyName, String defaultValue) {
        String propertyValue = url.getParameter(propertyName);
        if (StringUtils.isNotEmpty(propertyValue)) {
            properties.setProperty(propertyName, propertyValue);
        } else {
            properties.setProperty(propertyName, defaultValue);
        }

    }

    public static Map<String, String> getNacosPreservedParam(URL registryUrl) {
        Map<String, String> map = new HashMap();
        if (registryUrl.getParameter("preserved.register.source") != null) {
            map.put("preserved.register.source", registryUrl.getParameter("preserved.register.source"));
        }

        if (registryUrl.getParameter("preserved.heart.beat.timeout") != null) {
            map.put("preserved.heart.beat.timeout", registryUrl.getParameter("preserved.heart.beat.timeout"));
        }

        if (registryUrl.getParameter("preserved.ip.delete.timeout") != null) {
            map.put("preserved.ip.delete.timeout", registryUrl.getParameter("preserved.ip.delete.timeout"));
        }

        if (registryUrl.getParameter("preserved.heart.beat.interval") != null) {
            map.put("preserved.heart.beat.interval", registryUrl.getParameter("preserved.heart.beat.interval"));
        }

        if (registryUrl.getParameter("preserved.instance.id.generator") != null) {
            map.put("preserved.instance.id.generator", registryUrl.getParameter("preserved.instance.id.generator"));
        }

        return map;
    }
}
