package com.xiaomi.dayu.service.impl;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.service.RouteService;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;

@ActiveProfiles("test")
//@SpringBootTest(classes = DayuApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringJUnit4ClassRunner.class)
public class RouteServiceImplTest extends TestCase {
    @Autowired
    private RouteService routeService;
    @Test
    public void testFindAllConditionRoute() {
        setUser();
        System.err.println(JSON.toJSONString(routeService.findAllConditionRoute()));
        System.err.println(JSON.toJSONString(routeService.findAllAccess()));
        System.err.println(JSON.toJSONString(routeService.findAllTagRoute()));
    }
    public void testFindAllAccess() {
        JSON.toJSONString(routeService.findAllAccess());
    }
    @Test
    public void testFindAllTagRoute() {
        JSON.toJSONString(routeService.findAllTagRoute());
    }
    public void setUser(){
        UserInfo userInfovo = new UserInfo();
        userInfovo.setUserName("liuchuankang");
        userInfovo.setAdmin(false);
        ArrayList<String> arrayList = new ArrayList<>();
        arrayList.add("dubbo-providertest");
        arrayList.add("dubbo-consumer");
        arrayList.add("dayu");
        arrayList.add("spring-boot-dubbo-demo");
        userInfovo.setApplicationNames(arrayList);
        UserInfoThreadLocal.setUserInfo(userInfovo);
    }
}