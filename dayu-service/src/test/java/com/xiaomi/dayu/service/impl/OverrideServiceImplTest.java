package com.xiaomi.dayu.service.impl;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.service.OverrideService;
import junit.framework.TestCase;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;

@Ignore
@ActiveProfiles("test")
//@SpringBootTest(classes = DayuApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringJUnit4ClassRunner.class)
public class OverrideServiceImplTest extends TestCase {
    @Autowired
    private OverrideService overrideService;

    public void setUser(){
        UserInfo userInfovo = new UserInfo();
        userInfovo.setUserName("liuchuankang");
        userInfovo.setAdmin(false);
        ArrayList<String> arrayList = new ArrayList<>();
        arrayList.add("dubbo-providertest");
        arrayList.add("dubbo-consumer");
        arrayList.add("dayu");
        arrayList.add("spring-boot-dubbo-demo");
        userInfovo.setApplicationNames(arrayList);
        UserInfoThreadLocal.setUserInfo(userInfovo);
    }
    @Test
    public void testFindAllBalance() {
        setUser();
        System.err.println(JSON.toJSONString(overrideService.findAllOverrides()));
        System.err.println(JSON.toJSONString(overrideService.findAllBalancing()));
        System.err.println(JSON.toJSONString(overrideService.findAllWeight()));
    }
    @Test
    public void testFindAllBalancing() {
    }
    @Test
    public void testFindAllOverrides() {
    }
    @Test
    public void testFindAllWeight() {
    }
}