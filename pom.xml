<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.xiaomi</groupId>
	<artifactId>dayu</artifactId>
	<version>${revision}</version>
    <modules>
        <module>dayu-common</module>
        <module>dayu-dao</module>
        <module>dayu-rpc</module>
        <module>dayu-service</module>
        <module>dayu-server</module>
		<module>dayu-api</module>
	</modules>
	<packaging>pom</packaging>

	<name>dayu</name>

<!--	<parent>
		<groupId>com.xiaomi.mit</groupId>
		<artifactId>parent-pom</artifactId>
		<version>1.0.2-SNAPSHOT</version>
	</parent>-->



	<properties>
		<spring-boot.version>2.3.12.RELEASE</spring-boot.version>
		<mitlibs.version>1.0.18</mitlibs.version>
		<revision>0.3.2-SNAPSHOT</revision>
		<main.basedir>${project.basedir}</main.basedir>
<!--		<dubbo-version>2.7.12-mone-v23-SNAPSHOT</dubbo-version>-->
		<dubbo-version>3.3.4-mone-v1-SNAPSHOT</dubbo-version>
		<curator-version>2.12.0</curator-version>
		<curator-test-version>4.1.0</curator-test-version>
		<fastjson-version>1.2.83</fastjson-version>
		<jacoco-version>0.8.2</jacoco-version>
		<jedis-version>2.9.0</jedis-version>
		<apollo-version>1.2.0</apollo-version>
		<consul-version>1.4.2</consul-version>
		<consul-embedded-version>2.0.0</consul-embedded-version>
<!--
		<nacos-version>1.4.2</nacos-version>
-->
		<nacos-version>2.1.2-XIAOMI</nacos-version>
		<mone-threadpool-version>2.0.0-mone-SNAPSHOT</mone-threadpool-version>
		<dubbo-registry-nacos>1.2.1-mone-v1-SNAPSHOT</dubbo-registry-nacos>
<!--		<snakeyaml-version>1.24</snakeyaml-version>-->
		<snakeyaml-version>2.4</snakeyaml-version>
		<maven-checkstyle-plugin-version>3.0.0</maven-checkstyle-plugin-version>
		<maven_compiler_version>3.6.0</maven_compiler_version>
		<maven-flatten-version>1.1.0</maven-flatten-version>

		<spring-fox.version>2.9.2</spring-fox.version>
		<springfox-swagger-ui.version>2.9.2</springfox-swagger-ui.version>
		<springfox-swagger2.version>2.9.2</springfox-swagger2.version>
		<springfox-swagger-version>2.9.2</springfox-swagger-version>

		<java_source_version>1.8</java_source_version>
		<java_target_version>1.8</java_target_version>
		<file_encoding>UTF-8</file_encoding>
		<checkstyle.skip>true</checkstyle.skip>
		<rat.skip>true</rat.skip>
		<mockito-version>3.8.0</mockito-version>
		<mybatis-generator.version>1.3.5</mybatis-generator.version>

	</properties>

	<dependencyManagement>

        <dependencies>
			<dependency>
				<groupId>org.yaml</groupId>
				<artifactId>snakeyaml</artifactId>
				<version>${snakeyaml-version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-parent</artifactId>
				<version>${spring-boot.version}</version>
				<scope>import</scope>
				<type>pom</type>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis-version}</version>
			</dependency>
			<dependency>
				<groupId>com.ctrip.framework.apollo</groupId>
				<artifactId>apollo-openapi</artifactId>
				<version>${apollo-version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-client</artifactId>
				<version>${nacos-version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo-version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-metrics-api</artifactId>
				<version>${dubbo-version}</version>
			</dependency>


			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-serialization-kryo</artifactId>
				<version>${dubbo-version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-framework</artifactId>
				<version>${curator-version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-test</artifactId>
				<version>${curator-test-version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-recipes</artifactId>
				<version>${curator-version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.apache.zookeeper</groupId>
						<artifactId>zookeeper</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson-version}</version>
			</dependency>

			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${springfox-swagger2.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${springfox-swagger-ui.version}</version>
			</dependency>

			<dependency>
				<groupId>com.ecwid.consul</groupId>
				<artifactId>consul-api</artifactId>
				<version>${consul-version}</version>
			</dependency>
			<dependency>
				<groupId>com.pszymczyk.consul</groupId>
				<artifactId>embedded-consul</artifactId>
				<version>${consul-embedded-version}</version>
			</dependency>

			<!-- 用于兼容Junit4和junit3 -->
			<dependency>
				<groupId>org.junit.vintage</groupId>
				<artifactId>junit-vintage-engine</artifactId>
				<version>5.8.2</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-core</artifactId>
				<version>${mybatis-generator.version}</version>
			</dependency>

			<dependency>
				<groupId>com.xiaomi.mone</groupId>
				<artifactId>drizzle-api</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<artifactId>guava</artifactId>
				<groupId>com.google.guava</groupId>
				<version> 30.0-jre</version>
			</dependency>
			<dependency>
				<artifactId>zookeeper</artifactId>
				<groupId>org.apache.zookeeper</groupId>
				<version> 3.7.0</version>
			</dependency>

			<dependency>
				<groupId>io.projectreactor</groupId>
				<artifactId>reactor-core</artifactId>
				<version>3.7.5</version>
			</dependency>
		</dependencies>


	</dependencyManagement>
	<distributionManagement>
		<repository>
			<id>central</id>
			<name>maven-release-virtual</name>
			<url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<name>maven-snapshot-virtual</name>
			<url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>apache.snapshots.https</id>
			<name>Apache Development Snapshot Repository</name>
			<url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

	<profiles>
		<profile>
			<id>test</id>
		</profile>
		<profile>
			<id>staging</id>
		</profile>
		<profile>
			<id>pro</id>
		</profile>
		<profile>
			<id>checkstyle</id>
			<activation>
				<jdk>[1.8,)</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.mybatis.generator</groupId>
						<artifactId>mybatis-generator-maven-plugin</artifactId>
						<version>1.3.7</version>
						<configuration>
							<verbose>true</verbose>
							<overwrite>true</overwrite>
						</configuration>
						<dependencies>
							<dependency>
								<groupId>mysql</groupId>
								<artifactId>mysql-connector-java</artifactId>
								<version>8.0.20</version>
							</dependency>
							<dependency>
								<groupId>com.itfsw</groupId>
								<artifactId>mybatis-generator-plugin</artifactId>
								<version>1.3.8</version>
							</dependency>
						</dependencies>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<version>${maven-flatten-version}</version>
				<configuration>
					<updatePomFile>true</updatePomFile>
					<flattenMode>resolveCiFriendliesOnly</flattenMode>
					<pomElements>
						<dependencies>expand</dependencies>
					</pomElements>
				</configuration>
				<executions>
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
					</execution>
					<execution>
						<id>flatten.clean</id>
						<phase>clean</phase>
						<goals>
							<goal>clean</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-version}</version>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven_compiler_version}</version>
				<configuration>
					<source>${java_source_version}</source>
					<target>${java_target_version}</target>
					<encoding>${file_encoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.21.0</version>
			</plugin>
		</plugins>
	</build>


</project>
