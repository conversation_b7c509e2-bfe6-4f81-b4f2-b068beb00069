<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xiaomi</groupId>
		<artifactId>dayu</artifactId>
		<version>${revision}</version>
	</parent>
	<groupId>com.xiaomi</groupId>
	<artifactId>dayu-rpc</artifactId>
	<version>${revision}</version>
	<dependencies>

		<dependency>
			<groupId>com.xiaomi.mone</groupId>
			<artifactId>dubbo-auth-all</artifactId>
<!--			<version>2.7.12-mone-SNAPSHOT</version>-->
			<version>3.3.4-mone-v1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.xiaomi.youpin</groupId>
			<artifactId>tesla-auth-api</artifactId>
			<version>1.1.0-SNAPSHOT</version>
		</dependency>


		<dependency>
			<groupId>com.xiaomi.mone</groupId>
			<artifactId>mi-tpc-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.xiaomi</groupId>
			<artifactId>dayu-common</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>com.xiaomi.youpin</groupId>
			<artifactId>gwdash-api</artifactId>
			<version>1.0.1-SNAPSHOT</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>run.mone</groupId>
					<artifactId>batch-deploy-operator-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
			<scope>compile</scope>
		</dependency>
		<!--<dependency>
			<groupId>com.xiaomi.mit</groupId>
			<artifactId>mit-common</artifactId>
			<version>1.0.20-SNAPSHOT</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-text</artifactId>
				</exclusion>
			</exclusions>
		</dependency>-->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>


		<dependency>
			<groupId>com.xiaomi.mone</groupId>
			<artifactId>drizzle-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.xiaomi.youpin</groupId>
			<artifactId>hermes-api</artifactId>
			<version>0.0.3-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.xiaomi.youpin</groupId>
			<artifactId>tesla-k8s-proxy-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.xiaomi.mone</groupId>
			<artifactId>mimonitor-api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>

		<!-- moon -->
		<dependency>
			<artifactId>moon-schedule-dashboard-api</artifactId>
			<groupId>run.mone</groupId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<artifactId>z-api</artifactId>
			<groupId>run.mone.ai</groupId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
	</dependencies>
	<build>
	</build>
</project>
