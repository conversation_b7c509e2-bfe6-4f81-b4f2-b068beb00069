package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.youpin.tesla.auth.api.bo.Result;
import com.xiaomi.youpin.tesla.auth.api.newbo.AuthApprovalDto;
import com.xiaomi.youpin.tesla.auth.api.newbo.AuthPageReqDto;
import com.xiaomi.youpin.tesla.auth.api.newbo.AuthProviderDto;
import com.xiaomi.youpin.tesla.auth.api.newbo.PageData;
import com.xiaomi.youpin.tesla.auth.api.service.AuthDubboV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
@Slf4j
@Component
public class AuthDubboV2ServiceRpc {

    @DubboReference(check = false, interfaceClass = AuthDubboV2Service.class, retries = 1, group = "${rpc.teslaAuth.service.group}",timeout = 6000)
    private AuthDubboV2Service authDubboV2Service;


    public PageData<AuthProviderDto> queryPageAuthProvider(AuthPageReqDto authPageReqDto){
        Result<PageData<AuthProviderDto>> response = authDubboV2Service.queryPageAuthProvider(authPageReqDto);
        if(response.getCode() == 0){
            return response.getData();
        }else{
            log.warn("authDubboV2Service.queryPageAuthProvider 调用异常，authPageReqDto={}，reponse={}", JSON.toJSONString(authPageReqDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public Boolean createAuthProvider(AuthProviderDto authProviderDto){
        Result<Boolean> response = authDubboV2Service.createAuthProvider(authProviderDto);
        if(response.getCode() == 0){
            return true;
        }else{
            log.error("authDubboV2Service.createAuthProvider 调用异常，authProviderDto={}，reponse={}", JSON.toJSONString(authProviderDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public Boolean updateAuthProvider(AuthProviderDto authProviderDto){
        Result<Boolean> response = authDubboV2Service.updateAuthProvider(authProviderDto);
        if(response.getCode() == 0){
            return true;
        }else{
            log.error("authDubboV2Service.updateAuthProvider调用异常，authProviderDto={}，reponse={}", JSON.toJSONString(authProviderDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public Boolean closeAuthProvider(AuthProviderDto authProviderDto){
        Result<Boolean> response = authDubboV2Service.closeAuthProvider(authProviderDto);
        if(response.getCode() == 0){
            return true;
        }else{
            log.error("authDubboV2Service.closeAuthProvider 调用异常，authProviderDto={}，reponse={}", JSON.toJSONString(authProviderDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }
    public AuthProviderDto getAuthProvider(AuthProviderDto authProviderDto){
        Result<AuthProviderDto> response = authDubboV2Service.getAuthProvider(authProviderDto);
        if(response.getCode() == 0){
            if(response.getData() != null){
                return response.getData();
            }else{
                throw new ServiceException("authProviderDto.id="+authProviderDto.getId()+"对应的数据不存在");
            }
        }else{
            log.error("authDubboV2Service.getAuthProvider 调用异常，authProviderDto={}，reponse={}", JSON.toJSONString(authProviderDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }




    public PageData<AuthApprovalDto> queryPageAuthApproval(AuthPageReqDto authPageReqDto){
        Result<PageData<AuthApprovalDto>> response = authDubboV2Service.queryPageAuthApproval(authPageReqDto);
        if(response.getCode() == 0){
            return response.getData();
        }else{
            log.error("authDubboV2Service.queryPageAuthApproval调用异常，authProviderDto={}，reponse={}", JSON.toJSONString(authPageReqDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public Boolean createAuthApproval(AuthApprovalDto authApprovalDto){
        Result<Boolean> response = authDubboV2Service.createAuthApproval(authApprovalDto);
        if(response.getCode() == 0){
            return true;
        }else{
            log.error("authDubboV2Service.createAuthApproval 调用异常，authApprovalDto={}，reponse={}", JSON.toJSONString(authApprovalDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public Boolean updateAuthApproval(AuthApprovalDto authApprovalDto){
        Result<Boolean> response = authDubboV2Service.updateAuthApproval(authApprovalDto);
        if(response.getCode() == 0){
            return true;
        }else{
            log.error("authDubboV2Service.updateAuthApproval 调用异常，authApprovalDto={}，reponse={}", JSON.toJSONString(authApprovalDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public Boolean changeStatusAuthApproval(AuthApprovalDto authApprovalDto){
        Result<Boolean> response = authDubboV2Service.changeStatusAuthApproval(authApprovalDto);
        if(response.getCode() == 0){
            return true;
        }else{
            log.error("authDubboV2Service.changeStatusAuthApproval 调用异常，authApprovalDto={}，reponse={}", JSON.toJSONString(authApprovalDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public Boolean closeAuthApproval(AuthApprovalDto authApprovalDto){
        Result<Boolean> response = authDubboV2Service.closeAuthApproval(authApprovalDto);
        if(response.getCode() == 0){
            return true;
        }else{
            log.error("authDubboV2Service.closeAuthApproval 调用异常，authApprovalDto={}，reponse={}", JSON.toJSONString(authApprovalDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }

    public AuthApprovalDto getAuthApproval(AuthApprovalDto authApprovalDto){
        Result<AuthApprovalDto> response = authDubboV2Service.getAuthApproval(authApprovalDto);
        if(response.getCode() == 0){
            if(response.getData() != null){
                return response.getData();
            }else{
                throw new ServiceException("authApprovalDto.id="+authApprovalDto.getId()+"对应的数据不存在");
            }

        }else{
            log.error("authDubboV2Service.getAuthApproval 调用异常，authApprovalDto={}，reponse={}", JSON.toJSONString(authApprovalDto),JSON.toJSONString(response));
            throw new ServiceException(response.getMessage());
        }
    }
}
