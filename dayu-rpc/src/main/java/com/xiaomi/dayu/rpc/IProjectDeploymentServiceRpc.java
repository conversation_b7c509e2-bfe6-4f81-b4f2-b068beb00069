package com.xiaomi.dayu.rpc;

import com.xiaomi.youpin.gwdash.bo.MachineBo;
import com.xiaomi.youpin.gwdash.service.IProjectDeploymentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class IProjectDeploymentServiceRpc {
    @DubboReference(check = false,interfaceClass = IProjectDeploymentService.class, retries = 3, group = "${rpc.gwdash.IProjectDeploymentService.group}" ,timeout = 6000)
    private IProjectDeploymentService iProjectDeploymentService;
    public int listProjectEnvByProject(long envId) {
        try {
            List<MachineBo> machineList = iProjectDeploymentService.getMachineList(envId);
            return CollectionUtils.isNotEmpty(machineList) ? machineList.size() : 0;
        } catch (Exception e) {
            log.error("iProjectDeploymentService.getMachineList，调用异常，envId={}", envId, e.getMessage());
        }
        return 0;
    }
}
