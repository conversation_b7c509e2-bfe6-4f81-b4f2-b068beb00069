package com.xiaomi.dayu.rpc;

import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.youpin.quota.bo.ResourceBo;
import com.xiaomi.youpin.quota.bo.Result;
import com.xiaomi.youpin.quota.service.ResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
@Slf4j
@Component
public class ResourceServiceRpc {
    @DubboReference(check = false,interfaceClass = ResourceService.class, retries = 0, group = "${rpc.quota.ResourceService.group}",timeout = 6000)
    private ResourceService resourceService;

    public List<ResourceBo> getResourceByProjectId(int projectId){
        try {
            Result<List<ResourceBo>> resourceResult = resourceService.getResourceByProjectId(projectId);
            if(resourceResult.getCode() == 0){
                return resourceResult.getData();
            }else{
                throw new ServiceException("7001"+ Constants.COMMON_ERROR_MESSAGE);
            }
        }catch (Exception e){
            log.error("resourceService.getResourceByProjectId，调用异常，projectId={}",projectId,e);
            throw new ServiceException("7002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
}
