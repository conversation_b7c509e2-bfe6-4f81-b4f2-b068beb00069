package com.xiaomi.dayu.rpc;

import com.xiaomi.youpin.hermes.bo.request.ChangePermissionReq;
import com.xiaomi.youpin.hermes.enums.ResourceTypeEnum;
import com.xiaomi.youpin.hermes.service.PolicyService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/5/16
 */
@Component
public class PolicyServiceRpc {

    @DubboReference(check = false, interfaceClass = PolicyService.class, retries = 1, group = "${rpc.hermes.AccountService.group}",timeout = 6000)
    private PolicyService policyService;

    public boolean changePermissions(ChangePermissionReq req) {
        return this.policyService.changePermissions(req);
    }

    public List<String> searchAssignees(ResourceTypeEnum resourceType, String resourceValue, Integer privilege, Integer pageNo, Integer pageSize) {
        return this.policyService.searchAssignees(resourceType, resourceValue, privilege, pageNo, pageSize);
    }

    public int countAssignees(ResourceTypeEnum resourceType, String resourceValue, Integer privilege) {
        return this.policyService.countAssignees(resourceType, resourceValue, privilege);
    }

    public List<String> searchResources(List<String> assignees, ResourceTypeEnum resourceType, Integer privilege) {
        return this.policyService.searchResources(assignees, resourceType, privilege);
    }
}
