package com.xiaomi.dayu.rpc;

import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.mone.tpc.api.service.UserFacade;
import com.xiaomi.mone.tpc.common.param.UserQryParam;
import com.xiaomi.mone.tpc.common.vo.PageDataVo;
import com.xiaomi.mone.tpc.common.vo.UserVo;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Type UserFacadeRpc.java
 * @Desc
 * @date 2024/6/12 15:39
 */
@Slf4j
@Component
public class UserFacadeRpc {

    @DubboReference(check = false,interfaceClass = UserFacade.class,group = "${rpc.tpc.userFacade.group}",version = "1.0", timeout = 6000)
    private UserFacade userFacade;

    public void list() {
        UserQryParam param = new UserQryParam();
        param.setPage(10);
        param.setPager(true);
        param.setAccount(UserInfoThreadLocal.getUserInfo().getUserName());
        param.setUserType(0);
        Result<PageDataVo<UserVo>> list = userFacade.list(param);
    }
}
