package com.xiaomi.dayu.rpc;


import com.alibaba.fastjson.JSON;
import com.xiaomi.mone.faas.api.bo.FaasFuncBO;
import com.xiaomi.mone.faas.api.bo.FaasFuncRequest;
import com.xiaomi.mone.faas.api.bo.Pager;
import com.xiaomi.mone.faas.api.bo.Pipeline;
import com.xiaomi.mone.faas.api.service.FaasOpenApiInnerService;
import com.xiaomi.mone.miline.api.bo.common.PipelineRecordStatusEnum;
import com.xiaomi.mone.miline.api.dto.PipelineRecordDto;
import com.xiaomi.mone.miline.api.service.MilineOpenService;
import com.xiaomi.mone.tpc.api.service.NodeFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import run.mone.moon.api.bo.common.Result;
import run.mone.moon.api.bo.user.MoonMoneTpcContext;

import static run.mone.moon.api.bo.exception.CommonError.InterError;

/**
 *
 */
@Slf4j
@Service
public class FunctionServiceRpc{

    @DubboReference(interfaceClass = FaasOpenApiInnerService.class, check = false, group = "${ref.faas.service.group}", version = "${ref.faas.service.version}", retries = 1, timeout = 6000)
    private FaasOpenApiInnerService faasOpenApiInnerService;
    @DubboReference(interfaceClass = MilineOpenService.class, check = false, group = "${ref.miline.service.group}", version = "${ref.miline.service.version}", retries = 1, timeout = 6000)
    private MilineOpenService milineOpenService;
    @DubboReference(interfaceClass = NodeFacade.class, check = false, group = "${ref.tpc.node.group}", version = "${ref.tpc.node.version}", retries = 1, timeout = 5000)
    private NodeFacade nodeFacade;
    public Result<FaasFuncBO> readFaasFunction(MoonMoneTpcContext moneTpcContext, FaasFuncRequest faasFuncRequest) {
        try {
            com.xiaomi.youpin.infra.rpc.Result<FaasFuncBO> res = faasOpenApiInnerService.getFaasFunc(moneTpcContext, faasFuncRequest);
            return new Result<>(res.getCode(), res.getMessage(), res.getData());
        } catch (Exception e) {
            log.error("ProjectServiceImpl.readFaasFunction error, msg: {}", e.getMessage());
            return Result.fail(1, "获取faas函数失败");
        }
    }

    public Result<Boolean> canFaasFunctionToTask(MoonMoneTpcContext moneTpcContext, FaasFuncRequest faasFuncRequest) {
        Result<FaasFuncBO> faasFuncBOResult = readFaasFunction(moneTpcContext, faasFuncRequest);
        if (faasFuncBOResult.getCode() != 0) {
            return new Result<>(faasFuncBOResult.getCode(), faasFuncBOResult.getMessage(), false);
        }
        if (faasFuncBOResult.getData() == null) {
            log.error("ProjectServiceImpl.canFaasFunctionToTask, faasFuncBOResult: {}", faasFuncBOResult);
            return new Result<>(InterError.code, "获取faas函数失败", false);
        }

        if (faasFuncBOResult.getData().getEnvConfigList() == null
                || faasFuncBOResult.getData().getEnvConfigList().size() == 0
                || faasFuncBOResult.getData().getEnvConfigList().get(0).getPipeline() == null) {
            return Result.fail(1, "选中的faas函数还未编译");
        }
        Pipeline pipeline = faasFuncBOResult.getData().getEnvConfigList().get(0).getPipeline();
        com.xiaomi.mone.miline.api.bo.Result<PipelineRecordDto> pipelineRecordDtoResult = milineOpenService.qryPipelineRecordDetail(faasFuncBOResult.getData().getProjectId(), pipeline.getId(), null);
        if (pipelineRecordDtoResult.getCode() != 0 || pipelineRecordDtoResult.getData() == null) {
            log.error("ProjectServiceImpl.canFaasFunctionToTask, pipelineRecordDtoResult: {}", pipelineRecordDtoResult);
            return Result.fail(1, "选中faas函数的流水线获取失败");
        }
        if (pipelineRecordDtoResult.getData().getStatus() != PipelineRecordStatusEnum.SUCCESS.getCode()) {
            return Result.fail(1, "选中faas函数的流水线未编译成功");
        }

        return Result.success(true);
    }

    public Result<Pager<FaasFuncBO>> listFaasFunction(MoonMoneTpcContext moonMoneTpcContext, FaasFuncRequest param) {
        try {
            log.info("faasOpenApiInnerService.pageWithAuth，请求，moonMoneTpcContext={}，param={}", JSON.toJSONString(moonMoneTpcContext),JSON.toJSONString(param));
            Pager<FaasFuncBO> res = faasOpenApiInnerService.pageWithAuth(moonMoneTpcContext, param);
            log.info("faasOpenApiInnerService.pageWithAuth，请求，moonMoneTpcContext={}，param={},res={}", JSON.toJSONString(moonMoneTpcContext),JSON.toJSONString(param),JSON.toJSONString(res));
            return Result.success(res);
        } catch (Exception e) {
            log.error("faasOpenApiInnerService.pageWithAuth error, msg: {}", e.getMessage());
            return Result.fail(1, "获取faas函数列表失败");
        }
    }


}
