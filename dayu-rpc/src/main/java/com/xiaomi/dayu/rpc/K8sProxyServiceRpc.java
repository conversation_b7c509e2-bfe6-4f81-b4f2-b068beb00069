package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.xiami.mione.tesla.k8s.service.K8sProxyService;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.mione.tesla.k8s.bo.IPZone;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class K8sProxyServiceRpc {
    @DubboReference( check = false,interfaceClass = K8sProxyService.class, retries = 0, group = "${rpc.tesla.K8sProxyService.group}",timeout = 6000)
    private K8sProxyService k8sProxyService;

    private Gson gson = new Gson();


    public List<IPZone> getIPZone(List<String> ipList){
        try {
            Result<List<IPZone>> result = k8sProxyService.getIPZone(ipList);
            log.warn("k8sProxyService.getIPZone，返回数据，ipList={}，result={}",JSON.toJSONString(ipList),JSON.toJSONString(result));
            if(result.getCode() ==0){
                return result.getData();
            }else{
                log.error("k8sProxyService.getIPZone，调用异常，ipList={}，result={}",JSON.toJSONString(ipList),JSON.toJSONString(result));
                throw new ServiceException("4001"+ Constants.COMMON_ERROR_MESSAGE);
            }
        }catch (Exception e){
            log.error("k8sProxyService.getIPZone，调用异常，ipList={}",JSON.toJSONString(ipList),e);
            throw new ServiceException("4002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
}
