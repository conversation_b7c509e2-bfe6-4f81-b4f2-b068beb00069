package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.youpin.gwdash.bo.openApi.OperationLogRequest;
import com.xiaomi.youpin.gwdash.service.OperationLogService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OperateLogServiceRpc {
    @Value("${dubbo.application.name}")
    private String applicationName;
    @Reference(check = false, interfaceClass = OperationLogService.class, group = "${rpc.gwdash.OperationLogService.group}",timeout = 6000)
    private OperationLogService operationLogService;


    public void recoderOperateLog(int operation,String key,String beforeData,String afterData){

        OperationLogRequest request = new OperationLogRequest();
        request.setAppName(applicationName);
        request.setDataId(key);
        request.setDataBefore(beforeData);
        request.setDataAfter(afterData);
        request.setUserName(UserInfoThreadLocal.getUserInfo().getUserName());
        //1:创建，2：删除，3：更改
        request.setType(operation);
        try {
            log.info("调用OperateLogService.saveOperationLog,入参request={}", JSON.toJSONString(request));
            Result<Boolean> result = operationLogService.saveOperationLog(request);
            log.info("调用OperateLogService.saveOperationLog,返回数据result={}", JSON.toJSONString(result));
            if(!result.getData()){
                log.error("OperateLogService.recoderOperateLog，调用失败，入参request={}，result={}", JSON.toJSONString(request), JSON.toJSONString(result));
            }
        }catch (Throwable e){
            log.error("OperateLogService.recoderOperateLog，调用异常",e);
        }
    }
}
