package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.dto.ApplicationInfoDTO;
import com.xiaomi.dayu.model.dump.PipelineDeployVO;
import com.xiaomi.mone.miline.api.bo.common.DeployTypeEnum;
import com.xiaomi.mone.miline.api.bo.common.EnvEnum;
import com.xiaomi.mone.miline.api.bo.deploy.DeployMachine;
import com.xiaomi.mone.miline.api.dto.MiPipelineEnvDto;
import com.xiaomi.mone.miline.api.dto.PipelineDeployDto;
import com.xiaomi.youpin.gwdash.bo.SimplePipleEnvBo;
import com.xiaomi.youpin.gwdash.bo.openApi.ProjectEnvBo;
import com.xiaomi.youpin.gwdash.service.OpenApiService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class OpenApiServiceRpc {

    @DubboReference(check = true,interfaceClass = OpenApiService.class, retries = 0, group = "${rpc.gwdash.OpenApiService.group}",timeout = 10000)
    private OpenApiService openApiService;

    private static Cache<String, Map<String, List<String>>> localCache = CacheBuilder.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS)
            .maximumSize(20000).build();

    public  Map<String, List<String>> envMachinesByAppName(String appName, String name) {
        try {
            return localCache.get(appName+"_"+name, () -> {
                Result<Map<String, List<String>>> result = openApiService.envMachinesByAppName(appName, name);
                if(result.getCode() == 0){
                    return MapUtils.isNotEmpty(result.getData()) ? result.getData() : new HashMap<>();
                }else{
                    log.error("openApiService.envMachinesByAppName 调用异常，appName={}，name={}，reponse={}",appName,name,JSON.toJSONString(result));
                    throw new ServiceException("5006"+ Constants.COMMON_ERROR_MESSAGE);
                }
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }


    public Map<String, List<String>> qrySimplePipelineByProjectIds(Long id, String env){
        Map<String, List<String>> mapResult = new HashMap<>();
        try {
            log.info("openApiService.qrySimplePipelineByProjectIds，请求，id={}，env={}",id,id,env);
            Result<List<MiPipelineEnvDto>> listResult = openApiService.qrySimplePipelineByProjectIds(Lists.list(id),env);
            log.info("openApiService.qrySimplePipelineByProjectIds，响应，listResult={}",JSON.toJSONString(listResult));
            if(listResult != null && CollectionUtils.isNotEmpty(listResult.getData()) ){
                for (SimplePipleEnvBo simplePipleEnvBo : listResult.getData().get(0).getSimplePipleEnvBo()) {
                    mapResult.put(simplePipleEnvBo.getName(),simplePipleEnvBo.getIps());
                }
            }
            return mapResult;
        }catch (Exception e){
            log.error("openApiService.qrySimplePipelineByProjectIds，调用异常，id={}",id,e);
            return mapResult;
        }
    }
    public Map<Integer,List<PipelineDeployVO>> qryProjectContainerInfo(long projectId, List<Integer> deployTypes, String env){
        log.info("openApiService.qryProjectContainerInfo，请求，projectId={}，deployTypes={}，env={}",projectId,JSON.toJSONString(deployTypes),env);
        try{
            List<PipelineDeployDto> pipelineDeployDtos = openApiService.qryProjectContainerInfo(projectId, deployTypes, env);
            log.info("openApiService.qryProjectContainerInfo，响应，pipelineDeployDtos={}",JSON.toJSONString(pipelineDeployDtos));
            Map<Integer,List<PipelineDeployVO>> result = new HashMap<>();
            if(CollectionUtils.isNotEmpty(pipelineDeployDtos)){
                pipelineDeployDtos.forEach(dto->{
                    int deployType = dto.getDeployType();
                    if(deployType == DeployTypeEnum.DOCKERFILE.getId()){
                        deployType = DeployTypeEnum.DOCKER.getId();
                    }
                    if(CollectionUtils.isNotEmpty(dto.getDeployMachines())){
                        for (DeployMachine deployMachine : dto.getDeployMachines()) {
                            PipelineDeployVO vo = new PipelineDeployVO();
                            vo.setEnv(dto.getEnv());
                            vo.setContainerName(dto.getContainerName());
                            vo.setPipelineId(dto.getPipelineId());
                            vo.setDeployType(dto.getDeployType());
                            vo.setIp(deployMachine.getIp());
                            vo.setPodName(deployMachine.getName());
                            vo.setGitProjectName(dto.getPipelineBaseParam().getGitProjectName());
                            vo.setDeployMachineName(deployMachine.getName());
                            vo.setDeployMachineGroup(deployMachine.getGroup());
                            result.computeIfAbsent(deployType,k->new ArrayList<>()).add(vo);
                        }
                    }
                });
            }
            return  result;
        }catch (Exception e){
            log.error("openApiService.qryProjectContainerInfo，调用异常，projectId={}，deployTypes={}，env={}",projectId,JSON.toJSONString(deployTypes),env,e);
            throw new ServiceException("5010"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public Boolean dumpJvmProcess(Integer pipelineId,String ip,String containerName,String userName,String env){
        try {
            log.info("openApiService.dumpJvmProcess，请求，pipelineId={}，ip={}，containerName={}，userName={}，env={}",pipelineId,ip,containerName,userName,env);
            com.xiaomi.mone.miline.api.bo.Result<Boolean> result = openApiService.dumpJvmProcess(pipelineId, ip, containerName, userName, env);
            log.info("openApiService.dumpJvmProcess，响应，result={}",JSON.toJSONString(result));
            if(result.getCode() == 0){
                return result.getData();
            }else{
                log.error("milineOpenService.dumpJvmProcess，返回数据，pipelineId={}, ip={}, containerName={}, userName={}, env={},response={}",pipelineId, ip, containerName, userName, env, JSON.toJSON(result));
                throw new ServiceException("9001"+ result.getMessage());
            }
        }catch (Exception e){
            log.error("milineOpenService.dumpJvmProcess，调用异常，pipelineId={}, ip={}, containerName={}, userName={}, env={}",pipelineId, ip, containerName, userName, env,e);
            throw new ServiceException("9002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }

    public Map<String, List<ProjectEnvBo>> pipelineListByAppNames(List<String> appList, String milineEnv) {
        Result<Map<String, List<ProjectEnvBo>>> result = openApiService.pipelineListByAppNames(appList, milineEnv);
        if(result.getCode() ==0){
            return result.getData();
        }else{
            log.error("openApiService.pipelineListByAppNames 调用异常，appList={}，milineEnv={}，reponse={}",JSON.toJSONString(appList),milineEnv,JSON.toJSONString(result));
            throw new ServiceException("5001"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
}
