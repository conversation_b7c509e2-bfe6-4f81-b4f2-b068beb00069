package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.youpin.mischedule.api.service.bo.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import run.mone.moon.api.bo.common.Result;
import run.mone.moon.api.bo.task.*;
import run.mone.moon.api.bo.user.MoonMoneTpcContext;
import run.mone.moon.api.service.MoonTaskDubboService;

import java.util.List;
import java.util.function.Function;


@Slf4j
@Component
public class MoonTaskDubboServiceRpc {

    @DubboReference(check = false,interfaceClass = MoonTaskDubboService.class,  group = "${rpc.moon.project.group}",version = "1.0",timeout = 6000)
    private MoonTaskDubboService moonTaskDubboService;


    public Long create(MoonMoneTpcContext context,TaskReq taskReq){
        Result<Long> response;
        try{
            response = moonTaskDubboService.create(context,taskReq);
        }catch (Throwable e){
            log.error("moonTaskDubboService.create，调用异常，context={}，taskReq={}", JSON.toJSONString(context),JSON.toJSONString(taskReq),e);
            throw new ServiceException("10001："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.create，返回数据失败，context={}，taskReq={}，response={}", JSON.toJSONString(context),JSON.toJSONString(taskReq),JSON.toJSONString(response));
            throw new ServiceException("10001："+ response.getMessage());
        }

    }
    public Integer update(MoonMoneTpcContext context, TaskReq taskReq){
        Result<Integer> response;
        try{
            response = moonTaskDubboService.update(context,taskReq);
        }catch (Throwable e){
            log.error("moonTaskDubboService.update，调用异常，context={}，taskReq={}", JSON.toJSONString(context),JSON.toJSONString(taskReq),e);
            throw new ServiceException("10002："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.update，返回数据失败，context={}，taskReq={}，response={}", JSON.toJSONString(context),JSON.toJSONString(taskReq),JSON.toJSONString(response));
            throw new ServiceException("10002："+ response.getMessage());
        }

    }
    public Integer delete(MoonMoneTpcContext context, List<Long> idList){
        Result<Integer> response;
        try{
            response = moonTaskDubboService.delete(context,idList);
        }catch (Throwable e){
            log.error("moonTaskDubboService.delete，调用异常，context={}，taskReq={}", JSON.toJSONString(context),idList,e);
            throw new ServiceException("10003："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.delete，返回数据失败，context={}，taskReq={}，response={}", JSON.toJSONString(context),idList,JSON.toJSONString(response));
            throw new ServiceException("10003："+ response.getMessage());
        }

    }
    public Integer enable(MoonMoneTpcContext context, List<Long> idList,Boolean enable){
        Result<Integer> response ;
        try{
            if(enable){
                response = moonTaskDubboService.enable(context,idList);
            }else{
                response = moonTaskDubboService.disable(context,idList);
            }
        }catch (Throwable e){
            log.error("moonTaskDubboService.enable，调用异常，context={}，idList={}，enable={}", JSON.toJSONString(context),idList,enable,e);
            throw new ServiceException("10004："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else {
            log.error("moonTaskDubboService.enable，返回数据失败，context={}，idList={}，enable={}，response={}", JSON.toJSONString(context), enable, idList,JSON.toJSONString(response));
            throw new ServiceException("10004：" + response.getMessage());
        }
    }
    public Boolean run(MoonMoneTpcContext context, Long id){
        Result<Boolean> response ;
        try{
            response = moonTaskDubboService.run(context,id);
        }catch (Throwable e){
            log.error("moonTaskDubboService.run，调用异常，context={}，id={}，", JSON.toJSONString(context),id,e);
            throw new ServiceException("10004："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else {
            log.error("moonTaskDubboService.run，返回数据失败，context={}，id={}，response={}", JSON.toJSONString(context), id, JSON.toJSONString(response));
            throw new ServiceException("10004：" + response.getMessage());
        }
    }
    public PageInfo<TaskHistoryRes> historyList(MoonMoneTpcContext context, ReadTaskHistoryReq readTaskHistoryReq){
        Result<PageInfo<TaskHistoryRes>> response;
        try{
            response = moonTaskDubboService.historyList(context, readTaskHistoryReq);
        }catch (Throwable e){
            log.error("moonTaskDubboService.historyList，调用异常，readTaskHistoryReq={}", JSON.toJSONString(readTaskHistoryReq),e);
            throw new ServiceException("10005："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.historyList，返回数据失败，readTaskHistoryReq={}，response={}", JSON.toJSONString(readTaskHistoryReq),JSON.toJSONString(response));
            throw new ServiceException("10005："+ response.getMessage());
        }
    }
    public TaskReq get(MoonMoneTpcContext context, Long id){
        Result<TaskReq> response;
        try{
            response = moonTaskDubboService.get( id);
        }catch (Throwable e){
            log.error("moonTaskDubboService.get，调用异常，id={}", id,e);
            throw new ServiceException("10005："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.get，返回数据失败，id={}，response={}", id,JSON.toJSONString(response));
            throw new ServiceException("10005："+ response.getMessage());
        }
    }
    public TaskList list(MoonMoneTpcContext context, ReadTaskReq readTaskReq){
        Result<TaskList> response;
        try{
            response = moonTaskDubboService.list(context, readTaskReq);
        }catch (Throwable e){
            log.error("moonTaskDubboService.list，调用异常，readTaskReq={}", JSON.toJSONString(readTaskReq),e);
            throw new ServiceException("10005："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.list，返回数据失败，readTaskReq={}，response={}", JSON.toJSONString(readTaskReq),JSON.toJSONString(response));
            throw new ServiceException("10005："+ response.getMessage());
        }
    }
    public TaskHistorySum historySummary(MoonMoneTpcContext context, ReadTaskSummaryReq readTaskSummaryReq){
        Result<TaskHistorySum> response;
        try{
            response = moonTaskDubboService.historySummary(context, readTaskSummaryReq);
        }catch (Throwable e){
            log.error("moonTaskDubboService.historySummary，调用异常，readTaskSummaryReq={}", JSON.toJSONString(readTaskSummaryReq),e);
            throw new ServiceException("10005："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.historySummary，返回数据失败，readTaskSummaryReq={}，response={}", JSON.toJSONString(readTaskSummaryReq),JSON.toJSONString(response));
            throw new ServiceException("10005："+ response.getMessage());
        }
    }
    public Boolean historyStop(MoonMoneTpcContext context, ReadTaskHistoryReq readTaskHistoryReq){
        Result<Boolean> response;
        try{
            response = moonTaskDubboService.historyStop(context, readTaskHistoryReq);
        }catch (Throwable e){
            log.error("moonTaskDubboService.historySummary，调用异常，readTaskHistoryReq={}", JSON.toJSONString(readTaskHistoryReq),e);
            throw new ServiceException("10005："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.historyStop，返回数据失败，readTaskHistoryReq={}，response={}", JSON.toJSONString(readTaskHistoryReq),JSON.toJSONString(response));
            throw new ServiceException("10005："+ response.getMessage());
        }
    }
    public TaskHistoryRes historyGet(MoonMoneTpcContext context, ReadTaskHistoryReq readTaskHistoryReq){
        Result<TaskHistoryRes> response;
        try{
            response = moonTaskDubboService.historyGet(context, readTaskHistoryReq);
        }catch (Throwable e){
            log.error("moonTaskDubboService.historyGet，调用异常，readTaskHistoryReq={}", JSON.toJSONString(readTaskHistoryReq),e);
            throw new ServiceException("10005："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.historyGet，返回数据失败，readTaskHistoryReq={}，response={}", JSON.toJSONString(readTaskHistoryReq),JSON.toJSONString(response));
            throw new ServiceException("10005："+ response.getMessage());
        }
    }

    public Object get(String functionName, Function function,Object ...arg){
        Result<Object> response;
        try{
             response = (Result<Object>)function.apply(arg);
        }catch (Throwable e){
            log.error("moonTaskDubboService."+functionName+"，调用异常，参数={}", JSON.toJSONString(arg),e);
            throw new ServiceException("10005："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService."+functionName+"，返回数据失败，参数={}，response={}", JSON.toJSONString(arg),JSON.toJSONString(response));
            throw new ServiceException("10005："+ response.getMessage());
        }

    }
    public List<String> checkCron(String cron){
        Result<List<String>> response;
        try{
            response = moonTaskDubboService.checkCron(cron);
        }catch (Throwable e){
            log.error("moonTaskDubboService.checkCron，调用异常，cron={}", cron,e);
            throw new ServiceException("10006："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response.getData();
        }else{
            log.error("moonTaskDubboService.checkCron，返回数据失败，cron={}，response={}", cron,JSON.toJSONString(response));
            throw new ServiceException("10006："+ response.getMessage());
        }
    }
}
