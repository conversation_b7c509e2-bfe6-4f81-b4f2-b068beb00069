package com.xiaomi.dayu.rpc;

import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import run.mone.ai.z.dto.ZPrompt;
import run.mone.ai.z.enums.DatabaseEnum;
import run.mone.ai.z.service.ZDubboService;

import java.util.ArrayList;
import java.util.List;
@Slf4j
@Component
public class ZDubboServiceRpc {
    @DubboReference(check = false,interfaceClass = ZDubboService.class, retries = 0, group = "staging",version = "1.0",timeout = 6000)
    private ZDubboService zDubboService;

    public List<String> prompt(String prompt){
        try {
            ZPrompt zPrompt = new ZPrompt();
            zPrompt.setPrompt(prompt);
            zPrompt.setToken("***************************************************");
            zPrompt.setType(DatabaseEnum.DayuProjects.name);
            Result<String> prompt1 = zDubboService.prompt(zPrompt);
            List<String> list = new ArrayList<>();
            list.add(prompt1.getData());
            return list;
        }catch (Exception e){
            throw new ServiceException("3002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }

}
