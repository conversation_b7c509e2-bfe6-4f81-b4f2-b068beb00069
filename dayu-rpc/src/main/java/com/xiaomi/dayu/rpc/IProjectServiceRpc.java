package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.dto.ProjectEnvDTO;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.gwdash.bo.ProjectEnv2Bo;
import com.xiaomi.youpin.gwdash.bo.SearchAppNameParam;
import com.xiaomi.youpin.gwdash.service.IProjectService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class IProjectServiceRpc {
    @DubboReference(check = false,interfaceClass = IProjectService.class, retries = 3, group = "${rpc.gwdash.IProjectService.group}" ,timeout = 6000)
    private IProjectService iProjectService;

    private Cache<String,ProjectBo> projectBoCache = CacheBuilder.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(500).build();

    public List<ProjectEnvDTO> listProjectEnvByProject(Integer projectId){
        try {
            List<ProjectEnv2Bo> projectEnv2Bos = iProjectService.listProjectEnvByProject(projectId);
            if(CollectionUtils.isNotEmpty(projectEnv2Bos)){
                return projectEnv2Bos.stream().map(project->{
                    ProjectEnvDTO projectEnvDTO = new ProjectEnvDTO();
                    projectEnvDTO.setProjectId(project.getProjectId());
                    projectEnvDTO.setName(project.getName());
                    projectEnvDTO.setId(project.getId());
                    return projectEnvDTO;
                }).collect(Collectors.toList());
            }
            return null;
        }catch (Exception e){
            log.error("iProjectService.listProjectEnvByProject，调用异常，projectId={}",projectId,e);
            throw new ServiceException("3001"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public ProjectBo getProjectByName(String projectName){
        try {
            if(StringUtils.isBlank(projectName)){
                return null;
            }
            return projectBoCache.get(projectName,()->{
                List<ProjectBo> projectBos = iProjectService.getProjectByName(projectName,true);
                if(CollectionUtils.isNotEmpty(projectBos)){
                    return projectBos.get(0);
                }else{
                    return new ProjectBo();
                }
            });
        }catch (Exception e){
            log.error("iProjectService.getProjectByName，调用异常，projectName={}, projectId={}",projectName,null,e);
            throw new ServiceException("3002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public List<ProjectBo> getProjectListByNameList(List<String> nameList){
        try {
            return iProjectService.getProjectListByNameList(nameList);
        }catch (Exception e){
            log.error("iProjectService.getProjectListByNameList，调用异常，nameList={}", JSON.toJSONString(nameList),e);
            e.printStackTrace();
            throw new ServiceException("3003"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }

    public List<ProjectBo> getProjectsByIds(List<Integer> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) {
            return new ArrayList<>();
        }
        Result<List<ProjectBo>> result = this.iProjectService.searchProjects(SearchAppNameParam.builder()
                .projectIds(projectIds)
                .build());
        return result.getData();
    }

    public Set<String> getAllProjectName(){
        try {
            return iProjectService.getAllAppNames().getData();
        }catch (Exception e){
            log.error("iProjectService.getAllProjectName，调用异常:",e);
            throw new ServiceException("3004"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
}
