package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import run.mone.moon.api.bo.common.Result;
import run.mone.moon.api.bo.project.ProjectDto;
import run.mone.moon.api.bo.user.MoonMoneTpcContext;
import run.mone.moon.api.service.MoonProjectDubboService;

@Slf4j
@Component
public class MoonProjectDubboServiceRpc {
    @DubboReference(check = false,interfaceClass = MoonProjectDubboService.class,  group = "${rpc.moon.project.group}",version = "${rpc.moon.project.version}",timeout = 6000)
    private MoonProjectDubboService moonProjectDubboService;
    public Result<ProjectDto> projectDetail(MoonMoneTpcContext context,Long projectId){
        Result<ProjectDto> response;
        try{
            log.info("moonProjectDubboService.projectDetail，请求，context={}，projectId={}", JSON.toJSONString(context),projectId);
            response = moonProjectDubboService.projectDetail(context, projectId);
            log.info("moonProjectDubboService.projectDetail，响应，context={}，projectId={}，response", JSON.toJSONString(context),projectId,JSON.toJSONString(response));
        }catch (Throwable e){
            log.error("moonProjectDubboService.projectDetail，调用异常，context={}，projectId={}", JSON.toJSONString(context),projectId,e);
            throw new ServiceException("10001："+ e.getMessage());
        }
        if(response.getCode() ==0 ){
            return response;
        }else{
            log.error("moonProjectDubboService.projectDetail，返回数据失败，context={}，projectId={}，response={}", JSON.toJSONString(context),projectId,JSON.toJSONString(response));
            throw new ServiceException("10001："+ response.getMessage());
        }

    }
}
