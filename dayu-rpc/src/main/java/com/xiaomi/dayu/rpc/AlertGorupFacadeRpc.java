package com.xiaomi.dayu.rpc;

import com.xiaomi.mone.monitor.service.AlertGorupFacade;
import com.xiaomi.mone.monitor.service.bo.AlertGroupQryInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AlertGorupFacadeRpc {


    @DubboReference(check = false,interfaceClass = AlertGorupFacade.class, retries = 0, group = "${rpc.mimonitor.AlertGorupFacade.group}",version = "1.0",timeout = 6000)
    private AlertGorupFacade alertGorupFacade;

    public List<AlertGroupQryInfo> query(String account,String likeName){
        try {
            List<AlertGroupQryInfo> response = alertGorupFacade.query(account, likeName);
            return response;
        } catch (Exception e) {
            log.error("AlertGorupFacade.query，调用异常，account={}，likeName={}", account,likeName, e);
        }
        return null;
    }

}
