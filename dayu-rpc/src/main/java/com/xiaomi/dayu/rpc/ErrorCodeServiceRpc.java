package com.xiaomi.dayu.rpc;

import com.xiaomi.mone.drizzleapi.api.ErrorCodeService;
import com.xiaomi.mone.drizzleapi.bo.ErrorCodeBo;
import com.xiaomi.mone.drizzleapi.bo.ListErrorCodeReq;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @since 2022/5/16
 */
@Component
public class ErrorCodeServiceRpc {

    @DubboReference(check = false, interfaceClass = ErrorCodeService.class, retries = 1, group = "${rpc.drizzle.service.group}",timeout = 6000)
    private ErrorCodeService errorCodeService;

    public List<ErrorCodeBo> listCodes(ListErrorCodeReq reqBody) {
        return this.errorCodeService.listCodes(reqBody);
    }

    public Long countCodes(ListErrorCodeReq reqBody) {
        return this.errorCodeService.countCodes(reqBody);
    }
}
