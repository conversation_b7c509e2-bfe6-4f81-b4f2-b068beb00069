package com.xiaomi.dayu.rpc;

import com.xiaomi.mone.drizzleapi.api.ErrorScopeService;
import com.xiaomi.mone.drizzleapi.bo.ErrorScopeBo;
import com.xiaomi.mone.drizzleapi.bo.ListErrorScopeReq;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @since 2022/5/16
 */
@Component
public class ErrorScopeServiceRpc {

    @DubboReference(check = false, interfaceClass = ErrorScopeService.class, retries = 1, group = "${rpc.drizzle.service.group}",timeout = 6000)
    private ErrorScopeService errorScopeService;

    public List<ErrorScopeBo>  listScopes(ListErrorScopeReq reqBody) {
        return this.errorScopeService.listScopes(reqBody);
    }

    public Long countScopes(ListErrorScopeReq reqBody) {
        return this.errorScopeService.countScopes(reqBody);
    }

    public void saveScope(ErrorScopeBo reqBody) {
        this.errorScopeService.saveScope(reqBody);
    }

    public List<ErrorScopeBo> scopeDetails(List<Integer> ids) {
        return this.errorScopeService.scopeDetails(ids);
    }
}
