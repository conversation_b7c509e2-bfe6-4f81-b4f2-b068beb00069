package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.mone.tpc.api.service.ProjectFacade;
import com.xiaomi.mone.tpc.common.enums.UserTypeEnum;
import com.xiaomi.mone.tpc.common.param.ProjectQryParam;
import com.xiaomi.mone.tpc.common.param.ProjectUserQryParam;
import com.xiaomi.mone.tpc.common.vo.NodeUserRelVo;
import com.xiaomi.mone.tpc.common.vo.PageDataVo;
import com.xiaomi.mone.tpc.common.vo.ProjectVoV2;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
@Slf4j
@Component
public class ProjectFacadeRpc {
    private static Cache<String, ProjectVoV2> localCacheProject = CacheBuilder.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS)
            .maximumSize(500).build();

    @DubboReference(check = false,interfaceClass = ProjectFacade.class,  group = "${rpc.tpc.projectFacade.group}",version = "1.0",timeout = 6000)
    private ProjectFacade projectFacade;


    public ProjectVoV2 getProject(String projectName,Long projectId){
       try{
           return localCacheProject.get(projectName+"_"+projectId,()->{
               ProjectQryParam param = new ProjectQryParam();
               param.setName(projectName);
               param.setProjectId(projectId);
               param.setUserType(UserTypeEnum.CAS_TYPE.getCode());
               param.setAccount(UserInfoThreadLocal.getUserInfo().getUserName());
               Result<ProjectVoV2> response = projectFacade.get(param);
               if(response.getCode() ==0 ){
                   return response.getData();
               }else{
                   log.error("projectFacade.get，调用异常，projectName={}，response={}", projectName,JSON.toJSONString(response));
                   return new ProjectVoV2();
//                   throw new ServiceException("6001"+ Constants.COMMON_ERROR_MESSAGE);
               }
           });
        }catch (Throwable e){
            log.error("projectFacade.get，调用异常，projectName={}",projectName,e);
           throw new ServiceException("6002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public List<NodeUserRelVo> getProjectMembers(List<Long> projectList){
       try{
            ProjectUserQryParam param = new ProjectUserQryParam();
            param.setProjectIds(projectList);
           param.setUserType(UserTypeEnum.CAS_TYPE.getCode());
           if(UserInfoThreadLocal.getUserInfo() != null){
               param.setAccount(UserInfoThreadLocal.getUserInfo().getUserName());
           }else{
               param.setAccount(RpcContext.getContext().getAttachment("userName").toString());
           }
            Result<List<NodeUserRelVo>> response = projectFacade.getProjectMembers(param);
            if(response.getCode() ==0 && CollectionUtils.isNotEmpty(response.getData())){
                return response.getData();
            }else{
                log.error("projectFacade.getProjectMembers，调用异常，projectList={}，response={}", JSON.toJSONString(projectList),JSON.toJSONString(response));
                throw new ServiceException("6001"+ Constants.COMMON_ERROR_MESSAGE);
            }
        }catch (Throwable e){
            log.error("projectFacade.getProjectMembers，调用异常，projectList={}",JSON.toJSONString(projectList),e);
           throw new ServiceException("6002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public List<ProjectVoV2> search(String userName,String projectName){
        try{
            List<ProjectVoV2> data = new ArrayList<>();
            int page = 1;
            int pageSize = 100;
            int max = 100;
            Result<List<ProjectVoV2>> response ;
            do{
                ProjectQryParam param = new ProjectQryParam();
                param.setAccount(userName);
                param.setUserType(UserTypeEnum.CAS_TYPE.getCode());
                param.setPage(page);
                param.setPageSize(pageSize);
                param.setPager(true);
                if(StringUtils.isNotBlank(projectName)){
                    param.setName(projectName);
                }
                response = projectFacade.search(param);
                if(response != null &response.getCode() ==0){
                   if(CollectionUtils.isNotEmpty(response.getData())){
                       data.addAll(response.getData());
                   } else{
                       break;
                   }
                }else{
                    log.error("projectFacade.search，调用异常，request={}，response={}", JSON.toJSONString(param),JSON.toJSONString(response));
                    throw new ServiceException("6003"+ Constants.COMMON_ERROR_MESSAGE);
                }
                page++;
            }while (CollectionUtils.isNotEmpty(response.getData()) && response.getData().size() == pageSize && page < max);
            return data;
        }catch (Throwable e){
            log.error("projectFacade.search，调用异常，userName={}，projectName={}", userName,projectName,e.getMessage());
            throw new ServiceException("6004"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }

    public PageDataVo<ProjectVoV2> getAppsByUserName(String username, String appName, Boolean isShowAll, Integer pageNum, Integer pageSize) {
        ProjectQryParam param = new ProjectQryParam();
        param.setAccount(username);
        param.setUserType(UserTypeEnum.CAS_TYPE.getCode());
        param.setMy(isShowAll ? false:true);
        param.setPage(pageNum);
        param.setPageSize(pageSize);
        param.setPager(true);
        if(StringUtils.isNotBlank(appName)){
            param.setName(appName);
        }
        Result<PageDataVo<ProjectVoV2>> pageDataVoResult = projectFacade.searchByPage(param);
        if(pageDataVoResult.getCode() == 0){
            return pageDataVoResult.getData();
        }else {
            log.error("projectFacade.searchByPage，调用异常，request={}，response={}", JSON.toJSONString(param),JSON.toJSONString(pageDataVoResult));
            throw new ServiceException("6005"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
}
