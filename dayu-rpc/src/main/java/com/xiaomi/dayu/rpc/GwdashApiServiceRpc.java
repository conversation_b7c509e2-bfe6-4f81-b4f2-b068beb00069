package com.xiaomi.dayu.rpc;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.common.util.PageResult;
import com.xiaomi.dayu.model.dto.ApplicationInfoDTO;
import com.xiaomi.dayu.model.gwdash.ProjectDTO;
import com.xiaomi.mone.application.api.bo.ProjectVo;
import com.xiaomi.mone.tpc.common.vo.PageDataVo;
import com.xiaomi.mone.tpc.common.vo.ProjectVoV2;
import com.xiaomi.youpin.gwdash.bo.ProjectRoleBo;
import com.xiaomi.youpin.gwdash.service.GwdashApiService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class GwdashApiServiceRpc {

    @DubboReference( check = false,interfaceClass = GwdashApiService.class, retries = 0, group = "${rpc.gwdash.GwdashApiService.group}",timeout = 6000)
    private GwdashApiService gwdashApiService;
    @Autowired
    private ProjectFacadeRpc projectFacadeRpc;

    private Gson gson = new Gson();

    public List<ProjectRoleBo> getMembersByProjectId(Integer projectId){
        Result<List<ProjectRoleBo>> response;
        try {
            response = gwdashApiService.getMembersByProjectId(projectId);
            if(response.getCode() ==0){
                return response.getData();
            }else{
                log.error("gwdashApiService.getMembersByProjectId，调用异常，projectId={}，response={},",projectId,JSON.toJSONString(response) );
                throw new ServiceException("2001"+ Constants.COMMON_ERROR_MESSAGE);
            }
        }catch (Exception e){
            log.error("gwdashApiService.getMembersByProjectId，调用异常，projectId={}，result={}",projectId,gson.toString() ,e);
            throw new ServiceException("2002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public PageResult<ApplicationInfoDTO> queryApps(String username, String appName, Boolean isShowAll, Integer pageNum, Integer pageSize){
        Result<Map<String, Object>> result;
        try {
            result = gwdashApiService.getAppsByUserName(username, appName, isShowAll, pageNum, pageSize);
            Map<String, Object> data = result.getData();
            int total = (int)data.get("total");
            List<HashMap<String,Object>> list = (List<HashMap<String,Object>>)data.get("list");
            List<ApplicationInfoDTO> arrayList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(list)){
                for (HashMap<String, Object> hashMap : list) {
                    ProjectDTO projectDTO = JSON.parseObject(JSON.toJSONString(hashMap), ProjectDTO.class);
                    arrayList.add(convert(projectDTO));
                }
            }
            return new PageResult<>(arrayList,total, pageSize, pageNum);
        }catch (Exception e){
            log.error("gwdashApiService.queryApps，调用异常，appName={}，result={}",appName,gson.toString() ,e);
            throw new ServiceException("2003"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public PageResult<ApplicationInfoDTO> queryApps2(String username, String appName, Boolean isShowAll, Integer pageNum, Integer pageSize){
        PageDataVo<ProjectVoV2> result;
        try {
            result = projectFacadeRpc.getAppsByUserName(username, appName, isShowAll, pageNum, pageSize);
            List<ApplicationInfoDTO> data = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(result.getList())){
                for (ProjectVoV2 projectVoV2 : result.getList()) {
                    ProjectVo projectVo = projectVoV2.getProjectVo();
                    ProjectDTO projectDTO = new ProjectDTO();
                    projectDTO.setName(projectVo.getName());
                    projectDTO.setId(projectVo.getId());
                    projectDTO.setDesc(projectVo.getDesc());
                    projectDTO.setGitAddress(projectVo.getGitAddress());
                    projectDTO.setCtime(projectVo.getCtime());
                    projectDTO.setUtime(projectVo.getUtime());
                    projectDTO.setStatus(projectVo.getStatus());
                    data.add(convert(projectDTO));
                }
            }
            return new PageResult<>(data,result.getTotal(), pageSize, pageNum);
        }catch (Exception e){
            log.error("gwdashApiService.queryApps，调用异常，appName={}，result={}",appName,gson.toString() ,e);
            throw new ServiceException("2003"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }


//    @PostConstruct
    public void export(){
        Result<Map<String, Object>> result;
        List<HashMap<String,Object>> list ;
        int pageNo=1;
        int pageSize=50;
        List<LinkedHashMap<String,Object>> all = new ArrayList<>();
        try {
            do{
                result = gwdashApiService.getAppsByUserName( null,  null,  true,  pageNo,  pageSize);
                Map<String, Object> data = result.getData();
                int total = (int)data.get("total");
                list = (List<HashMap<String,Object>>)data.get("list");
                if(CollectionUtils.isNotEmpty(list)){
                    for (HashMap<String, Object> hashMap : list) {
                        ProjectDTO projectDTO = JSON.parseObject(JSON.toJSONString(hashMap), ProjectDTO.class);
                        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
                        //  linkedHashMap.put("id",projectDTO.getId());
                        linkedHashMap.put("name",projectDTO.getName());
                        linkedHashMap.put("desc",projectDTO.getDesc());
                        linkedHashMap.put("gitAddress",projectDTO.getGitAddress());
                        all.add(linkedHashMap);
                    }
                }
                pageNo++;
            }while (CollectionUtils.isNotEmpty(list));
            System.err.println(JSON.toJSONString(all));
        }catch (Exception e){
            throw new ServiceException("2003"+ Constants.COMMON_ERROR_MESSAGE);
        }

    }
    private ApplicationInfoDTO convert(ProjectDTO projectDTO){
        ApplicationInfoDTO applicationInfoDTO = new ApplicationInfoDTO();
        applicationInfoDTO.setId(projectDTO.getId());
        applicationInfoDTO.setName(projectDTO.getName());
        applicationInfoDTO.setStatus(projectDTO.getStatus());
        if(projectDTO.getProjectGen() != null){
            applicationInfoDTO.setType(projectDTO.getProjectGen().getType());
        }
        applicationInfoDTO.setDescription(projectDTO.getDesc());
        applicationInfoDTO.setModifyDate(new Date(projectDTO.getUtime()));
        applicationInfoDTO.setCreationDate(new Date(projectDTO.getCtime()));
        return applicationInfoDTO;
    }

    public List<ProjectRoleBo> getProjectsByAccountIds(List<Integer> accountIds) {
        Result<List<ProjectRoleBo>> result = this.gwdashApiService.getProjectsByAccountIds(accountIds);
        return result.getData();
    }
}
