package com.xiaomi.dayu.rpc;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.dayu.common.exception.ServiceException;
import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.dto.RadarSystems;
import com.xiaomi.mone.application.api.bo.ProjectVo;
import com.xiaomi.mone.tpc.common.param.ProjectUserQryParam;
import com.xiaomi.mone.tpc.common.vo.NodeUserRelVo;
import com.xiaomi.mone.tpc.common.vo.ProjectVoV2;
import com.xiaomi.youpin.gwdash.bo.ProjectBo;
import com.xiaomi.youpin.hermes.bo.ApplicationListBo;
import com.xiaomi.youpin.hermes.bo.ResourceBo;
import com.xiaomi.youpin.hermes.bo.RoleBo;
import com.xiaomi.youpin.hermes.bo.UserInfoResult;
import com.xiaomi.youpin.hermes.bo.request.AccountRegisterRequest;
import com.xiaomi.youpin.hermes.bo.request.QueryRoleRequest;
import com.xiaomi.youpin.hermes.bo.response.Account;
import com.xiaomi.youpin.hermes.entity.Project;
import com.xiaomi.youpin.hermes.service.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AccountServiceRpc {
    private static Cache<String,List<UserInfoResult>> localCache = CacheBuilder.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS)
            .maximumSize(500).build();
    private static Cache<String,ApplicationListBo> localCacheApp = CacheBuilder.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS)
            .maximumSize(500).build();


    @Value("${dubbo.application.name}")
    private String appName;

    @Value("${radar.system.url:}")
    private String radarUrl;

    @Resource
    private UserFacadeRpc userFacadeRpc;

    @DubboReference(check = false,interfaceClass = AccountService.class, retries = 0, group = "${rpc.hermes.AccountService.group}" ,timeout = 6000)
    private AccountService accountService;
    @Resource
    private ProjectFacadeRpc projectFacadeRpc;
    @Autowired
    private IProjectServiceRpc iProjectServiceRpc;

    public ApplicationListBo queryApplicationNamesByUsername(String userName){
        try {
            return localCacheApp.get(userName,() -> {
                ApplicationListBo applicationListBo = new ApplicationListBo();
                List<ProjectVoV2> projectVoV2List = projectFacadeRpc.search(userName, null);
                if(CollectionUtils.isNotEmpty(projectVoV2List)){
                    applicationListBo.setApplicationList(projectVoV2List.stream().map(projectVoV2 -> {
                        ProjectVo projectVo = projectVoV2.getProjectVo();
                        Project project = new Project();
                        project.setId(projectVo.getId());
                        project.setName(projectVo.getName());
                        return project;
                    }).collect(Collectors.toList()));
                }else{
                    applicationListBo = new ApplicationListBo();
                }
//                ApplicationListBo applicationListBo = accountService.queryApplicationListByUsername(userName, appName);
                return applicationListBo;
            });
        }catch (Exception e){
            log.error("AccountService.queryApplicationNamesByUsername，调用异常，userName={}",userName,e);
            throw new ServiceException("1001"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
/*    public List<UserInfoResult> queryUsersByAppName(String applicationName){
        try {
            return localCache.get(applicationName, () -> {
                List<UserInfoResult> userInfoResults = accountService.queryUsersInfoByAppName(applicationName);
                if(userInfoResults == null){
                    userInfoResults = new ArrayList<>();
                }
                return userInfoResults;
            });

        }catch (Throwable e){
            log.error("AccountService.queryUsersByAppName，调用异常，applicationName={}，e={}",applicationName,e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }*/
    public List<UserInfoResult> queryUsersByAppName(String applicationName){
        try {
            ProjectBo projectBo = iProjectServiceRpc.getProjectByName(applicationName);
            if(projectBo == null || projectBo.getId() == null){
                return null;
            }
            ProjectUserQryParam param = new ProjectUserQryParam();
            param.setProjectIds(Lists.list(new Long(projectBo.getId())));
            return localCache.get(applicationName, () -> {
                List<UserInfoResult> result = new ArrayList<>() ;
                List<NodeUserRelVo> projectMembers = projectFacadeRpc.getProjectMembers(Lists.list(new Long(projectBo.getId())));
                if(CollectionUtils.isNotEmpty(projectMembers)){
                    HashSet<String> userSet = new HashSet<>();
                    projectMembers.forEach(nodeUserRelVo -> {
                        if (userSet.add(nodeUserRelVo.getAccount())) {
                            UserInfoResult userInfoResult = new UserInfoResult();
                            userInfoResult.setUserName(nodeUserRelVo.getAccount());
                            userInfoResult.setName(nodeUserRelVo.getAccount());
                            result.add(userInfoResult);
                        }
//                        userInfoResult.setId(nodeUserRelVo.getId());
                    });
                }
                return  result;
            });

        }catch (Throwable e){
            log.error("AccountService.queryUsersByAppName，调用异常，applicationName={}，",applicationName,e);
            throw new ServiceException("1002"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    public List<String> queryUsersByAppNameList(String applicationName){
        List<UserInfoResult> userInfoResults = queryUsersByAppName(applicationName);
        List<String> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(userInfoResults)){
            for (UserInfoResult userInfoResult : userInfoResults) {
                list.add(userInfoResult.getUserName());
            }
        }
        return list;
    }

    public List<Account> getAllAccountList(){
        try {
//            userFacadeRpc.list();
            return null;
            //return  accountService.getAllAccountList();
        }catch (Exception e){
            log.error("AccountService.getAllAccountList，调用异常，",e);
            throw new ServiceException("1003"+ Constants.COMMON_ERROR_MESSAGE);
        }
    }
    /*public ApplicationListBo queryAppNamesByUser(String user) {
        String url = radarUrl + "?page=1&pageSize=50&appName=&appType=0&user="+user;
        RadarSystems radarSystems = getUrlData(url);

        List<RadarSystems.AppInfo> appInfoList = radarSystems.getData().getList();
        List<Project> listPros = new ArrayList<>();
        for (RadarSystems.AppInfo appInfo : appInfoList) {
            Project project = new Project();
            //名字太长，一些无意义的去掉
            project.setName(appInfo.getName().replaceAll("_mi_com","")
                    .replaceAll("_xiaomi_com","")
                    .replaceAll("_cn_pro",""));
            project.setId(Long.parseLong(appInfo.getId()+""));
            project.setDescription(appInfo.getSystem_description());
            project.setCreationDate(toDate(appInfo.getCreateTime()));
            project.setModifyDate(toDate(appInfo.getUpdateTime()));
            listPros.add(project);
        }
        ApplicationListBo appListBo = new ApplicationListBo();
        appListBo.setApplicationList(listPros);
        return appListBo;
    }*/

/*    public RadarSystems.PageData queryAppByUser(String user, int pageNum, int pageSize) {
        String url = radarUrl + "?page="+pageNum+"&pageSize="+pageSize+"&appName=&appType=0&user="+user;
        return getRadarPageData(url);
    }*/

/*    public RadarSystems.PageData queryAppByName(String user, String name) {
        String url = radarUrl + "?page=1&pageSize=10&appName="+name+"&appType=0&user="+user;
        return getRadarPageData(url);
    }*/

/*
    private RadarSystems.PageData getRadarPageData(String url) {
        RadarSystems radarSystems = getUrlData(url);

        //名字太长，一些无意义的去掉
        List<RadarSystems.AppInfo> appInfoList = radarSystems.getData().getList();
        for (RadarSystems.AppInfo appInfo : appInfoList) {
            String appName = appInfo.getName().replaceAll("_mi_com","")
                    .replaceAll("_xiaomi_com","")
                    .replaceAll("_cn_pro","");
            appInfo.setName(appName);
        }
        return radarSystems.getData();
    }
*/


/*    private RadarSystems getUrlData(String url) {
        RadarSystems radarSystems = HttpClients.url(url).get().as(RadarSystems.class);
        int count = 0;
        while (count < 4 && (radarSystems ==null || radarSystems.getData() ==null || radarSystems.getData().getList() ==null)) {
            radarSystems = HttpClients.url(url).get().as(RadarSystems.class);
            count++;
        }
        return radarSystems;
    }*/

/*    public Account queryUserByName(String name) {
        return accountService.queryUserByName(name);
    }


    public List<RoleBo> getRoleByProjectName(QueryRoleRequest request) {
        return accountService.getRoleByProjectName(request);
    }

    public Account registerAccount(AccountRegisterRequest request) {
        return accountService.registerAccount(request);
    }*/


    public List<UserInfoResult> queryUsersInfoByAppNameAndRole (String appName,Long projectId, Integer role) {
        List<NodeUserRelVo> projectMembers = projectFacadeRpc.getProjectMembers(Lists.list(projectId));
        if(CollectionUtils.isNotEmpty(projectMembers)){
            return projectMembers.stream().map(member -> {
                UserInfoResult userInfoResult = new UserInfoResult();
                userInfoResult.setName(member.getAccount());
                userInfoResult.setUserName(member.getAccount());
                return userInfoResult;
            }).collect(Collectors.toList());

        }
        return null;
        //return this.accountService.queryUsersInfoByAppNameAndRole(appName, role);
    }

    public List<Account> queryUsersByUserNames(List<String> usernames) {
        return this.accountService.queryUsersByUserNames(usernames);
    }


}
