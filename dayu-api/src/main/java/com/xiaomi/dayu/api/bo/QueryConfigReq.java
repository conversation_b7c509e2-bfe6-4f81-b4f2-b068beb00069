package com.xiaomi.dayu.api.bo;

import lombok.Data;

import java.io.Serializable;
@Data
public class QueryConfigReq implements Serializable {
    /**
     * dataId 必填
     */
    private String dataId;
    /**
     * dataId 必填
     */
    private String group;
    /**
     * 非必填，default public
     */
    private String tenant;
    /**
     * appName 必填
     */
    private String appName;
    /**
     * 操作用户名
     */
    private String username;
    /**
     *  id 非必填
     */
    private Long id;

    private Long pageNo = 1L;

    private int pageSize = 10;
}
