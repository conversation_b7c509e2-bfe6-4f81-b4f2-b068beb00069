package com.xiaomi.dayu.api.bo;

import com.xiaomi.dayu.api.constants.SideEnum;
import lombok.Data;

import java.io.Serializable;

public class DubboSearchReq implements Serializable {
    /**
     * consumer,provider
     */
    private SideEnum side ;
    /**
     * 应用名
     */
    private String application;
    /**
     * 服务名
     */
    private String serviceName;
    /**
     * 是否包含instance
     */
    private boolean includeInstance;

    private boolean includeMetadata;


    private int pageSize;
    private int pageNo;

    private String fullService;

    public SideEnum getSide() {
        return side;
    }

    public void setSide(SideEnum side) {
        this.side = side;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public boolean isIncludeInstance() {
        return includeInstance;
    }

    public void setIncludeInstance(boolean includeInstance) {
        this.includeInstance = includeInstance;
    }

    public boolean isIncludeMetadata() {
        return includeMetadata;
    }

    public void setIncludeMetadata(boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public String getFullService() {
        return fullService;
    }

    public void setFullService(String fullService) {
        this.fullService = fullService;
    }
}
