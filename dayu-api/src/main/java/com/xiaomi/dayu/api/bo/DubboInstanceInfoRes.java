package com.xiaomi.dayu.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class DubboInstanceInfoRes implements Serializable {

    private String instanceId;

    private String serviceName;

    private String dubboService;

    private String dubboGroup;

    private String dubboVersion;

    private String fullService;

    private String application;

    private String side;

    private String ip;

    private Integer port;

    private Double weight;

    private Boolean healthy;

    private Boolean enabled;

    private Boolean ephemeral;

    private String clusterName;

    private String namespaceId;

    private String groupName;

    private String metadata;

    private String md5;

    private Date lastBeatTime;

    private Date createTime;

    private Date updateTime;
}
