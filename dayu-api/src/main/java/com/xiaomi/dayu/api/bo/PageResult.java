package com.xiaomi.dayu.api.bo;

import java.io.Serializable;
import java.util.List;

public class PageResult<T> implements Serializable {
    private List<T> data;
    private long totalNum;
    private int pageNo;
    private int pageSize;

    public PageResult() {
    }

    public PageResult(long totalNum, int pageNo, int pageSize) {
        this.totalNum = totalNum;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public PageResult(List<T> data, long totalNum, int pageNo, int pageSize) {
        this.data = data;
        this.totalNum = totalNum;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(long totalNum) {
        this.totalNum = totalNum;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
