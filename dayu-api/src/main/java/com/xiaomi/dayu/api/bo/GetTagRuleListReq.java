/*
 * Copyright 2020 XiaoMi.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at the following link.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.api.bo;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by dongzhenxing on 2023/3/20 7:22 PM
 */
@Data
public class GetTagRuleListReq implements Serializable {
    @HttpApiDocClassDefine(value = "页数",description = "页数",defaultValue = "1")
    private int page;
    @HttpApiDocClassDefine(value = "页码",description = "页码",defaultValue = "15")
    private int pageSize;
    @HttpApiDocClassDefine(value = "所属应用id",description = "所属应用id",defaultValue = "166")
    private int appId;
    @HttpApiDocClassDefine(value = "创建人",description = "创建人",defaultValue = "dongzhenxing")
    private String creator;
    @HttpApiDocClassDefine(value = "标签名",description = "标签名",defaultValue = "dzxTag")
    private String routeTag;
}
