package com.xiaomi.dayu.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Type DubboServiceAndAppInfoRes.java
 * @Desc
 * @date 2024/3/6 17:06
 */
public class DubboServiceAndAppInfoRes implements Serializable {
    private String department;
    private List<String> developers;

    private String dubboService;
    private String application;
    private String side;

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public List<String> getDevelopers() {
        return developers;
    }

    public void setDevelopers(List<String> developers) {
        this.developers = developers;
    }

    public String getDubboService() {
        return dubboService;
    }

    public void setDubboService(String dubboService) {
        this.dubboService = dubboService;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }
}
