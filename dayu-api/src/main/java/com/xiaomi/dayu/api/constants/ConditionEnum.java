/*
 * Copyright 2020 XiaoMi.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at the following link.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.api.constants;


/**
 * Created by dongzhenxing on 2023/3/17 3:38 PM
 */
public enum ConditionEnum {

    EQ(1),
    CONTAIN(2),
    BIGGER(3),
    SMLLER(4),
    BIGGER_AND_EQ(5),
    SMALLER_AND_EQ(6),
    NOT_CONTAIN(7),
    NOT_EQ(8);

    final int code;

    ConditionEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
