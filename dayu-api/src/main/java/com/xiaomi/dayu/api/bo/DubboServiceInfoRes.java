package com.xiaomi.dayu.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DubboServiceInfoRes implements Serializable {


    private String serviceName;

    private String dubboService;

    private String dubboGroup;

    private String dubboVersion;

    private String fullService;

    private String application;

    private String side;

    private String clusterName;

    private String namespaceId;

    private String groupName;

    private Boolean del;

    private List<DubboInstanceInfoRes> instanceList;



}
