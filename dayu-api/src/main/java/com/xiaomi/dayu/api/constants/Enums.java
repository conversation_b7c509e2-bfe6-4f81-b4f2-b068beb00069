package com.xiaomi.dayu.api.constants;

/**
 * <AUTHOR> (yang<PERSON><PERSON><PERSON>@xiaomi.com)
 * @version 1.0
 * @since 2022/1/4
 */
public class Enums {

    public enum ConfigType {

        APP(1),
        DOMAIN(2),
        GLOBAL(3)
        ;

        private final int value;

        ConfigType(int value) {
            this.value = value;
        }

        public int getValue () {
            return this.value;
        }

        public static boolean requiresAppName (Integer value) {
            return !Integer.valueOf(GLOBAL.getValue()).equals(value);
        }
    }

    public enum OperateType {

        READ(0,"读"),
        CREATE(1,"创建"),
        DELETE(2,"删除"),
        UPDATE(3,"更新")
        ;

        private final int value;
        private final String desc;

        OperateType(int value,String desc) {
            this.value = value;
            this.desc = desc;
        }

        public int getValue () {
            return this.value;
        }
        public String getDesc () {
            return this.desc;
        }

        public static OperateType getOperateType(int value){
            for (OperateType v : OperateType.values()) {
                if(v.getValue() == value){
                    return v;
                }
            }
            throw new IllegalStateException("Unexpected  OperateType value: " + value);
        }
    }
    public enum ChannelType {

        HTTP(1),
        DUBBO(2);

        private final int value;

        ChannelType(int value) {
            this.value = value;
        }

        public int getValue () {
            return this.value;
        }
    }
}
