package com.xiaomi.dayu.api.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.dayu.api.constants.Enums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/1/10
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class PublishConfigReq implements Serializable {
    /**
     * dataId 必填
     */
    private String dataId;
    /**
     * dataId 必填 默认 DEFAULT_GROUP
     */
    private String group;
    /**
     * tenant 非必填，默认public
     */
    private String tenant;
    /**
     * 配置内容
     */
    private String content;
    private String type;
    private  String appName;
    private Long id;
    /**
     * @see Enums.OperateType
     */
    private Integer operateType;
    private String envId;
    private String envName;
    /**
     * @see Enums.ConfigType
     */
    private Integer configType;
    /**
     * 操作用户名
     */
    @JsonProperty("src_user")
    private String username;
    private Integer channelType;

    private String remark;
    /**
     * 审批人列表
     */
    private String approver;
    /**
     * 审批备注
     */
    private String applyRemark;

/*    public PublishConfigReq() {
    }*/
}
