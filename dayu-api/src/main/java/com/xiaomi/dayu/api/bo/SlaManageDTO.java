package com.xiaomi.dayu.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SlaManageDTO implements Serializable {
    private Long id;
    private String appName;
    private Integer appId;
    private String type;
    private String className;
    private String methodName;
    private String dubboGroup;
    private String dubboVersion;
    private String operator;
    private List<SlaTypeDTO> slaContent;
    private Date updateTime;
    private Date createTime;

}
