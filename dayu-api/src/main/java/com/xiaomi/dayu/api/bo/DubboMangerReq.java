package com.xiaomi.dayu.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class DubboMangerReq  implements Serializable {
    /**
     * ip集合
     */
    private List<String> ips;
    /**
     * 应用名
     */
    private String appName;
    /**
     * 应用Id
     */
    private Integer appId;
    /**
     * 下线：false,上线：true;
     */
    private Boolean enabled;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 服务名
     */
    private String serviceName;
}
