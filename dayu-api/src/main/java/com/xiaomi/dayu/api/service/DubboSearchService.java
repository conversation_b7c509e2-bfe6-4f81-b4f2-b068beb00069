package com.xiaomi.dayu.api.service;

import com.xiaomi.dayu.api.bo.*;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface DubboSearchService {
    Result<PageResult<DubboServiceInfoRes>> searchService(DubboSearchReq requestBo);

    Result<Collection<DubboServiceAndAppInfoRes>> serviceInfo(DubboSearchReq requestBo);

    Result<PageResult<DubboServiceInfoRes>> searchServiceForHera(DubboSearchReq requestBo);

    Result<Map<String, Map<String,ServiceRelationDTO>>> queryServiceRelations(ServiceRelationReq relationReq);


    Result<String> queryServiceRelationsMarkDown(ServiceRelationReq relationReq);

    Result<Map<String, Map<String, ServiceRelationDTO>>> queryConsumerRelations(ServiceRelationReq relationReq);
}
