/*
 * Copyright 2020 XiaoMi.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at the following link.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package swimlane.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by dongzhenxing on 2023/4/19 5:24 PM
 */
@Data
public class MQConfBo implements Serializable {
    /**
     * 是否启用 MQ 的泳道功能
     */
    private boolean enable;

    /**
     * 使用泳道功能的 topic 列表
     */
    private List<String> topicList;

}
