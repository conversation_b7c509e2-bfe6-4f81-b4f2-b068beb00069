package swimlane.bo;

import java.io.Serializable;
import java.util.List;

public class SwimLaneInfoBo implements Serializable {
    private String name;
    private boolean status;
    private String flowControlTag;
    private List<AppEnvDTO> appEnvs;
    private SwimLaneConditionBo swimLaneCondition;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<AppEnvDTO> getAppEnvs() {
        return appEnvs;
    }

    public void setAppEnvs(List<AppEnvDTO> appEnvs) {
        this.appEnvs = appEnvs;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getFlowControlTag() {
        return flowControlTag;
    }

    public void setFlowControlTag(String flowControlTag) {
        this.flowControlTag = flowControlTag;
    }

    public SwimLaneConditionBo getSwimLaneCondition() {
        return swimLaneCondition;
    }

    public void setSwimLaneCondition(SwimLaneConditionBo swimLaneCondition) {
        this.swimLaneCondition = swimLaneCondition;
    }

    @Override
    public String toString() {
        return "SwimLaneInfoBo{" +
                "name='" + name + '\'' +
                ", status=" + status +
                ", flowControlTag='" + flowControlTag + '\'' +
                ", appEnvs=" + appEnvs +
                ", swimLaneCondition=" + swimLaneCondition +
                '}';
    }
}
