/*
 * Copyright 2020 XiaoMi.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at the following link.
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package swimlane.bo;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by dongzhenxing on 2023/3/17 8:19 PM
 */
@Data
public class TagRuleInfoDTO implements Serializable {
    /**
     * 该条规则id
     */
    @HttpApiDocClassDefine(value = "规则id",description = "规则标签的id",defaultValue = "63542")
    private int ruleId;

    /**
     * 所属应用id
     */
    @HttpApiDocClassDefine(value = "所属应用id",description = "所属应用id",defaultValue = "642")
    private int appId;

    /**
     * 所属应用名
     */
    @HttpApiDocClassDefine(value = "所属应用名",description = "所属应用名",defaultValue = "642")
    private String appName;

    /**
     * 标签
     */
    @HttpApiDocClassDefine(value = "路由标签",description = "匹配后打的标签",defaultValue = "dzx-tag")
    private String routeTag;

    @HttpApiDocClassDefine(value = "创建人",description = "创建人",defaultValue = "dzx")
    private String creator;

    @HttpApiDocClassDefine(value = "最后更新人",description = "最后更新人",defaultValue = "dzx")
    private String updater;

    @HttpApiDocClassDefine(value = "创建时间",description = "创建时间",defaultValue = "2124124")
    private long ctime;


    @HttpApiDocClassDefine(value = "最后更新时间",description = "最后更新时间",defaultValue = "2124124")
    private long utime;

    /**
     * 1 同时满足
     * 2 满足任一
     */
    @HttpApiDocClassDefine(value = "规则匹配类型",description = "规则匹配类型 1 同时满足 2 满足任一",defaultValue = "1")
    private Integer conditionType;

    /**
     * 匹配的条件
     */
    private List<ConditionPairBo> conditionList;
}
