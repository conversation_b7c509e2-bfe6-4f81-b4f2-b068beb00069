package swimlane.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Type ConditionPairCheckBo.java
 * @Desc
 * @date 2025/3/17 10:52
 */
public class ConditionPairCheckBo implements Serializable {
	private ConditionPairBo conditionPairBo;
	private String jsonParam;

	public ConditionPairBo getConditionPairBo() {
		return conditionPairBo;
	}

	public void setConditionPairBo(ConditionPairBo conditionPairBo) {
		this.conditionPairBo = conditionPairBo;
	}

	public String getJsonParam() {
		return jsonParam;
	}

	public void setJsonParam(String jsonParam) {
		this.jsonParam = jsonParam;
	}
}
