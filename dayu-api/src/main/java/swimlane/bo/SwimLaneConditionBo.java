package swimlane.bo;

import java.io.Serializable;
import java.util.List;

public class SwimLaneConditionBo implements Serializable {
    /**
     * 参数对
     */
    private List<ConditionPairBo> conditionPairs;

    /**
     * 1 同时满足
     * 2 满足任一
     */
    private Integer conditionType;

    public List<ConditionPairBo> getConditionPairs() {
        return conditionPairs;
    }

    public void setConditionPairs(List<ConditionPairBo> conditionPairs) {
        this.conditionPairs = conditionPairs;
    }

    public Integer getConditionType() {
        return conditionType;
    }

    public void setConditionType(Integer conditionType) {
        this.conditionType = conditionType;
    }
}
