package swimlane.bo;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

@Data
public class ConditionPairBo implements Serializable {

    /**
     * 1 Parameter
     * 2 Header
     * 3 RpcContext
     */
    @HttpApiDocClassDefine(value = "条件类型",description = "本条条件类型 1 Parameter 2 Header 3 RpcContext",defaultValue = "1")
    private Integer paramType;

    @HttpApiDocClassDefine(value = "参数名",description = "参数名",defaultValue = "dTag")
    private String paramName;

    /**
     * 取值表达式
     */
    @HttpApiDocClassDefine(value = "目标取值表达式",ignore = true)
    private String parseExpr;


    /**
     * 源取值表达式
     */
    @HttpApiDocClassDefine(value = "源取值表达式",description = "paramType为1，Parameter时用的取值表达式",defaultValue = "{data}{goodId::int}")
    private String oriParseExpr;

    /**
     * 1 =
     * 2 包含
     * 3 >
     * 4 <
     */
    @HttpApiDocClassDefine(value = "比较操作",description = "比较判断的操作类型 1 =，2 包含，3 >，4 <，5 >=，6 <=，7 不包含，8 !=",defaultValue = "1")
    private Integer op;

    @HttpApiDocClassDefine(value = "目标值",description = "目标值",defaultValue = "abTest")
    private String paramValue;


    @Override
    public String toString() {
        return "ConditionPairBo{" +
                "paramType=" + paramType +
                ", paramName='" + paramName + '\'' +
                ", parseExpr='" + parseExpr + '\'' +
                ", op=" + op +
                ", paramValue='" + paramValue + '\'' +
                '}';
    }
}
