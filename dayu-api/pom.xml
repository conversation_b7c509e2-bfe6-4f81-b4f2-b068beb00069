<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xiaomi</groupId>
        <artifactId>dayu</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dayu-api</artifactId>
    <version>${revision}</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>youpin-infra-rpc</artifactId>
            <version>1.12-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>drizzle-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
<!--        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-rpc-api</artifactId>
            <version>2.7.0-SNAPSHOT</version>
        </dependency>-->

        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>http-docs-core</artifactId>
            <version>2.7.12-mone-v5-SNAPSHOT</version>
        </dependency>

    </dependencies>
</project>