<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xiaomi</groupId>
		<artifactId>dayu</artifactId>
		<version>${revision}</version>
	</parent>
	<groupId>com.xiaomi</groupId>
	<artifactId>dayu-common</artifactId>
	<version>${revision}</version>
	<dependencies>
		<dependency>
			<groupId>com.alibaba.nacos</groupId>
			<artifactId>nacos-client</artifactId>
			<version>${nacos-version}</version>
		</dependency>
		<!-- spring mvc -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<version>2.0.6.RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-to-slf4j</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.xiaomi</groupId>
			<artifactId>dayu-api</artifactId>
			<version>${revision}</version>
		</dependency>
<!--		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>2.17.0</version>
		</dependency>-->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-to-slf4j</artifactId>
			<version>2.17.2</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>com.xiaomi.youpin</groupId>
			<artifactId>hermes-api</artifactId>
			<version>0.0.3-SNAPSHOT</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<artifactId>youpin-infra-rpc</artifactId>
					<groupId>com.xiaomi.youpin</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>
		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
			<version>2.1.4.2-mone-SNAPSHOT</version>
			<!--<exclusions>
				<exclusion>
					<groupId>com.alibaba.csp</groupId>
					<artifactId>sentinel-dubbo-adapter</artifactId>
				</exclusion>
			</exclusions>-->
		</dependency>

		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>3.3.0</version>
		</dependency>
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>miline-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>run.mone</groupId>
					<artifactId>batch-deploy-operator-api</artifactId>
				</exclusion>
			</exclusions>
        </dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.12.0</version>
		</dependency>
		<dependency>
			<groupId>com.xiaomi.youpin</groupId>
			<artifactId>antlr</artifactId>
			<version>1.4-SNAPSHOT</version>
		</dependency>
	</dependencies>


	<build>
	</build>


</project>
