package com.xiaomi.dayu.wrapper;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;

public class DubboAdminHttpServletResponseWrapper extends HttpServletResponseWrapper {
    private int status = 0;
    /**
     * Constructs a response adaptor wrapping the given response.
     *
     * @param response The response to be wrapped
     * @throws IllegalArgumentException if the response is null
     */
    public DubboAdminHttpServletResponseWrapper(HttpServletResponse response) {
        super(response);
    }

    @Override
    public void setStatus(int sc) {
        this.status = sc;
        super.setStatus(sc);
    }

    public int getStatus() {
        return status;
    }
    public int getRespStatus() {
        if(status == 0){
            status = super.getStatus();
        }
        return status;
    }
}
