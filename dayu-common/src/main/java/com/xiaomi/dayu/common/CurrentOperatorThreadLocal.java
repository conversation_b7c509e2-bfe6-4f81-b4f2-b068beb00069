package com.xiaomi.dayu.common;

public class CurrentOperatorThreadLocal {
    private static final ThreadLocal<String> currentOperatorThreadLocal= new ThreadLocal<>();
    public static String getCurrentOperator() {
        return currentOperatorThreadLocal.get();
    }

    public static void setCurrentOperator(String operator){
        currentOperatorThreadLocal.set(operator);
    }

    public static void removeCurrentOperator(){
        currentOperatorThreadLocal.remove();
    }
}
