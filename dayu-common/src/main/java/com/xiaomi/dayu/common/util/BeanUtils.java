package com.xiaomi.dayu.common.util;

import net.sf.cglib.beans.BeanMap;

import java.util.Map;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/3/23
 */
public class BeanUtils {

    public static <T> Map<String, Object> beanToMap(T bean) {
        return BeanMap.create(bean);
    }

    public static <T> T mapToBean(Map<String, Object> map, T bean) {
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(map);
        return bean;
    }
}
