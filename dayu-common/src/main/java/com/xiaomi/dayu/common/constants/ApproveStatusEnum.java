package com.xiaomi.dayu.common.constants;

public enum ApproveStatusEnum {
    //状态（10：提交，20：通过，30：驳回，40：取消）
    UN_APPROVE(10,"提交"), // 审核中
    PASS(20,"通过"), // 已通过
    REFUSE(30,"驳回"),// 驳回
    CANCEL(40,"取消");// 取消
    private int code;
    private String desc;

    ApproveStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApproveStatusEnum getApproveStatus(int code){
        for (ApproveStatusEnum value : ApproveStatusEnum.values()) {
            if(value.getCode() == code){
                return value;
            }
        }
        throw new IllegalStateException("Unexpected  ApproveStatus value: " + code);
    }
}
