package com.xiaomi.dayu.common.util;

import com.xiaomi.dayu.common.ResultResponse;
import com.xiaomi.dayu.common.interfaces.Success;
import org.slf4j.Logger;

import java.util.concurrent.Callable;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * <AUTHOR> (yang<PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/1/10
 */
public class RetryUtil {

    public static Success callWithRetries(Callable<Success> caller, int maxTries, Logger logger, String logPrefix) {
        return callWithRetries(caller, maxTries, logger, logPrefix, 1000L);
    }

    public static Success callWithRetries(Callable<Success> caller, int maxTries, Logger logger, String logPrefix, long interval) {
        checkArgument(maxTries > 0 && interval > 0 && interval < 5000L,
                String.format("RetryUtil callWithRetries invalid input maxTries %d, interval %d", maxTries, interval));
        if (logPrefix == null) {
            logPrefix = "";
        }
        logPrefix += " callWithRetries ";
        maxTries = Math.min(maxTries, 3);

        for (int count = 0; count < maxTries; count++) {
            try {
                Success res = caller.call();
                if (res != null && res.isSuccess()) {
                    return res;
                }
            } catch (Exception exception) {
                logger.error(logPrefix, exception);
            }
            if (count < maxTries - 1) {
                logger.info(logPrefix + " retrying count: {}", count + 1);

                try {
                    Thread.sleep(interval * (count + 1));
                } catch (InterruptedException ignored) {
                }
            }
        }
        logger.error(logPrefix + " reached the maximum retry count: {}", maxTries);
        ResultResponse<Object> resp = new ResultResponse<>();
        resp.setSuccess(false);
        return resp;
    }
}
