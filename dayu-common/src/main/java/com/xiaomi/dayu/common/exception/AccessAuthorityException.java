package com.xiaomi.dayu.common.exception;

public class AccessAuthorityException extends RuntimeException{
    private ExceptionEnum exceptionEnum;
    public AccessAuthorityException(String message) {
        super(message);
    }
    public AccessAuthorityException(ExceptionEnum exceptionEnum){
        super(exceptionEnum.getMessage());
        this.exceptionEnum = exceptionEnum;
    }
    public AccessAuthorityException(String message, Throwable cause) {
        super(message, cause);
    }

    protected AccessAuthorityException() {
        super();
    }

    public ExceptionEnum getExceptionEnum() {
        return exceptionEnum;
    }
}
