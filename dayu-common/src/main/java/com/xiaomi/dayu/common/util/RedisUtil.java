package com.xiaomi.dayu.common.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Set;

@Component
public class RedisUtil {

    @Autowired
    private JedisPool jedisPool;

    /**
     * 根据传入Key获取指定Value
     */
    public String get(String key) {
        Jedis jedis = null;
        String value;
        try {
            jedis = jedisPool.getResource();
            value = jedis.get(key);
        } catch (Exception e) {
            return "0";
        } finally {
            jedis.close();
        }
        return value;
    }

    /**
     * 根据传入Key获取指定Value
     */
    public String increase(String key) {
        Jedis jedis = null;
        Long value;
        try {
            jedis = jedisPool.getResource();
            value = jedis.incrBy(key,1);
        } catch (Exception e) {
            return "0";
        } finally {
            jedis.close();
        }
        return String.valueOf(value);
    }

    /**
     * 校验Key值是否存在
     */
    public Boolean exists(String key) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return jedis.exists(key);
        } catch (Exception e) {
            return false;
        } finally {
            jedis.close();
        }
    }

    /**
     * 删除指定Key-Value
     */
    public Long del(String key) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return jedis.del(key);
        } catch (Exception e) {
            return 0L;
        } finally {
            jedis.close();
        }
    }
    /**
     * set
     */
    public Long sadd(String key,String member) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return jedis.sadd(key,member);
        } catch (Exception e) {
            return 0L;
        } finally {
            jedis.close();
        }
    }
    /**
     * set
     */
    public Set<String> smembers(String key) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return jedis.smembers(key);
        } catch (Exception e) {
            return null;
        } finally {
            jedis.close();
        }
    }
    /**
     * set
     */
    public String set(String key,String value) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return jedis.set(key,value);
        } catch (Exception e) {
            return null;
        } finally {
            jedis.close();
        }
    }
}