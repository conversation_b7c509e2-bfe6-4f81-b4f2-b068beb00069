package com.xiaomi.dayu.common.util;

import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.common.exception.PermissionDeniedException;
import com.xiaomi.dayu.model.UserInfo;

public class CheckPermissUtil {
    public static void checkPermissByAppName(String appName){
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        if(!userInfo.isAdmin() && !userInfo.getApplicationNames().contains(appName)){
            throw new PermissionDeniedException("用户没有权限访问该应用");
        }
    }
}
