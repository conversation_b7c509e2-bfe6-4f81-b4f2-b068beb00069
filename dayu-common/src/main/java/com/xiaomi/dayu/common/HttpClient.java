
package com.xiaomi.dayu.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.common.Constants;
import com.alibaba.nacos.common.utils.HttpMethod;
import com.alibaba.nacos.common.utils.IoUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.net.HttpHeaders;
import com.google.gson.JsonObject;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.common.util.AESEncryptUtils;
import com.xiaomi.dayu.common.util.BeanUtils;
import com.xiaomi.dayu.model.UserInfo;
import com.xiaomi.dayu.wrapper.DubboAdminHttpServletRequestWrapper;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.zip.GZIPInputStream;

import static com.alibaba.nacos.client.utils.LogUtils.NAMING_LOGGER;
import static com.google.common.base.Preconditions.checkArgument;
import static com.xiaomi.dayu.common.util.ParseUtils.checkFormType;

public class HttpClient {
    public static final String UTF_8="UTF-8";
    public static final int TIME_OUT_MILLIS =  50000;
    public static final int CON_TIME_OUT_MILLIS = 3000;
    private static final boolean ENABLE_HTTPS = true;

    public static String SENTINEL_URL = "";
    public static String NACOS_URL = "";
    public static String IAUTH_URL = "";
    public static String ARTHAS_URL = "";

    public static void setSentinelUrl(String sentinelUrl) {
        SENTINEL_URL = sentinelUrl;
    }

    public static void setNacosUrl(String nacosUrl) {
        NACOS_URL = nacosUrl;
    }

    public static void setIauthUrl(String iauthUrl) {
        IAUTH_URL = iauthUrl;
    }

    public static void setArthasUrl(String arthasUrl) {
        ARTHAS_URL = arthasUrl;
    }

    public static String NACOS_GET_CONFIG_URL="/nacos/v1/cs/configs";
    public static String HTTP="http://";
    public static String HTTPS="https://";
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClient.class);



    public static String getPrefix() {
        if (ENABLE_HTTPS) {
            return HTTPS;
        }

        return HTTP;

    }
    public static String queryFullDeptByUid(String idmUrl,String idmAppId,String idmAppKey,String uid) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("uid", uid);
        String data = queryIdmSignData(idmAppId,idmAppKey,paramMap);
        String url = idmUrl + "/api/department/queryFullDeptByUid";
        try {
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("data",data);
            HttpResult httpResult = request(url, null, paramValues, null, UTF_8, RequestMethod.POST.name());
            return httpResult.content;
        } catch (Exception e) {
            LOGGER.error(String.format("IDM query userDetail exception,url:%s,phone:%s,data:%s", url, uid, data), e);
        }
        LOGGER.error("IDM query userId by phone error,contact us");
        return null;
    }
    public static String findUidByUserName(String idmUrl,String idmAppId,String idmAppKey,String userName) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userNames", userName);
        paramMap.put("accountType", "employee");
        paramMap.put("accountState", "2");
        String data = queryIdmSignData(idmAppId,idmAppKey,paramMap);
        String url = idmUrl + "/api/user/batch/findUidsByUserNames";
        try {
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("data",data);
            HttpResult httpResult = request(url, null, paramValues, null, UTF_8, RequestMethod.POST.name());
            return httpResult.content;
        } catch (Exception e) {
            LOGGER.error(String.format("IDM query userDetail exception,url:%s,userName:%s,data:%s", url, userName, data), e);
        }
        LOGGER.error("IDM query userId by phone error,contact us");
        return null;
    }
    public static  String queryIdmSignData(String idmApp_id,String idmAppKey,Map<String, Object> paramMap) {
        String body = JSON.toJSONString(paramMap);
        String sign = DigestUtils.md5Hex(idmApp_id + body + idmAppKey).toUpperCase();
        JsonObject json = new JsonObject();
        JsonObject header = new JsonObject();
        header.addProperty("appid", idmApp_id);
        header.addProperty("sign", sign);
        json.add("header", header);
        json.addProperty("body", body);
        return Base64Utils.encodeToString(json.toString().getBytes());
    }

    public static String getNacosConfigAppName(String tenant,String group,String dataId){
        String nacosConfig = getNacosConfig(tenant, group, dataId);
        if(StringUtils.isNotBlank(nacosConfig)){
            JSONObject jsonObject = JSONObject.parseObject(nacosConfig);
            return jsonObject.getString("appName");
        }
        return null;
    }

    public static String getNacosConfig(String tenant,String group,String dataId){
        HashMap<String, String> params = new HashMap<>();
        params.put("tenant",tenant);
        params.put("group",group);
        params.put("dataId",dataId);
        params.put("show","all");
        HttpResult httpResult = request(HTTP+NACOS_URL+NACOS_GET_CONFIG_URL, null, params, null, UTF_8, RequestMethod.GET.name());
        return httpResult.content;
    }
    public static ResultResponse<Object> nacosProxyHttp(HttpServletRequest req){
        return nacosProxyHttp(req,null,null,null);
    }
    public static ResultResponse<Object> nacosProxyHttp(HttpServletRequest req,String requestMethod,HashMap<String, String> params){
       return nacosProxyHttp( req, null, requestMethod,params);
    }
    public static ResultResponse<Object> nacosProxyHttp(HttpServletRequest req,String requestUri,String requestMethod,HashMap<String, String> params){

        String url;
        if(StringUtils.isNotBlank(requestUri)){
            url = HTTP+NACOS_URL+requestUri;
        }else{
            //去掉/api/{env}
            String requestURI = req.getRequestURI();
            requestURI = requestURI.substring(requestURI.indexOf("/", 3));
            url = HTTP+NACOS_URL+requestURI;
        }
        List<String> headers = new ArrayList<>();
/*        Enumeration<String> headerNames = req.getHeaderNames();
        while(headerNames.hasMoreElements()){
            String key = headerNames.nextElement();
            headers.add(key);
            headers.add(req.getHeader(key));
        }*/
        Map<String, String> paramValues = new HashMap<>(req.getParameterMap().size());
        if(req instanceof DubboAdminHttpServletRequestWrapper){
            DubboAdminHttpServletRequestWrapper request =(DubboAdminHttpServletRequestWrapper)req;
            if(url.contains("/v1/cs/configs")){
                request.getParams().remove("pageNo");
                request.getParams().remove("pageSize");
            }
            request.getParams().forEach((key,value)->{
                paramValues.put(key,Joiner.on(",").join(value));
            });
        }
        if(MapUtils.isNotEmpty(params)){
            paramValues.putAll(params);
        }
        String method = req.getMethod();
        if(StringUtils.isNotBlank(requestMethod)){
            method = requestMethod;
        }

        String body = getBody(req);
        LOGGER.info("调用httpclient,url={},params={},body={}",url,JSON.toJSON(paramValues).toString(),body);
        HttpResult httpResult = request(url, headers, paramValues, body, UTF_8, method);
        LOGGER.info("调用httpclient,httpResult={}",JSON.toJSONString(httpResult));
        if(httpResult.code == 200 ){
            if(httpResult.content.contains("{")){
                return ResultResponse.success(JSON.parseObject(httpResult.content));
            }else{
                return ResultResponse.success(httpResult.content);
            }
        }else{
            return ResultResponse.fail(500,httpResult.content);
        }
    }

    public static ResultResponse<Object> nacosRequest(Object reqBody, String requestUri, String requestMethod) throws JsonProcessingException {
        return nacosRequest(reqBody, requestUri, requestMethod, null);
    }

    public static ResultResponse<Object> nacosRequest(Object reqBody, String requestUri, String requestMethod, HashMap<String, String> params) throws JsonProcessingException {
        return nacosRequest(reqBody, requestUri, requestMethod, params, null);
    }

    public static ResultResponse<Object> nacosRequest(Object reqBody, String requestUri, String requestMethod, HashMap<String, String> params, List<String> headers) throws JsonProcessingException {
        return urlRequest(reqBody, HTTP + NACOS_URL+ "/nacos" + requestUri, requestMethod, params, headers);
    }

    public static ResultResponse<Object> urlRequest(Object reqBody, String url, String requestMethod, HashMap<String, String> params, List<String> headers) throws JsonProcessingException {
        checkArgument(StringUtils.isNotBlank(url), "urlRequest requestUri is blank");
        checkArgument(StringUtils.isNotBlank(requestMethod), "urlRequest requestMethod is blank");

        ObjectMapper objectMapper = new ObjectMapper();
        String body = "";
        Map<String, String> paramValues = new HashMap<>();

        if (checkFormType(headers)) {
            Map<String, Object> bMap = BeanUtils.beanToMap(reqBody);

            for (Map.Entry<String, Object> entry : bMap.entrySet()) {
                if (entry.getKey() == null || entry.getValue() == null) {
                    continue;
                }
                paramValues.put(entry.getKey(), String.valueOf(entry.getValue()));
            }
        } else {
            body = reqBody instanceof String ? (String)reqBody : objectMapper.writeValueAsString(reqBody);
        }
        if(MapUtils.isNotEmpty(params)){
            paramValues.putAll(params);
        }
        LOGGER.info("调用httpclient,url={},params={},body={}",url,objectMapper.writeValueAsString(paramValues),body);
        HttpResult httpResult = request(url, headers, paramValues, body, UTF_8, requestMethod);

        if (httpResult.code >= 400) {
            LOGGER.error("调用httpclient,httpResult={}", objectMapper.writeValueAsString(httpResult));
            return ResultResponse.fail(500, httpResult.content);
        }
        LOGGER.info("调用httpclient,httpResult={}", objectMapper.writeValueAsString(httpResult));

        if (httpResult.content.startsWith("{") || httpResult.content.startsWith("[")) {
            return ResultResponse.success(JSON.parseObject(httpResult.content));
        }
        return ResultResponse.success(httpResult.content);
    }

    public static ResultResponse<String> iauthProxyHttp(HttpServletRequest request,String requestUri,RequestMethod requestMethod,HashMap<String,String> params,HashMap<String,Object> bodyParams,UserInfo userInfo){
        String requestURI="";
        if(StringUtils.isBlank(requestUri)){
            requestURI = request.getRequestURI();
            //去掉/api/iauth
            requestURI = requestURI.substring(requestURI.indexOf("iauth")+5);
        }else{
            requestURI = requestUri;
        }

        String url = IAUTH_URL+requestURI;
        List<String> headers = new ArrayList<>();
        headers.add("Authorization");
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("userName", userInfo.getUserName());
        headers.add(AESEncryptUtils.encryptIauth(JSON.toJSONString(hashMap)));
        headers.add("Content-Type");
        headers.add("application/json; charset=utf-8");

        Map<String, String> paramValues = new HashMap<>(request.getParameterMap().size());
        if(MapUtils.isEmpty(params)){
            request.getParameterMap().forEach((key,value)->{
                paramValues.put(key,Joiner.on(",").join(value));
            });
        }else{
            paramValues.putAll(params);
        }
        paramValues.put("platform","china-area");
        String method = null;
        if(requestMethod == null){
            method = request.getMethod();
        }else{
            method = requestMethod.name();
        }
        String body = null;
        if(MapUtils.isNotEmpty(bodyParams)){
            bodyParams.put("platform","china-area");
            body = JSON.toJSONString(bodyParams);
        }else{
            body = getBody(request);
        }
        LOGGER.info("调用iauthProxyHttp,url={},method={},headers={},params={},body={}",url,method,JSON.toJSONString(headers),JSON.toJSONString(paramValues),JSON.toJSONString(bodyParams));
        HttpResult httpResult = requestIauth(url, headers, paramValues, body, UTF_8, method);
        LOGGER.info("调用iauthProxyHttp,httpResult={}",JSON.toJSONString(httpResult));
        JSONObject jsonObject = JSON.parseObject(httpResult.content);
        if(jsonObject != null){
            Object codeStr = jsonObject.get("code");
            int code = 0;
            if(codeStr != null){
                code = (Integer)codeStr;
            }
            String msg = jsonObject.getString("msg");
            String data = jsonObject.getString("data");
            Boolean success = jsonObject.getBooleanValue("success");
            return new ResultResponse<String>(success,code,msg,data);
        }else{
            return ResultResponse.success(null);
        }
    }
    public static ResultResponse<String> iauthProxyHttp(HttpServletRequest request,String requestUri,RequestMethod requestMethod,HashMap<String,String> params,HashMap<String,Object> bodyParams) {
        return iauthProxyHttp(request, requestUri, requestMethod, params, bodyParams, UserInfoThreadLocal.getUserInfo());
    }
    public static ResultResponse<String> iauthProxyHttp(HttpServletRequest request){
        return iauthProxyHttp(request,null,null,null,null);
    }
    public static ResultResponse<String> iauthProxyHttp(HttpServletRequest request,HashMap<String, Object> bodyParams ){
        return iauthProxyHttp(request,null,null,null,bodyParams);
    }
    public static ResultResponse<String> iauthProxyHttp(HttpServletRequest request, String requestUri, RequestMethod method, HashMap<String, Object> bodyParams) {
        return iauthProxyHttp(request,requestUri,method,null,bodyParams);
    }





    public static ResultResponse<Object> sentinelProxyHttp(HttpServletRequest request){
        return sentinelProxyHttp( request,null, null,null,null);
    }
    public static ResultResponse<Object> sentinelProxyHttp(HttpServletRequest request,String requestUrl, Object body){
        return sentinelProxyHttp( request,requestUrl,null, body,null);
    }
    public static ResultResponse<Object> sentinelProxyHttp(HttpServletRequest request,String requestUrl,String requestMethod){
        return sentinelProxyHttp( request,requestUrl,requestMethod, null,null);
    }
    public static ResultResponse<Object> sentinelProxyHttp(HttpServletRequest request,String requestUrl,String requestMethod,Object body){
        return sentinelProxyHttp( request,requestUrl,requestMethod, body,null);
    }
    public static ResultResponse<Object> sentinelProxyHttp(HttpServletRequest request,String requestUrl,String requestMethod,Map<String,String> params){
        return sentinelProxyHttp( request,requestUrl,requestMethod, null,params);
    }

    public static ResultResponse<Object> sentinelProxyHttp(HttpServletRequest request,String requestUrl,String requestMethod, Object body,Map<String,String> params){
        String requestURI;
        if(requestUrl != null){
            requestURI = requestUrl;
        }else{
            requestURI =  request.getRequestURI();
            //去掉/api/{env}
            requestURI = requestURI.substring(requestURI.indexOf("/", 3));
        }
        List<String> headers = new ArrayList<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while(headerNames.hasMoreElements()){
            String key = headerNames.nextElement();
            headers.add(key);
            headers.add(request.getHeader(key));
        }
        //添加sentinel免登录校验head
        headers.add("code");
        headers.add("dubboAdminAccess");
        Map<String, String> paramValues = new HashMap<>();
        String bodyString = null;
        if(params == null && body == null){
            request.getParameterMap().forEach((key,value)->{
                paramValues.put(key,Joiner.on(",").join(value));
            });
        }else if(params != null){
            paramValues.putAll(params);
        }else if(body != null){
            headers.add("Content-Type");
            headers.add("application/json");
            bodyString = JSON.toJSONString(body);
        }
        String url = requestURI.startsWith("/") ? SENTINEL_URL+requestURI: SENTINEL_URL+"/"+requestURI;
        String method = requestMethod != null ? requestMethod:request.getMethod();
        LOGGER.info("调用httpclient,url={},params={},body={},headers={}",url,JSON.toJSON(paramValues).toString(),bodyString,JSON.toJSONString(headers));
        HttpResult httpResult = request(url, headers, paramValues, bodyString, UTF_8, method);
        LOGGER.info("调用httpclient,httpResult={}",JSON.toJSONString(httpResult));
        JSONObject jsonObject = JSON.parseObject(httpResult.content);
        if(jsonObject != null){
            String msg = jsonObject.getString("msg");
            Object data = jsonObject.get("data");
            Boolean success = jsonObject.getBooleanValue("success");
            Object codeStr = jsonObject.get("code");
            int code = 0;
            if(codeStr != null){
                code = (Integer)codeStr;
            }else{
                LOGGER.error("调用httpclient,url={},params={},body={}",url,JSON.toJSON(paramValues).toString(),bodyString);
                LOGGER.error("调用httpclient,httpResult={}",JSON.toJSONString(httpResult));
                throw new ParamValidationException(msg);
            }

            if(code == 0){
                return new ResultResponse<Object>(success,code,msg,data);
            }else{
                LOGGER.error("调用httpclient,url={},params={},body={}",url,JSON.toJSON(paramValues).toString(),bodyString);
                LOGGER.error("调用httpclient,httpResult={}",JSON.toJSONString(httpResult));
                throw new ParamValidationException(msg);
            }

//            return new ResultResponse<Object>(success,code,msg,data);
        }else{
            return ResultResponse.success(null);
        }

    }
    public static String getBody(HttpServletRequest request) {
        BufferedReader br = null;
        String str, wholeStr = "";
        try {
            br = request.getReader();
            while((str = br.readLine()) != null){
                wholeStr += str;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return wholeStr;
    }

    public static HttpResult httpGet(String url, List<String> headers, Map<String, String> paramValues, String encoding) {
        return request(url, headers, paramValues, StringUtils.EMPTY, encoding, HttpMethod.GET);
    }

    public static HttpResult request(String url, List<String> headers, Map<String, String> paramValues, String body, String encoding, String method) {
        HttpURLConnection conn = null;
        int contentLength = 0;

        try {
            String encodedContent = "";

            if (paramValues != null && !paramValues.isEmpty()) {
                encodedContent = encodingParams(paramValues, encoding);
                contentLength = encodedContent.length();

                if (HttpMethod.GET.equals(method) || HttpMethod.DELETE.equals(method)) {
                    url += (StringUtils.isEmpty(encodedContent)) ? "" : ("?" + encodedContent);
                }
            }
            conn = (HttpURLConnection) new URL(url).openConnection();

            setHeaders(conn, headers, encoding);
            conn.setConnectTimeout(CON_TIME_OUT_MILLIS);
            conn.setReadTimeout(TIME_OUT_MILLIS);
            conn.setRequestMethod(method);
            conn.setDoOutput(true);

            if ((HttpMethod.POST.equals(method) || HttpMethod.PUT.equals(method)) && contentLength > 0) {
                conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                byte[] encodedBytes = encodedContent.getBytes();
                conn.setRequestProperty("Content-Length", String.valueOf(encodedBytes.length));
                OutputStream os = null;

                try {
                    os = conn.getOutputStream();
                    os.write(encodedBytes, 0, encodedBytes.length);
                } catch (Exception e) {
                    NAMING_LOGGER.error("HttpClient request failed to write post, contentLength:" + contentLength, e);
                } finally {
                    if (os != null) {
                        os.flush();
                        os.close();
                    }
                }
            } else if (StringUtils.isNotBlank(body)) {
                byte[] b = body.getBytes();
                contentLength = b.length;
                conn.setRequestProperty("Content-Length", String.valueOf(b.length));
                conn.getOutputStream().write(b, 0, b.length);
                conn.getOutputStream().flush();
                conn.getOutputStream().close();
            }
            conn.connect();
            if (NAMING_LOGGER.isDebugEnabled()) {
                NAMING_LOGGER.debug("Request from server: " + url);
            }
            return getResult(conn);
        } catch (Throwable e) {
            try {
                if (conn != null) {
                    NAMING_LOGGER.warn("failed to request " + conn.getURL() + " from "
                        + InetAddress.getByName(conn.getURL().getHost()).getHostAddress());
                }
            } catch (Exception e1) {
                NAMING_LOGGER.error("[NA] failed to request ", e1);
                //ignore
            }
            NAMING_LOGGER.error("[NA] failed to request, contentLength:" + contentLength, e);
            return new HttpResult(500, e.toString(), Collections.<String, String>emptyMap());
        } finally {
            IoUtils.closeQuietly(conn);
        }
    }

    public static HttpResult requestIauth(String url, List<String> headers, Map<String, String> paramValues, String body, String encoding, String method) {
        HttpURLConnection conn = null;
        try {
            String encodedContent = encodingParams(paramValues, encoding);
            url += (StringUtils.isEmpty(encodedContent)) ? "" : ("?" + encodedContent);

            conn = (HttpURLConnection) new URL(url).openConnection();

            setHeaders(conn, headers, encoding);
            conn.setConnectTimeout(CON_TIME_OUT_MILLIS);
            conn.setReadTimeout(TIME_OUT_MILLIS);
            conn.setRequestMethod(method);
            conn.setDoOutput(true);
            if (StringUtils.isNotBlank(body)) {
                byte[] b = body.getBytes();
                conn.setRequestProperty("Content-Length", String.valueOf(b.length));
                conn.getOutputStream().write(b, 0, b.length);
                conn.getOutputStream().flush();
                conn.getOutputStream().close();
            }
            conn.connect();
            if (NAMING_LOGGER.isDebugEnabled()) {
                NAMING_LOGGER.debug("Request from server: " + url);
            }
            return getResult(conn);
        } catch (Exception e) {
            try {
                if (conn != null) {
                    NAMING_LOGGER.warn("failed to request " + conn.getURL() + " from "
                            + InetAddress.getByName(conn.getURL().getHost()).getHostAddress());
                }
            } catch (Exception e1) {
                NAMING_LOGGER.error("[NA] failed to request ", e1);
                //ignore
            }

            NAMING_LOGGER.error("[NA] failed to request ", e);

            return new HttpResult(500, e.toString(), Collections.<String, String>emptyMap());
        } finally {
            IoUtils.closeQuietly(conn);
        }
    }

    private static HttpResult getResult(HttpURLConnection conn) throws IOException {
        int respCode = conn.getResponseCode();

        InputStream inputStream;
        if (HttpURLConnection.HTTP_OK == respCode
            || HttpURLConnection.HTTP_NOT_MODIFIED == respCode
            || Constants.WRITE_REDIRECT_CODE == respCode) {
            inputStream = conn.getInputStream();
        } else {
            inputStream = conn.getErrorStream();
        }

        Map<String, String> respHeaders = new HashMap<String, String>(conn.getHeaderFields().size());
        for (Map.Entry<String, List<String>> entry : conn.getHeaderFields().entrySet()) {
            respHeaders.put(entry.getKey(), entry.getValue().get(0));
        }

        String encodingGzip = "gzip";

        if (encodingGzip.equals(respHeaders.get(HttpHeaders.CONTENT_ENCODING))) {
            inputStream = new GZIPInputStream(inputStream);
        }
        return new HttpResult(respCode, IoUtils.toString(inputStream,getCharset(conn) ), respHeaders);
    }

    private static String getCharset(HttpURLConnection conn) {
        String contentType = conn.getContentType();
        if (StringUtils.isEmpty(contentType)) {
            return "UTF-8";
        }

        String[] values = contentType.split(";");
        if (values.length == 0) {
            return "UTF-8";
        }

        String charset = "UTF-8";
        for (String value : values) {
            value = value.trim();

            if (value.toLowerCase().startsWith("charset=")) {
                charset = value.substring("charset=".length());
            }
        }

        return charset;
    }

    private static void setHeaders(HttpURLConnection conn, List<String> headers, String encoding) {
        if (null != headers) {
            for (Iterator<String> iter = headers.iterator(); iter.hasNext(); ) {
                conn.addRequestProperty(iter.next(), iter.next());
            }
        }

        conn.addRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset="
            + encoding);
        conn.addRequestProperty("Accept-Charset", encoding);
    }

    private static String encodingParams(Map<String, String> params, String encoding)
        throws UnsupportedEncodingException {
        if (null == params || params.isEmpty()) {
            return "";
        }

        params.put("encoding", encoding);
        StringBuilder sb = new StringBuilder();

        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (StringUtils.isEmpty(entry.getValue())) {
                continue;
            }

            sb.append(entry.getKey()).append("=");
            sb.append(URLEncoder.encode(entry.getValue(), encoding));
            sb.append("&");
        }

        if (sb.length() > 0) {
            sb = sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }


    public static class HttpResult {
        final public int code;
        final public String content;
        final private Map<String, String> respHeaders;

        public HttpResult(int code, String content, Map<String, String> respHeaders) {
            this.code = code;
            this.content = content;
            this.respHeaders = respHeaders;
        }

        public String getHeader(String name) {
            return respHeaders.get(name);
        }

    }
}
