package com.xiaomi.dayu.common.util;

import java.util.List;
public class PageResult<T> {
    private List<T> content;
    private int totalPages;
    private int totalElements;
    private int size;
    private int number;

    public PageResult(List<T> content, int totalElements, int size, int number) {
        this.content = content;
        this.totalElements = totalElements;
        this.size = size;
        this.number = number;
        if(totalElements == 0){
            this.totalPages = 0;
        }else if(totalElements % size ==0){
            this.totalPages = totalElements/size;
        }else{
            this.totalPages = totalElements/size+1;
        }
    }

    public List<T> getContent() {
        return content;
    }

    public PageResult setContent(List<T> content) {
        this.content = content;
        return this;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public PageResult setTotalPages(int totalPages) {
        this.totalPages = totalPages;
        return this;
    }

    public int getTotalElements() {
        return totalElements;
    }

    public PageResult setTotalElements(int totalElements) {
        this.totalElements = totalElements;
        return this;
    }

    public int getSize() {
        return size;
    }

    public PageResult setSize(int size) {
        this.size = size;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public PageResult setNumber(int number) {
        this.number = number;
        return this;
    }
}
