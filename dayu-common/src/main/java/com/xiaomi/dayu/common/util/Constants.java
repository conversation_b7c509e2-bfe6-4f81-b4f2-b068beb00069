/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xiaomi.dayu.common.util;


import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

public class Constants {


    public static final String DEFAULT_EXPR_PREX = "params.toList()";

//    public static final String DEFAULT_EXPR_JSON_PREX = "params.json()";
//
//    public static final String EXPR_INT_FLAG = "::int";
//
//    public static final String EXPR_STRING_FLAG = "::string";
//
//    public static final String EXPR_LIST_FLAG = "::list[";
//
//    public static final Pattern EL_PATTERN = Pattern.compile("\\{([^}]*)}");

    public static final String REGISTRY_ADDRESS = "dubbo.registry.address";
    public static final String METADATA_ADDRESS = "dubbo.metadata-report.address";
    public static final String DEFAULT_ROOT = "dubbo";
    public static final String PATH_SEPARATOR = "/";
    public static final String GROUP_KEY = "group";
    public static final String NAMESPACE_KEY = "namespace";
    public static final String USERNAME_KEY = "username";
    public static final String PASSWORD_KEY = "password";
    public static final String CONFIG_KEY = "config" + PATH_SEPARATOR + "dubbo";
    public static final String DUBBO_PROPERTY = "dubbo.properties";
    public static final String PROVIDER_SIDE = "provider";
    public static final String CONSUMER_SIDE = "consumer";
    public static final String CATEGORY_KEY = "category";
    public static final String ROUTERS_CATEGORY = "routers";
    public static final String CONDITION_ROUTE = "condition_route";
    public static final String CONDITION_RULE_SUFFIX = ".condition-router";
    public static final String CONFIGURATOR = "configurators";
    public static final String CONFIGURATOR_RULE_SUFFIX = ".configurators";
    public static final String TAG_ROUTE = "tag_route";

    public static final String MQ_TAG_ROUTE = "mq.tag_route";

    public static final String TAG_RULE_SUFFIX = ".tag-router";

    public static final String MQ_TAG_ROUTE_SUFFIX = ".mq.tag-route";

    public static final String COMPATIBLE_CONFIG = "compatible_config";
    public static final String WEIGHT = "weight";
    public static final String BALANCING = "balancing";
    public static final String SERVICE = "service";
    public static final String CONSUMER_APP = "consumerApp";
    public static final String APPLICATION = "application";
    public static final String PUNCTUATION_POINT = ".";
    public static final String PUNCTUATION_SEPARATOR_POINT = "\\.";
    public static final String INTERROGATION_POINT = "?";
    public static final String ANY_VALUE = "*";
    public static final String PLUS_SIGNS = "+";
    public static final String IP = "ip";
    public static final String INTERFACE_KEY = "interface";
    public static final String DYNAMIC_KEY = "dynamic";
    public static final String CONSUMER_PROTOCOL = "consumer";
    public static final String PROVIDER_PROTOCOL = "provider";
    public static final String ROUTE_PROTOCOL = "route";
    public static final String APPLICATION_KEY = "application";
    public static final String ENABLED_KEY = "enabled";
    public static final String HEALTHY_KEY = "health";
    public static final String RULE_KEY = "rule";
    public static final String ANYHOST_VALUE = "0.0.0.0";
    public static final String OVERRIDE_PROTOCOL = "override";
    public static final String CONFIGURATORS_CATEGORY = "configurators";
    public static final String EMPTY_PROTOCOL = "empty";
    public static final String WEIGHT_KEY = "weight";
    public static final int DEFAULT_WEIGHT = 100;
    public static final String ADMIN_PROTOCOL = "admin";
    public static final String CLASSIFIER_KEY = "classifier";
    public static final String CHECK_KEY = "check";
    public static final String VERSION_KEY = "version";
    public static final String PROVIDERS_CATEGORY = "providers";
    public static final String CONSUMERS_CATEGORY = "consumers";
    public static final String SPECIFICATION_VERSION_KEY = "release";
    public static final String GLOBAL_CONFIG = "global";
    public static final String GLOBAL_CONFIG_PATH = "config/dubbo/dubbo.properties";
    public static final String METRICS_PORT = "metrics.port";
    public static final String METRICS_PROTOCOL = "metrics.protocol";
    public static final Set<String> CONFIGS = new HashSet<>();
    public static final String COLON = ":";
    public static final String WELL = "#";
    public static final String EIT = "@";
    public static final String COOKIE_USER = "CAS";
    public static final int TAG_CREATE_TYPE_DIY = 0;
    public static final int TAG_CREATE_TYPE_SWIMLANE = 1;
    //标签类泳道组
    public static final int SWIM_LANE_GROUP_TYPE_TAG = 1;
    public static final int SWIM_LANE_ENABLE = 1;
    public static final int SWIM_LANE_DISABLE = 0;

    public static final String REDIS_SWIMLINE_GROUP_KEY = "swimlane_group_index";
    public static final String REDIS_SWIMLINE_KEY = "swimlane_index";

    public static final String LOCAL_ENV = "本地环境";
    public static final String NACOS_DEFAULT_GROUP = "DEFAULT_GROUP@@";
    public static Integer ZERO  =0;

    public static final String COMMON_ERROR_MESSAGE = " Rpc接口调用异常，请联系研发人员。";

    /**
     * 比较操作常量
     */
    public static final int CONDITION_OP_TYPE_EQ = 1;
    public static final int CONDITION_OP_TYPE_CONTAIN = 2;
    public static final int CONDITION_OP_TYPE_BIGGER = 3;
    public static final int CONDITION_OP_TYPE_SMALLER = 4;
    public static final int CONDITION_OP_TYPE_BIGGER_AND_EQ = 5;

    public static final int CONDITION_OP_TYPE_SMALLER_EQ = 6;

    public static final int CONDITION_OP_TYPE_NOT_CONTAIN = 7;
    public static final int CONDITION_OP_TYPE_NOT_EQ = 8;

    static {
        CONFIGS.add(WEIGHT);
        CONFIGS.add(BALANCING);
    }

}
