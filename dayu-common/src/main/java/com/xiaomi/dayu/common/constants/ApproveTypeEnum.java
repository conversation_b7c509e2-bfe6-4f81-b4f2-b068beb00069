package com.xiaomi.dayu.common.constants;

public enum ApproveTypeEnum {
    NACOS_CONFIG(10,"Nacos配置"); // nacos配置
    private int code;
    private String desc;

    ApproveTypeEnum(int code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static ApproveTypeEnum getApproveTypeEnum(int code){
        for (ApproveTypeEnum value : ApproveTypeEnum.values()) {
            if(value.getCode() == code){
                return value;
            }
        }
        throw new IllegalStateException("Unexpected  ApproveTypeEnum value: " + code);
    }
}
