package com.xiaomi.dayu.common.exception;

public enum ExceptionEnum {
    NO_PERMISSION_APPLICATION(4001,"没有应用访问权限"),
    NO_PERMISSION_SERVICE(4002,"没有服务名访问权限"),
    NO_EXISTS_APPLICATION(1001,"应用名不存在"),
    NO_EXISTS_SERVICE(1002,"服务名不存在"),
    NO_EXISTS_DATA_ID(2001,"nacos配置不存在"),
    NO_TAG_PARAM(2003,"tag名参数不能为空"),
    NO_PERMISSION_DATA_ID(2002,"没有nacos配置权限");
    private int code;
    private String message;

    ExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
