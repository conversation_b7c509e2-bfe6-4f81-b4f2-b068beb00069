package com.xiaomi.dayu.common;

import com.xiaomi.dayu.common.exception.ExceptionEnum;
import com.xiaomi.dayu.common.interfaces.Success;

import java.io.Serializable;

public class ResultResponse<T> implements Serializable, Success {

    private boolean success = true;

    private int code ;

    private String message;

    private T data;

    public ResultResponse() {
    }
    public ResultResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }
    public ResultResponse(T data) {
        this.data = data;
    }



    public ResultResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResultResponse(boolean success, int code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }

    public ResultResponse(boolean success, int code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> ResultResponse<T> success(T data){
        return new ResultResponse<>(data);
    }
    public static <T> ResultResponse<T> fail(int code ,String message){
        return new ResultResponse<>(false,code,message);
    }
    public static <T> ResultResponse<T> fail(ExceptionEnum exceptionEnum){
        return new ResultResponse<>(false,exceptionEnum.getCode(),exceptionEnum.getMessage());
    }



    public boolean isSuccess() {
        return success;
    }

    public ResultResponse<T> setSuccess(boolean success) {
        this.success = success;
        return this;
    }

    public int getCode() {
        return code;
    }

    public ResultResponse<T> setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public ResultResponse<T> setMessage(String message) {
        this.message = message;
        return this;
    }

    public T getData() {
        return this.data;
    }

    public ResultResponse<T> setData(T data) {
        this.data = data;
        return this;
    }
}
