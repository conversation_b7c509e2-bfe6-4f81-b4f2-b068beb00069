package com.xiaomi.dayu.common.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Type DateUtils.java
 * @Desc
 * @date 2024/11/26 16:32
 */
public class DateUtils {
	public static String formatDateToString(Date date){
		if(date == null){
			return null;
		}
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return simpleDateFormat.format(date);
	}
}
