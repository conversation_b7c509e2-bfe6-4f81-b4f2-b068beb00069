package com.xiaomi.dayu.common.util;

import com.fasterxml.jackson.databind.JsonNode;

import java.util.Optional;

/**
 * <AUTHOR> (yang<PERSON><PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/1/7
 */
public class SerializeUtils {

    public static Optional<String> getText(JsonNode jsonNode, String key) {
        if (jsonNode == null || !jsonNode.has(key) || jsonNode.get(key) == null) {
            return Optional.empty();
        }
        return Optional.of(jsonNode.get(key).asText());
    }

    public static Optional<Integer> getInteger(JsonNode jsonNode, String key) {
        if (jsonNode == null || !jsonNode.has(key) || jsonNode.get(key) == null) {
            return Optional.empty();
        }
        return Optional.of(jsonNode.get(key).asInt());
    }

    public static Optional<Long> getLong(JsonNode jsonNode, String key) {
        if (jsonNode == null || !jsonNode.has(key) || jsonNode.get(key) == null) {
            return Optional.empty();
        }
        return Optional.of(jsonNode.get(key).asLong());
    }
}
