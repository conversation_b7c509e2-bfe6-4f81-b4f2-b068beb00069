package com.xiaomi.dayu.common;

//"dataId","dubbo-consumer::.condition-router"：：条件路由、黑白名单
//"dataId","dubbo-provider.tag-router"：标签路由
//"dataId","dubbo-provider.configurators"：动态配置、权重配置、负载均衡

public enum ConfigTypeEnum {
    Accesses("Accesses","黑白名单","condition-router"),
    ConditionRoutes("ConditionRoutes","条件路由","condition-router"),
    TagRoutes("TagRoutes","标签路由","tag-router"),
    Overrides("Overrides","动态配置","configurators"),
    Weight("Weight","权重调整","configurators"),
    LoadBalance("LoadBalance","负载均衡","configurators");
    private String type;
    private String desc;
    private String configName;

    ConfigTypeEnum(String type, String desc, String configName) {
        this.type = type;
        this.desc = desc;
        this.configName = configName;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getConfigName() {
        return configName;
    }
}
