/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.common.util;

import com.xiaomi.dayu.common.ConfigTypeEnum;
import com.xiaomi.dayu.common.exception.ParamValidationException;
import com.xiaomi.dayu.model.dto.BaseDTO;
import com.xiaomi.dayu.model.dto.BaseVO;
import com.xiaomi.dayu.model.store.OverrideDTO;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.metadata.definition.model.MethodDefinition;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.xiaomi.dayu.common.util.Constants.*;

public class ConvertUtil {
    private ConvertUtil() {
    }

    public static Map<String, String> serviceName2Map(String serviceName) {
        String group = Tool.getGroup(serviceName);
        String version = Tool.getVersion(serviceName);
        String interfaze = Tool.getInterface(serviceName);

        Map<String, String> ret = new HashMap<String, String>();
        if (!StringUtils.isEmpty(serviceName)) {
            ret.put(Constants.INTERFACE_KEY, interfaze);
        }
        if (!StringUtils.isEmpty(version)) {
            ret.put(Constants.VERSION_KEY, version);
        }
        if (!StringUtils.isEmpty(group)) {
            ret.put(Constants.GROUP_KEY, group);
        }

        return ret;
    }

    public static String getIdFromDTO(BaseDTO baseDTO) {
        if ("application".equals(baseDTO.getScope())) {
            if (StringUtils.isNotEmpty(baseDTO.getApplication())) {
                return baseDTO.getApplication();
            } else {
                throw new ParamValidationException("application are Empty!");
            }
        }
        // id format: "${class}:${version}:${group}"
        StringBuilder stringBuilder = new StringBuilder(baseDTO.getService());
        if(org.apache.commons.lang3.StringUtils.isNotBlank(baseDTO.getServiceVersion())){
            stringBuilder.append(COLON).append(baseDTO.getServiceVersion());
        }
        if(org.apache.commons.lang3.StringUtils.isNotBlank(baseDTO.getServiceGroup())){
            stringBuilder.append(COLON).append(baseDTO.getServiceGroup());
        }

        return stringBuilder.toString();
    }

    public static BaseVO parseString2BaseVO(ConfigTypeEnum configTypeEnum, String dataId) {
        BaseVO baseVO = new BaseVO();
        String replace = dataId.replace("." + configTypeEnum.getConfigName(), "");
        String[] split = replace.split(COLON);
        if (split.length == 3) {
            baseVO.setService(split[0]);
            baseVO.setServiceVersion(split[1]);
            baseVO.setServiceGroup(split[2]);
        } else {
            baseVO.setApplication(replace);
        }
        return baseVO;
    }
    public static BaseVO parseString2BaseVO(ConfigTypeEnum configTypeEnum, OverrideDTO overrideDTO) {
        BaseVO baseVO = new BaseVO();
        String scope = overrideDTO.getScope();
        baseVO.setScope(scope);
        if(scope.equals("service")){
            baseVO.setService(overrideDTO.getService());
            baseVO.setServiceVersion(overrideDTO.getServiceVersion());
            baseVO.setServiceGroup(overrideDTO.getServiceGroup());
        }else {
            baseVO.setApplication(overrideDTO.getKey());
        }
        return baseVO;
    }

    /**
     * Detach interface class, version and group from id.
     *
     * @param id
     * @return java.lang.String[] 0: interface class; 1: version; 2: group
     */
    public static String[] detachId(String id) {
        if (id.contains(COLON)) {
            return id.split(COLON);
        } else {
            return new String[]{id};
        }
    }

    public static void detachIdToService(String id, BaseDTO baseDTO) {
        String[] detachResult = detachId(id);
        baseDTO.setService(detachResult[0]);
        if (detachResult.length > 1) {
            baseDTO.setServiceVersion(detachResult[1]);
        }
        if (detachResult.length > 2) {
            baseDTO.setServiceGroup(detachResult[2]);
        }
    }

    public static String getServiceIdFromDTO(BaseDTO baseDTO, String serviceVersion, String serviceGroup,
                                             boolean groupAsFolder) {
        StringBuilder buf = new StringBuilder();
        buf.append(baseDTO.getService());
        if (StringUtils.isNotEmpty(serviceVersion)) {
            buf.append(COLON).append(serviceVersion);
        }
        if (StringUtils.isNotEmpty(serviceGroup)) {
            if (groupAsFolder) {
                buf.insert(0, serviceGroup + "/");
            } else {
                buf.append(COLON).append(serviceGroup);
            }
        }
        return buf.toString();
    }

    public static String null2EmptyString(String str) {
        if (null == str) {
            str = StringUtils.EMPTY_STRING;
        }
        return str;
    }

    public static String getScopeFromDTO(BaseDTO baseDTO) {
        if (StringUtils.isNotEmpty(baseDTO.getApplication())) {
            return Constants.APPLICATION;
        } else {
            return Constants.SERVICE;
        }
    }

    public static Map methodList2Map(List<MethodDefinition> methods) {
        Map<String, MethodDefinition> res = new HashMap<>();
        for (int i = 0; i < methods.size(); i++) {
            res.put(methods.get(i).getName(), methods.get(i));
        }
        return res;
    }

    public static void assign(BaseVO baseVO, BaseDTO baseDTO) {
        baseDTO.setApplication(baseVO.getApplication());
        baseDTO.setService(baseVO.getService());
        baseDTO.setServiceGroup(baseVO.getServiceGroup());
        baseDTO.setServiceVersion(baseVO.getServiceVersion());
        if (baseVO.getScope().equals(APPLICATION)) {
            baseDTO.setId(baseDTO.getApplication());
            baseDTO.setScope(APPLICATION);
        } else {
            StringBuilder keyStringBuilder = new StringBuilder();
            if(org.apache.commons.lang3.StringUtils.isNotBlank(baseDTO.getServiceGroup())){
                keyStringBuilder.append(baseDTO.getServiceGroup()).append("/");
            }
            keyStringBuilder.append(baseDTO.getService());
            if(org.apache.commons.lang3.StringUtils.isNotBlank(baseDTO.getServiceVersion())){
                keyStringBuilder.append(":").append(baseDTO.getServiceVersion());
            }
            baseDTO.setId(keyStringBuilder.toString());
            baseDTO.setScope(SERVICE);
        }
    }

    public static Map<String, List<String>> clearEmptyEnv(Map<String, List<String>> envIpList) {
        Map<String, List<String>> result = new HashMap<>();
        envIpList.keySet().forEach(env -> {
            if (Objects.nonNull(envIpList.get(env)) && !envIpList.get(env).isEmpty()) {
                result.putIfAbsent(env, envIpList.get(env));
            }
        });
        return result;
    }

    /**
     * 处理转换取值表达式为真实表达式
     */
    public static String adapt2RealParseExpr(String originParseExpr) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(originParseExpr)) {
            return DEFAULT_EXPR_PREX;
        }
        //直接加上params.toList()前缀取
        return DEFAULT_EXPR_PREX + originParseExpr;
    }
}
