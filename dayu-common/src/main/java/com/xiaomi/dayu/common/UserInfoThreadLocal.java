package com.xiaomi.dayu.common;

import com.xiaomi.dayu.model.UserInfo;

public class UserInfoThreadLocal {
    private static final ThreadLocal<UserInfo> userInfoThreadLocal = new ThreadLocal<>();

    public static UserInfo getUserInfo() {
        return userInfoThreadLocal.get();
    }

    public static void setUserInfo(UserInfo userInfo){
        userInfoThreadLocal.set(userInfo);
    }

    public static void removeUserInfo(){
        userInfoThreadLocal.remove();
    }


}
