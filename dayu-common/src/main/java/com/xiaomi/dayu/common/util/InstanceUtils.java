package com.xiaomi.dayu.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.domain.Provider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
@Slf4j
public class InstanceUtils {
    private static final Logger logger = LoggerFactory.getLogger(InstanceUtils.class);

    public static String getDubboVersion(Provider provider) {
        String dubboVersion = null;
        try{
            String release = getParams(provider.getParameters()).get("release");
            if (StringUtils.isNotBlank(release)) {
                dubboVersion = release;
            } else {
                String dubbo_version = getParams(provider.getParameters()).get("dubbo_version");
                if (StringUtils.isNotBlank(dubbo_version)) {
                    dubboVersion = dubbo_version.split("_")[0];
                }
            }
        } catch (Exception e) {
            log.error("getDubboVersion  异常，provider={}", JSON.toJSONString(provider), e);
        }
        return dubboVersion;
    }

    public static String getDubboVersion(Consumer consumer) {
        String dubboVersion = null;
        try {
            String release = getParams(consumer.getParameters()).get("release");
            if (StringUtils.isNotBlank(release)) {
                dubboVersion = release;
            } else {
                String dubbo_version = getParams(consumer.getParameters()).get("dubbo_version");
                if (StringUtils.isNotBlank(dubbo_version)) {
                    dubboVersion = dubbo_version.split("_")[0];
                }
            }
        } catch (Exception e) {
            log.error("getDubboVersion  异常，consumer={}", JSON.toJSONString(consumer), e);
        }
        return dubboVersion;
    }

    public static Map<String,String> getParams(String paramsString){
        String[] split = paramsString.split("&");
        HashMap<String,String> params = new HashMap<>(split.length);
        if(split != null && split.length>0){
            Arrays.stream(split).forEach(str->{
                String[] kv = str.split("=");
                if(kv.length == 2){
                    params.put(kv[0],kv[1]);
                }
            });
        }
        return params;
    }

    public static String getDubboVersion(String metadata) {
        String dubboVersion = null;
        try{

            JSONObject jsonObject = JSON.parseObject(metadata);
            String release = jsonObject.getString("release");
            if(StringUtils.isNotBlank(release)){
                dubboVersion = release;
            }else{
                String dubbo_version = jsonObject.getString("dubbo_version");
                if(StringUtils.isNotBlank(dubbo_version)){
                    dubboVersion = dubbo_version.split("_")[0];
                }
            }
        }catch (Exception e){
            log.error("getDubboVersion  异常，metadata={}",metadata,e);
        }
        return dubboVersion;
    }
    public static String getDubboVersion(Map<String,String> map) {
        String dubboVersion = null;
        try{
            String release = map.get("release");
            if(StringUtils.isNotBlank(release)){
                dubboVersion = release;
            }else{
                String dubbo_version = map.get("dubbo_version");
                if(StringUtils.isNotBlank(dubbo_version)){
                    dubboVersion = dubbo_version.split("_")[0];
                }
            }
        }catch (Exception e){
            log.error("getDubboVersion  异常，map={}",JSON.toJSONString(map),e);
        }
            return dubboVersion;
        }
    public static String getValueFromJson(String params,String key){
        try{
            JSONObject jsonObject = JSON.parseObject(params);
            return jsonObject.getString(key);
        }catch (Exception e){
            logger.error("转换成json异常，params={},key={}",params,key, e);
            return null;
        }
    }

    public static String getValueFromParam(String parameters, String key) {
        return getParams(parameters).get(key);
    }
}
