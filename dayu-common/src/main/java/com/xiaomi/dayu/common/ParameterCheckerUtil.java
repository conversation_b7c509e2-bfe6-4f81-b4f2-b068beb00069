package com.xiaomi.dayu.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import swimlane.bo.ConditionPairBo;

import java.math.BigDecimal;
import com.xiaomi.data.push.antlr.expr.Expr;
import swimlane.bo.ConditionPairCheckBo;

import static com.xiaomi.dayu.common.util.Constants.*;
import static com.xiaomi.dayu.common.util.Constants.CONDITION_OP_TYPE_NOT_CONTAIN;

/**
 * <AUTHOR>
 * @Type ParameterCheckerUtil.java
 * @Desc
 * @date 2025/2/20 17:20
 */
@Slf4j
public class ParameterCheckerUtil {
	public static boolean judgeParamSatisfy(ConditionPairCheckBo conditionPairCheckBo) {
		ConditionPairBo condition = conditionPairCheckBo.getConditionPairBo();
		String jsonParam = conditionPairCheckBo.getJsonParam();
		String opVal = getTargetValue(condition.getParseExpr(), jsonParam).toString();
		String targetVal = condition.getParamValue();

		boolean match = false;
		try {
			String[] targetStrArr;
			if (targetVal.contains("|")) {
				targetStrArr = targetVal.split("\\|");
			} else {
				targetStrArr = new String[]{targetVal};
			}

			switch (condition.getOp()) {
				case CONDITION_OP_TYPE_EQ:
					//=
					for (String s : targetStrArr) {
						if (opVal.equals(s)) {
							match = true;
							break;
						}
					}
					break;
				case CONDITION_OP_TYPE_NOT_EQ: {
					//!=
					for (String s : targetStrArr) {
						if (!opVal.equals(s)) {
							match = true;
							break;
						}
					}
				}
				break;
				case CONDITION_OP_TYPE_BIGGER: {
					// >
					for (String s : targetStrArr) {
						if (opVal.compareTo(s) > 0) {
							match = true;
							break;
						}
					}
				}
				break;

				case CONDITION_OP_TYPE_BIGGER_AND_EQ: {
					// >=
					for (String s : targetStrArr) {
						if (opVal.compareTo(s) >= 0) {
							match = true;
							break;
						}
					}
				}
				break;

				case CONDITION_OP_TYPE_SMALLER: {
					// <
					for (String s : targetStrArr) {
						if (opVal.compareTo(s) < 0) {
							match = true;
							break;
						}
					}
				}
				break;

				case CONDITION_OP_TYPE_SMALLER_EQ: {
					// <=
					for (String s : targetStrArr) {
						if (opVal.compareTo(s) <= 0) {
							match = true;
							break;
						}
					}
				}
				break;

				case CONDITION_OP_TYPE_CONTAIN: {
					//包含
					for (String s : targetStrArr) {
						if (opVal.contains(s)) {
							match = true;
							break;
						}
					}
				}
				break;

				case CONDITION_OP_TYPE_NOT_CONTAIN: {
					//不包含
					for (String s : targetStrArr) {
						if (!opVal.contains(s)) {
							match = true;
							break;
						}
					}
				}
				break;
			}
		} catch (Exception e) {
			log.warn("ParameterCheckerUtil judgeParamSatisfy failed,condition:{},err:{}", condition, e.getMessage());
			match = false;
		}
		return match;
	}
	/**
	 * 根据取值表达式从参数json串中取值
	 */
	public static Object getTargetValue(String parseExpr, String paramJson) {
		Object value;
		try {
			if (parseExpr.contains("|")) {
				//存在该标识符说明指定值类型
				//表达式 例如：params.toList()[0]{data}{goodIds}|int
				String[] exprArr = parseExpr.split("\\|", 2);
				value = Expr.params(paramJson, exprArr[0]);
				value = getValByType(value, exprArr[1]);
			} else {
				//表达式 例如：params.toList()[0]{data}{goodIds}
				value = Expr.params(paramJson, parseExpr);
			}
		} catch (Exception e) {
			log.error("ParameterCheckerUtil getTargetValue get val failed,expr:{},cause by:{}", parseExpr, e.getMessage());
			value = StringUtils.EMPTY;
		}
		return value;
	}
	/**
	 * 转换值类型
	 */
	public static Object getValByType(Object oriValue, String type) {
		Object value;
		try {
			switch (type) {
				case "int":
					return (int) Double.parseDouble(oriValue.toString());
				case "double":
					return Double.parseDouble(oriValue.toString());
				case "long":
					String valStr = oriValue.toString();
					if (valStr.contains("E")){
						//科学计数法
						BigDecimal val = new BigDecimal(valStr);
						return Long.parseLong(val.toPlainString());
					}
					return Long.parseLong(oriValue.toString());
				default:
					return oriValue;
			}
		} catch (Exception e) {
			log.error("ParameterCheckerUtil getValByType get val failed,type:{},cause by:{}", type, e.getMessage());
			value = StringUtils.EMPTY;
		}
		return value;
    }
}
