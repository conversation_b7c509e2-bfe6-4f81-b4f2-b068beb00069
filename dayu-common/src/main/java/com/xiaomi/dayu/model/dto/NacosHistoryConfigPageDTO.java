package com.xiaomi.dayu.model.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @Type NacosHistoryConfigPageDTO.java
 * @Desc
 * @date 2025/3/31 16:07
 */
public class NacosHistoryConfigPageDTO implements java.io.Serializable{
	private Object pageItems;
	private int pagesAvailable;
	private int pageNumber;

	private int pageSize;
	private long totalCount;

	public Object getPageItems() {
		return pageItems;
	}

	public void setPageItems(Object pageItems) {
		this.pageItems = pageItems;
	}

	public int getPagesAvailable() {
		return pagesAvailable;
	}

	public void setPagesAvailable(int pagesAvailable) {
		this.pagesAvailable = pagesAvailable;
	}

	public int getPageNumber() {
		return pageNumber;
	}

	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}
}
