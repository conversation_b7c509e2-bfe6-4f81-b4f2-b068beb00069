/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xiaomi.dayu.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public abstract class RouteDTO extends BaseDTO {
    @ApiModelProperty(value = " 路由规则的优先级，用于排序，优先级越大越靠前执行")
    private int priority;
    @ApiModelProperty(value = "当前路由规则是否生效，可不填，缺省生效")
    private boolean enabled;
    @ApiModelProperty(value = "当路由结果为空时，是否强制执行，如果不强制执行，路由结果为空的路由规则将自动失效，可不填，缺省为 false")
    private boolean force;
    @ApiModelProperty(value = "")
    private boolean runtime;

    private String updateTime;
}
