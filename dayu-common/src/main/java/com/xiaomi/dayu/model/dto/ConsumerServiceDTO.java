package com.xiaomi.dayu.model.dto;

import com.xiaomi.dayu.model.domain.Override;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.domain.Route;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
public class ConsumerServiceDTO implements Comparable<ConsumerServiceDTO>{
    private String serviceId;

    private String service;

    private String group;

    private String version;

    private String parameters;

    private String result;    /*route result*/

    private String address; /* address of consumer */

    private String registry; /* Consumer connected registry address */

    private String application; /* application name */

    private String username;      /* user name of consumer */

    private String statistics;    /* Service call statistics */

    private Date collected;  /* Date statistics was recorded */

    private Override override;

    private List<Override> overrides;

    private List<Route> conditionRoutes;

    private List<Provider> providers;

    private Date expired;

    private long alived;    /*Time to live in milliseconds*/

    private String developers;

    private String department;

    private String hostName;

    private String dubboVersion;

    private Integer consumerCount;

    private String monitorUrl;

    private Boolean own;

    @java.lang.Override
    public int compareTo(ConsumerServiceDTO o) {
        int result = StringUtils.trimToEmpty(application).compareTo(StringUtils.trimToEmpty(o.getApplication()));
        if (result == 0) {
            result = StringUtils.trimToEmpty(service).compareTo(StringUtils.trimToEmpty(o.getService()));
            if (result == 0) {
                result = StringUtils.trimToEmpty(group).compareTo(StringUtils.trimToEmpty(o.getGroup()));
            }
            if (result == 0) {
                result = StringUtils.trimToEmpty(version).compareTo(StringUtils.trimToEmpty(o.getVersion()));
            }
            if (result == 0) {
                result = StringUtils.trimToEmpty(address).compareTo(StringUtils.trimToEmpty(o.getAddress()));
            }
        }
        return result;
    }
    @java.lang.Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ConsumerServiceDTO that = (ConsumerServiceDTO) o;
        return Objects.equals(service, that.service) && Objects.equals(application, that.application) && Objects
                .equals(group, that.group) && Objects.equals(version, that.version);
    }

    @java.lang.Override
    public int hashCode() {
        return Objects.hash(service, application, group, version);
    }

}
