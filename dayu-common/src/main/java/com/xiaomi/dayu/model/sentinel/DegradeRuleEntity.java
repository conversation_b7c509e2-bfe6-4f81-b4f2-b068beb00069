/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.model.sentinel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 */
@ApiModel(description = "熔断")
public class DegradeRuleEntity implements Serializable {

    private Long id;
    @ApiModelProperty(value = "应用名称")
    private String app;
    @ApiModelProperty(value = "")
    private String ip;
    @ApiModelProperty(value = "")
    private Integer port;
    @ApiModelProperty(value = "资源名，service")
    private String resource;
    @ApiModelProperty(value = "service")
    private String service;
    @ApiModelProperty(value = "dubboGroup")
    private String dubboGroup;
    @ApiModelProperty(value = "dubboVersion")
    private String dubboVersion;
    @ApiModelProperty(value = "方法名")
    private String method;
    @ApiModelProperty(value = "受限app")
    private String limitApp;
    @ApiModelProperty(value = "最大 RT")
    private Double count;
    @ApiModelProperty(value = "熔断时长")
    private Integer timeWindow;
    @ApiModelProperty(value = "熔断策略，慢调用比例：0，异常比例：1，异常数：2")
    private Integer grade;
    @ApiModelProperty(value = "最小请求数")
    private Integer minRequestAmount;
    @ApiModelProperty(value = "比例阈值")
    private Double slowRatioThreshold;
    @ApiModelProperty(value = "统计时长")
    private Integer statIntervalMs;
    /**
     * 0. open 1.close default 0
     */
    @ApiModelProperty(value = "是否开启,0. open 1.close default 0")
    private Integer isClose;
    private Date gmtCreate;
    private Date gmtModified;


    @ApiModelProperty(value = "降级class:method")
    private String defaultFallbackMethod;
    @ApiModelProperty(value = "降级class")
    private String fallbackClass;
    @ApiModelProperty(value = "降级method")
    private String fallbackMethod;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getLimitApp() {
        return limitApp;
    }

    public void setLimitApp(String limitApp) {
        this.limitApp = limitApp;
    }

    public Double getCount() {
        return count;
    }

    public void setCount(Double count) {
        this.count = count;
    }

    public Integer getTimeWindow() {
        return timeWindow;
    }

    public void setTimeWindow(Integer timeWindow) {
        this.timeWindow = timeWindow;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Integer getMinRequestAmount() {
        return minRequestAmount;
    }

    public void setMinRequestAmount(Integer minRequestAmount) {
        this.minRequestAmount = minRequestAmount;
    }

    public Double getSlowRatioThreshold() {
        return slowRatioThreshold;
    }

    public void setSlowRatioThreshold(Double slowRatioThreshold) {
        this.slowRatioThreshold = slowRatioThreshold;
    }

    public Integer getStatIntervalMs() {
        return statIntervalMs;
    }

    public void setStatIntervalMs(Integer statIntervalMs) {
        this.statIntervalMs = statIntervalMs;
    }

    public Integer getIsClose() {
        return isClose;
    }

    public void setIsClose(Integer isClose) {
        this.isClose = isClose;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getDefaultFallbackMethod() {
        return defaultFallbackMethod;
    }

    public void setDefaultFallbackMethod(String defaultFallbackMethod) {
        this.defaultFallbackMethod = defaultFallbackMethod;
    }

    public String getFallbackClass() {
        return fallbackClass;
    }

    public void setFallbackClass(String fallbackClass) {
        this.fallbackClass = fallbackClass;
    }

    public String getFallbackMethod() {
        return fallbackMethod;
    }

    public void setFallbackMethod(String fallbackMethod) {
        this.fallbackMethod = fallbackMethod;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getDubboGroup() {
        return dubboGroup;
    }

    public void setDubboGroup(String dubboGroup) {
        this.dubboGroup = dubboGroup;
    }

    public String getDubboVersion() {
        return dubboVersion;
    }

    public void setDubboVersion(String dubboVersion) {
        this.dubboVersion = dubboVersion;
    }
}
