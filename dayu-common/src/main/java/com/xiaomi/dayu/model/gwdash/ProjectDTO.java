/*
 *  Copyright 2020 Xiaomi
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

package com.xiaomi.dayu.model.gwdash;

import lombok.Data;

import java.io.Serializable;

/**
 * 项目
 */
public class ProjectDTO implements Serializable {

    private long id;

    private String name;

    private String desc;

    private long ctime;

    private long utime;

    private int status;

    private String gitAddress;

    private String gitGroup;

    private String gitName;

    private ProjectGen projectGen;

    private int version;

    private int deployLimit;

    private int page = 1;

    private int pageSize = 20;

    private boolean showAll;

    private String search;

    private String domain;

    private Long iamTreeId;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public long getCtime() {
        return ctime;
    }

    public void setCtime(long ctime) {
        this.ctime = ctime;
    }

    public long getUtime() {
        return utime;
    }

    public void setUtime(long utime) {
        this.utime = utime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getGitAddress() {
        return gitAddress;
    }

    public void setGitAddress(String gitAddress) {
        this.gitAddress = gitAddress;
    }

    public String getGitGroup() {
        return gitGroup;
    }

    public void setGitGroup(String gitGroup) {
        this.gitGroup = gitGroup;
    }

    public String getGitName() {
        return gitName;
    }

    public void setGitName(String gitName) {
        this.gitName = gitName;
    }

    public ProjectGen getProjectGen() {
        return projectGen;
    }

    public void setProjectGen(ProjectGen projectGen) {
        this.projectGen = projectGen;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getDeployLimit() {
        return deployLimit;
    }

    public void setDeployLimit(int deployLimit) {
        this.deployLimit = deployLimit;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public boolean isShowAll() {
        return showAll;
    }

    public void setShowAll(boolean showAll) {
        this.showAll = showAll;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public Long getIamTreeId() {
        return iamTreeId;
    }

    public void setIamTreeId(Long iamTreeId) {
        this.iamTreeId = iamTreeId;
    }
}
