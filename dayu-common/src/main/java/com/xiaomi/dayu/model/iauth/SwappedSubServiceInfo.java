package com.xiaomi.dayu.model.iauth;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

public class SwappedSubServiceInfo implements Serializable {
    private SubServiceInfo subServiceInfo;
    private Integer scopeCount;


    public SwappedSubServiceInfo(SubServiceInfo subServiceInfo, Integer scopeCount) {
        this.subServiceInfo = subServiceInfo;
        this.scopeCount = scopeCount;
    }

    public SubServiceInfo getSubServiceInfo() {
        return subServiceInfo;
    }

    public void setSubServiceInfo(SubServiceInfo subServiceInfo) {
        this.subServiceInfo = subServiceInfo;
    }

    public Integer getScopeCount() {
        return scopeCount;
    }

    public void setScopeCount(Integer scopeCount) {
        this.scopeCount = scopeCount;
    }

    @Override
    public String toString() {
        return "SwappedSubServiceInfo{" +
                "subServiceInfo=" + JSONObject.toJSONString(subServiceInfo) +
                ", scopeCount=" + scopeCount +
                '}';
    }
}
