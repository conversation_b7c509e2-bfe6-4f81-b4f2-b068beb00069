/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xiaomi.dayu.model.dto;

import com.xiaomi.dayu.model.store.OverrideConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description="动态规则配置，https://dubbo.apache.org/zh/docs/v2.7/user/examples/config-rule/")
public class DynamicConfigDTO extends BaseDTO {
    @ApiModelProperty(name = "")
    private String configVersion = "";
    @ApiModelProperty(name = "开启或关闭")
    private boolean enabled;
    @ApiModelProperty(name = "配置列表")
    private List<OverrideConfig> configs;
    private String key ;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<OverrideConfig> getConfigs() {
        return configs;
    }

    public void setConfigs(List<OverrideConfig> configs) {
        this.configs = configs;
    }

    public String getConfigVersion() {
        return configVersion;
    }

    public void setConfigVersion(String configVersion) {
        this.configVersion = configVersion;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
