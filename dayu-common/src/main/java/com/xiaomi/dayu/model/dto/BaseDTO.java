/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * BaseDTO
 *
 * For receive ID parameter with @RequestBody
 */
@ApiModel(description="")
public abstract class BaseDTO {
    @ApiModelProperty(value="应用名称")
    private String application;

    private String appTopicToGroups;
    @ApiModelProperty(value="服务名")
    private String service;
    private String id;
    @ApiModelProperty(value="版本")
    private String serviceVersion;
    @ApiModelProperty(value="组名")
    private String serviceGroup;
/*    @ApiModelProperty(value="调用端传consumer，被调用端传provider")
    private String side;*/
    @ApiModelProperty(value="application或service")
    private String scope;

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getServiceVersion() {
        return serviceVersion;
    }

    public void setServiceVersion(String serviceVersion) {
        this.serviceVersion = serviceVersion;
    }

    public String getServiceGroup() {
        return serviceGroup;
    }

    public void setServiceGroup(String serviceGroup) {
        this.serviceGroup = serviceGroup;
    }

/*    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }*/

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getAppTopicToGroups() {
        return appTopicToGroups;
    }

    public void setAppTopicToGroups(String appTopicToGroups) {
        this.appTopicToGroups = appTopicToGroups;
    }
}
