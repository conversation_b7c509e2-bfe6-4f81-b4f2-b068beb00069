package com.xiaomi.dayu.model.sentinel;

import com.alibaba.csp.sentinel.slots.block.flow.ClusterFlowConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "流控多条件汇总")
public class FlowRuleInfo {
    @ApiModelProperty(value = "应用名称")
    private String app;
    @ApiModelProperty(value = "资源名")
    private String resource;
    @ApiModelProperty(value = "service")
    private String service;
    @ApiModelProperty(value = "方法名")
    private String method;
    @ApiModelProperty(value = "dubboGroup")
    private String dubboGroup;
    @ApiModelProperty(value = "dubboVersion")
    private String dubboVersion;


    @ApiModelProperty(value = "阈值")
    private Double count;
    @ApiModelProperty(value = "是否集群")
    private boolean clusterMode;
    @ApiModelProperty(value = "集群配置")
    private ClusterFlowConfig clusterConfig;
    @ApiModelProperty(value = "是否开启")
    private Integer isClose;

    private Long id;
    private String ip;
    @ApiModelProperty(value = "")
    private Integer port;
    @ApiModelProperty(value = "")
    private String limitApp;
    /**
     * 0为线程数;1为qps
     */
    @ApiModelProperty(value = "0为线程数;1为qps")
    private Integer grade;
    /**
     * 0为直接限流;1为关联限流;2为链路限流
     ***/
    @ApiModelProperty(value = "0为直接限流;1为关联限流;2为链路限流")
    private Integer strategy;
    @ApiModelProperty(value = "关联资源名")
    private String refResource;
    /**
     * 0. default, 1. warm up, 2. rate limiter
     */
    @ApiModelProperty(value = "流控效果：0. default, 1. warm up, 2. rate limiter")
    private Integer controlBehavior;
    @ApiModelProperty(value = "预热时长")
    private Integer warmUpPeriodSec;
    /**
     * max queueing time in rate limiter behavior
     */
    @ApiModelProperty(value = "超时时间")
    private Integer maxQueueingTimeMs;
    private Date gmtCreate;
    private Date gmtModified;

    @ApiModelProperty(value = "是否可以修改，有些数据不是通过大禹平台创建的")
    private boolean canEdit;


    @ApiModelProperty(value = "降级class:method")
    private String defaultFallbackMethod;
    @ApiModelProperty(value = "降级class")
    private String fallbackClass;
    @ApiModelProperty(value = "降级method")
    private String fallbackMethod;


    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Double getCount() {
        return count;
    }

    public void setCount(Double count) {
        this.count = count;
    }

    public boolean isClusterMode() {
        return clusterMode;
    }

    public void setClusterMode(boolean clusterMode) {
        this.clusterMode = clusterMode;
    }

    public Integer getIsClose() {
        return isClose;
    }

    public void setIsClose(Integer isClose) {
        this.isClose = isClose;
    }


    public ClusterFlowConfig getClusterConfig() {
        return clusterConfig;
    }

    public void setClusterConfig(ClusterFlowConfig clusterConfig) {
        this.clusterConfig = clusterConfig;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getLimitApp() {
        return limitApp;
    }

    public void setLimitApp(String limitApp) {
        this.limitApp = limitApp;
    }

    public Integer getStrategy() {
        return strategy;
    }

    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }

    public String getRefResource() {
        return refResource;
    }

    public void setRefResource(String refResource) {
        this.refResource = refResource;
    }

    public Integer getControlBehavior() {
        return controlBehavior;
    }

    public void setControlBehavior(Integer controlBehavior) {
        this.controlBehavior = controlBehavior;
    }

    public Integer getWarmUpPeriodSec() {
        return warmUpPeriodSec;
    }

    public void setWarmUpPeriodSec(Integer warmUpPeriodSec) {
        this.warmUpPeriodSec = warmUpPeriodSec;
    }

    public Integer getMaxQueueingTimeMs() {
        return maxQueueingTimeMs;
    }

    public void setMaxQueueingTimeMs(Integer maxQueueingTimeMs) {
        this.maxQueueingTimeMs = maxQueueingTimeMs;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public boolean isCanEdit() {
        return canEdit;
    }

    public void setCanEdit(boolean canEdit) {
        this.canEdit = canEdit;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getDefaultFallbackMethod() {
        return defaultFallbackMethod;
    }

    public void setDefaultFallbackMethod(String defaultFallbackMethod) {
        this.defaultFallbackMethod = defaultFallbackMethod;
    }

    public String getFallbackClass() {
        return fallbackClass;
    }

    public void setFallbackClass(String fallbackClass) {
        this.fallbackClass = fallbackClass;
    }

    public String getFallbackMethod() {
        return fallbackMethod;
    }

    public void setFallbackMethod(String fallbackMethod) {
        this.fallbackMethod = fallbackMethod;
    }

    public String getDubboGroup() {
        return dubboGroup;
    }

    public void setDubboGroup(String dubboGroup) {
        this.dubboGroup = dubboGroup;
    }

    public String getDubboVersion() {
        return dubboVersion;
    }

    public void setDubboVersion(String dubboVersion) {
        this.dubboVersion = dubboVersion;
    }
}