package com.xiaomi.dayu.model.sentinel;

import com.alibaba.csp.sentinel.slots.block.ClusterRuleConstant;
import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import lombok.Data;

@Data
public class ClusterFlowConfig {

    /**
     * Global unique ID.
     */
    private Long flowId;

    /**
     * Threshold type (average by local value or global value).
     */
    private int thresholdType = ClusterRuleConstant.FLOW_THRESHOLD_AVG_LOCAL;
    private boolean fallbackToLocalWhenFail = true;

    /**
     * 0: normal.
     */
    private int strategy = ClusterRuleConstant.FLOW_CLUSTER_STRATEGY_NORMAL;

    private int sampleCount = ClusterRuleConstant.DEFAULT_CLUSTER_SAMPLE_COUNT;
    /**
     * The time interval length of the statistic sliding window (in milliseconds)
     */
    private int windowIntervalMs = RuleConstant.DEFAULT_WINDOW_INTERVAL_MS;

    /**
     * if the client keep the token for more than resourceTimeout,resourceTimeoutStrategy will work.
     */
    private long resourceTimeout = 2000;

    /**
     * 0:ignore,1:release the token.
     */
    private int resourceTimeoutStrategy = RuleConstant.DEFAULT_RESOURCE_TIMEOUT_STRATEGY;

    /**
     * if the request(prioritized=true) is block,acquireRefuseStrategy will work..
     * 0:ignore and block.
     * 1:try again .
     * 2:try until success.
     */
    private int acquireRefuseStrategy = RuleConstant.DEFAULT_BLOCK_STRATEGY;

    /**
     * if a client is offline,the server will delete all the token the client holds after clientOfflineTime.
     */
    private long clientOfflineTime = 2000;

}