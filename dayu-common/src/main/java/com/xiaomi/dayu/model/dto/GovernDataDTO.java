package com.xiaomi.dayu.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description="接口服务治理数据汇总")
public class GovernDataDTO {
    @ApiModelProperty(name = "服务鉴权汇总")
    private int serviceAuthCount;
    @ApiModelProperty(name = "标签路由汇总")
    private int tagRouteCount;
    @ApiModelProperty(name = "熔断规则汇总")
    private int degradeCount;
    @ApiModelProperty(name = "限流规则汇总")
    private int flowCount;
    @ApiModelProperty(name = "覆盖规则汇总")
    private int overrideCount;
}
