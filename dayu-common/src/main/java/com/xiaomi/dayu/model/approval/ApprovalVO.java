package com.xiaomi.dayu.model.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "审批")
public class ApprovalVO  implements Serializable {
    private Integer id;
    @ApiModelProperty(value = "应用名")
    private String appName;
    @ApiModelProperty(value = "申请类型：10：nacos配置")
    private Integer approveType;
    @ApiModelProperty(value = "操作类型：1：创建，2：删除，3：更新")
    private Integer operateType;
    @ApiModelProperty(value = "审批状态：10：提交，20：通过，30：驳回，40：取消")
    private Integer status;
    @ApiModelProperty(value = "申请人")
    private String applicant;
    @ApiModelProperty(value = "申请备注")
    private String applyRemark;
    @ApiModelProperty(value = "审批人，多个用‘,’连接")
    private String approver;
    @ApiModelProperty(value = "审批操作人")
    private String operator;
    @ApiModelProperty(value = "操作备注")
    private String operateRemark;
    @ApiModelProperty(value = "是否删除")
    private Boolean del;
    @ApiModelProperty(value = "关联key")
    private String relateKey;

    @ApiModelProperty(value = "key")
    private String key;

    private String relateInfo;
    @ApiModelProperty(value = "申请创建时间")
    private Date createTime;
    @ApiModelProperty(value = "最近更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "修改之后的数据")
    private String newData;
    @ApiModelProperty(value = "修改之前的数据")
    private String oldData;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Integer getApproveType() {
        return approveType;
    }

    public void setApproveType(Integer approveType) {
        this.approveType = approveType;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperateRemark() {
        return operateRemark;
    }

    public void setOperateRemark(String operateRemark) {
        this.operateRemark = operateRemark;
    }

    public Boolean getDel() {
        return del;
    }

    public void setDel(Boolean del) {
        this.del = del;
    }

    public String getRelateKey() {
        return relateKey;
    }

    public void setRelateKey(String relateKey) {
        this.relateKey = relateKey;
    }

    public String getRelateInfo() {
        return relateInfo;
    }

    public void setRelateInfo(String relateInfo) {
        this.relateInfo = relateInfo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getNewData() {
        return newData;
    }

    public void setNewData(String newData) {
        this.newData = newData;
    }

    public String getOldData() {
        return oldData;
    }

    public void setOldData(String oldData) {
        this.oldData = oldData;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
