package com.xiaomi.dayu.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ThreadpoolDTO implements Serializable {
    private transient Long id;

    private transient String appName;

    private transient String poolName;

    private int corePoolSize;

    private int maximumPoolSize;

    private int keepAliveTime;

    private int capacity;

    private int reject;
}
