/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.xiaomi.dayu.model.adapter;

import com.xiaomi.dayu.common.util.Constants;
import com.xiaomi.dayu.model.dto.DynamicConfigDTO;
import com.xiaomi.dayu.model.store.OverrideDTO;
import org.apache.commons.lang3.StringUtils;

public class DynamicConfigDTO2OverrideDTOAdapter extends OverrideDTO {
  public DynamicConfigDTO2OverrideDTOAdapter(DynamicConfigDTO dynamicConfigDTO) {
    if (dynamicConfigDTO.getScope().equals("application")) {
      setScope(Constants.APPLICATION);
      setKey(dynamicConfigDTO.getApplication());
    } else {
      setScope(Constants.SERVICE);
      StringBuilder keyStringBuilder = new StringBuilder();
      if(StringUtils.isNotBlank(dynamicConfigDTO.getServiceGroup())){
        keyStringBuilder.append(dynamicConfigDTO.getServiceGroup()).append("/");
      }
      keyStringBuilder.append(dynamicConfigDTO.getService());
      if(StringUtils.isNotBlank(dynamicConfigDTO.getServiceVersion())){
        keyStringBuilder.append(":").append(dynamicConfigDTO.getServiceVersion());
      }
      setKey(keyStringBuilder.toString());
      setService(dynamicConfigDTO.getService());
      setServiceVersion(dynamicConfigDTO.getServiceVersion());
      setServiceGroup(dynamicConfigDTO.getServiceGroup());
    }

    setConfigVersion(dynamicConfigDTO.getConfigVersion());
    setConfigs(dynamicConfigDTO.getConfigs());
  }
}
