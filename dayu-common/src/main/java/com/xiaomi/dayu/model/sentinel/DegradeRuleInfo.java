package com.xiaomi.dayu.model.sentinel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "熔断多条件汇总")
public class DegradeRuleInfo {
    @ApiModelProperty(value = "应用名称")
    private String app;
    @ApiModelProperty(value = "资源名，service")
    private String resource;
    @ApiModelProperty(value = "熔断时长")
    private Integer timeWindow;
    @ApiModelProperty(value = "最小请求数")
    private Integer minRequestAmount;
    @ApiModelProperty(value = "统计时长")
    private Integer statIntervalMs;
    @ApiModelProperty(value = "是否开启")
    private Integer isClose;


    @ApiModelProperty(value = "降级class:method")
    private String defaultFallbackMethod;
    @ApiModelProperty(value = "降级class")
    private String fallbackClass;
    @ApiModelProperty(value = "降级method")
    private String fallbackMethod;

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Integer getTimeWindow() {
        return timeWindow;
    }

    public void setTimeWindow(Integer timeWindow) {
        this.timeWindow = timeWindow;
    }

    public Integer getMinRequestAmount() {
        return minRequestAmount;
    }

    public void setMinRequestAmount(Integer minRequestAmount) {
        this.minRequestAmount = minRequestAmount;
    }

    public Integer getStatIntervalMs() {
        return statIntervalMs;
    }

    public void setStatIntervalMs(Integer statIntervalMs) {
        this.statIntervalMs = statIntervalMs;
    }

    public Integer getIsClose() {
        return isClose;
    }

    public void setIsClose(Integer isClose) {
        this.isClose = isClose;
    }



    public String getFallbackClass() {
        return fallbackClass;
    }

    public void setFallbackClass(String fallbackClass) {
        this.fallbackClass = fallbackClass;
    }

    public String getFallbackMethod() {
        return fallbackMethod;
    }

    public void setFallbackMethod(String fallbackMethod) {
        this.fallbackMethod = fallbackMethod;
    }

    public String getDefaultFallbackMethod() {
        return defaultFallbackMethod;
    }

    public void setDefaultFallbackMethod(String defaultFallbackMethod) {
        this.defaultFallbackMethod = defaultFallbackMethod;
    }
}
