package com.xiaomi.dayu.model.iauth;

import java.util.List;

public class GrantedInfoSummary {
    private String name;
    private List<SwappedWhiteInfo> swappedWhiteInfoList;
    private Integer grantedCount;
    private Integer unGrantedCount;
    private Integer level;



    public List<SwappedWhiteInfo> getSwappedWhiteInfoList() {
        return swappedWhiteInfoList;
    }

    public void setSwappedWhiteInfoList(List<SwappedWhiteInfo> swappedWhiteInfoList) {
        this.swappedWhiteInfoList = swappedWhiteInfoList;
    }

    public Integer getGrantedCount() {
        return grantedCount;
    }

    public void setGrantedCount(Integer grantedCount) {
        this.grantedCount = grantedCount;
    }

    public Integer getUnGrantedCount() {
        return unGrantedCount;
    }

    public void setUnGrantedCount(Integer unGrantedCount) {
        this.unGrantedCount = unGrantedCount;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
