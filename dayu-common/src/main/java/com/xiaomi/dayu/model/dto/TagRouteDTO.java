/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xiaomi.dayu.model.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.config.utils.MD5;
import com.xiaomi.dayu.model.domain.Tag;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

public class TagRouteDTO extends RouteDTO{
    private List<Tag> tags;

    private String md5;

    public List<Tag> getTags() {
        return tags;
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getNewMd5(){
        Collections.sort(tags,(a,b)->a.getName().compareTo(b.getName()));
        for (Tag tag : tags) {
            if(CollectionUtils.isNotEmpty(tag.getAddresses())){
                Collections.sort(tag.getAddresses(),(a,b)->a.compareTo(b));
            }
        }
        return MD5.getInstance().getMD5String(JSON.toJSONString(tags));
    };
}
