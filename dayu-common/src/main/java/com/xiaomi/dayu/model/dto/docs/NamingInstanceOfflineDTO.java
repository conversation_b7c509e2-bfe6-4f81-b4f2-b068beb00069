package com.xiaomi.dayu.model.dto.docs;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Type NamingInstanceOfflineDTO.java
 * @Desc
 * @date 2025/3/26 15:05
 */
public class NamingInstanceOfflineDTO implements Serializable {
	private String serviceName;//: providers:com.xiaomi.dayu.api.service.DataIdProvider:dev_lck
	private String clusterName;//: DEFAULT
	private String groupName;//: DEFAULT_GROUP
	private String ip;//: **************
	private int port;//: 20880
	private boolean ephemeral;//: true
	private double weight;//: 1
	private boolean enabled;//: false
	private String metadata;//: {"side":"provider","service.name":"ServiceBean:dev_lck/com.xiaomi.dayu.api.service.DataIdProvider","methods":"queryDataIdRecord","release":"2.7.12-mone-v14-SNAPSHOT","deprecated":"false","dubbo":"2.0.2","weight":"100","pid":"1","interface":"com.xiaomi.dayu.api.service.DataIdProvider","generic":"false","timeout":"1000","balancing":"random","protocol":"dubbo","metadata-type":"remote","application":"dayu","lb":"random","dynamic":"true","category":"providers","anyhost":"true","group":"dev_lck","timestamp":"1742200108425"}
	private String namespaceId;//:

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public String getClusterName() {
		return clusterName;
	}

	public void setClusterName(String clusterName) {
		this.clusterName = clusterName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getMetadata() {
		return metadata;
	}

	public void setMetadata(String metadata) {
		this.metadata = metadata;
	}

	public String getNamespaceId() {
		return namespaceId;
	}

	public void setNamespaceId(String namespaceId) {
		this.namespaceId = namespaceId;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public boolean isEphemeral() {
		return ephemeral;
	}

	public void setEphemeral(boolean ephemeral) {
		this.ephemeral = ephemeral;
	}

	public double getWeight() {
		return weight;
	}

	public void setWeight(double weight) {
		this.weight = weight;
	}

	public boolean isEnabled() {
		return enabled;
	}

	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}
}
