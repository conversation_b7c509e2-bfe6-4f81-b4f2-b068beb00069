package com.xiaomi.dayu.model.iauth;

import java.io.Serializable;
import java.sql.Timestamp;

public class SwappedWhiteInfo implements Serializable {
    private long id;
    private Timestamp createTime;
    private Timestamp updateTime;
    private String applyUser;
    private Long appId;
    private String appName;
    private String subApp;
    private String serviceId;
    private String subService;
    private String platform;
    private Integer authZStatus; // 授权状态Authorization
    private Integer authNStatus; // 鉴权开关状态Authentication
    private String operator;
    private String message;
    private Integer valid = 1;/*判断记录是否失效, 0: 已失效; 1: 有效*/

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }


    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }


    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getSubApp() {
        return subApp;
    }

    public void setSubApp(String subApp) {
        this.subApp = subApp;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getSubService() {
        return subService;
    }

    public void setSubService(String subService) {
        this.subService = subService;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Integer getAuthZStatus() {
        return authZStatus;
    }

    public void setAuthZStatus(Integer authZStatus) {
        this.authZStatus = authZStatus;
    }

    public Integer getAuthNStatus() {
        return authNStatus;
    }

    public void setAuthNStatus(Integer authNStatus) {
        this.authNStatus = authNStatus;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }



}
