package com.xiaomi.dayu.model.dto;

import com.xiaomi.mone.http.docs.annotations.HttpApiDocClassDefine;
import lombok.Data;

@Data
public class ConditionPair {
    /**
     * 1 Parameter
     * 2 Header
     */
    private Integer paramType;

    private String paramName;

    /**
     * 取值表达式
     */
    @HttpApiDocClassDefine(value = "目标取值表达式",ignore = true)
    private String parseExpr;


    /**
     * 源取值表达式
     */
    @HttpApiDocClassDefine(value = "源取值表达式",description = "paramType为1，Parameter时用的取值表达式",defaultValue = "{data}{goodId::int}")
    private String oriParseExpr;

    /**
     * 1 =
     * 2 包含
     */
    private Integer op;

    private String paramValue;
}
