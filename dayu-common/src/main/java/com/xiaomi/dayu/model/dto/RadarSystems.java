package com.xiaomi.dayu.model.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class RadarSystems {
    private int code;
    private String message;
    private PageData data;
    public RadarSystems() {}

    @Getter
    @Setter
    @NoArgsConstructor
    static public class PageData {

        @JsonAlias("page_size")
        private int pageSize;

        @JsonAlias("total_size")
        private int total;

        @JsonAlias("page_total")
        private int page;

        @JsonAlias("page_num")
        private int pageIndex;

        @JsonAlias("items")
        private List<AppInfo> list;

    }

    @Getter
    @Setter
    @NoArgsConstructor
    static public class AppInfo {
        private int id;

        @JsonAlias("system_name")
        private String name;

        @JsonAlias("desc")
        private String system_description;

        private int iamTreeId;

        private List<Member> members;

        @JsonIgnore
        private String env;

        private boolean isJoined;
        private String createTime;
        private String updateTime;

    }

    @Getter
    @Setter
    @NoArgsConstructor
    static public class Member {
        private String user_id;
        private String user_name;
    }
}
