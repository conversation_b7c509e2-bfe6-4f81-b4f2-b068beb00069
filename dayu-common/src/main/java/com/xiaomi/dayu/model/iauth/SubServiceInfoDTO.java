package com.xiaomi.dayu.model.iauth;

import java.io.Serializable;

public class SubServiceInfoDTO extends SubServiceInfo implements Serializable,Comparable {

    private Integer scopeCount;



    public Integer getScopeCount() {
        return scopeCount;
    }

    public void setScopeCount(Integer scopeCount) {
        this.scopeCount = scopeCount;
    }

    @Override
    public int compareTo(Object object) {
        SubServiceInfoDTO other = (SubServiceInfoDTO) object;
        return Long.compare(other.getId(),this.getId());
    }
}
