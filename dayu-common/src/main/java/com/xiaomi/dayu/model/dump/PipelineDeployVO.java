package com.xiaomi.dayu.model.dump;

import lombok.Data;

import java.io.Serializable;
@Data
public class PipelineDeployVO implements Serializable {
    private String env;
    private long pipelineId;
    private String containerName;
    private int deployType;
    private int status;
    private String ip;
    private String podName;
    private String gitProjectName;
    private String deployMachineName;
    private String deployMachineGroup;
}