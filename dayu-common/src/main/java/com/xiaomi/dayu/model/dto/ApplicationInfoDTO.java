package com.xiaomi.dayu.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class ApplicationInfoDTO implements Serializable,Comparable<ApplicationInfoDTO> {
    private Long id;
    private String name;
    private String address;
    private String type;
    private String description;
    private Date creationDate;
    private Date modifyDate;
    private Integer status;
    private String creator;
    private String updator;
    private String monitorUrl;
    private String logUrl;
    private String traceUrl;
    private int deployGroupNum;
    private int serviceNum;
    private String department;
    private String developers;
    private String framework;
    private String accessType;
    private String gitAddress;


    @Override
    public int compareTo(ApplicationInfoDTO o) {
        return Long.compare(o.getId(),this.id);
    }
}
