package com.xiaomi.dayu.model;

import com.xiaomi.youpin.hermes.entity.Project;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class UserInfo {
    private String userName;
    private List<String> applicationNames;
    private Map<String,Project> applicationMap;
    private boolean isAdmin;
    private int deptId;
    private String userId;
    private String deptName;
    private Integer userType;
    private String tenant;
    private int role;
    private String currEnv;

    public UserInfo() {
    }

    public UserInfo(String userName, List<String> applicationNames, boolean isAdmin, int deptId) {
        this.userName = userName;
        this.applicationNames = applicationNames;
        this.isAdmin = isAdmin;
        this.deptId = deptId;
    }
}

