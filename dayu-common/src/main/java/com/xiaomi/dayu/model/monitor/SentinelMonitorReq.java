package com.xiaomi.dayu.model.monitor;

import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 * @Type SentinelMonitorReq.java
 * @Desc
 * @date 2025/4/8 14:37
 */
public class SentinelMonitorReq implements Serializable {
    private static final long serialVersionUID = -1L;

    private List<String> appNames;

	private List<String> resources;

	private List<String> types;

	public List<String> getAppNames() {
		return appNames;
	}

	public void setAppNames(List<String> appNames) {
		this.appNames = appNames;
	}

	public List<String> getResources() {
		return resources;
	}

	public void setResources(List<String> resources) {
		this.resources = resources;
	}

	public List<String> getTypes() {
		return types;
	}

	public void setTypes(List<String> types) {
		this.types = types;
	}
}
