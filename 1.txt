[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.xiaomi:dayu-service:jar:0.3.2-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.xiaomi:dayu-api:jar -> duplicate declaration of version ${revision} @ com.xiaomi:dayu-service:${revision}, D:\code\xiaomi\dayu\dayu-service\pom.xml, line 39, column 16
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.pszymczyk.consul:embedded-consul:jar -> duplicate declaration of version (?) @ com.xiaomi:dayu-service:${revision}, D:\code\xiaomi\dayu\dayu-service\pom.xml, line 102, column 16
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.springframework.data:spring-data-commons:jar -> duplicate declaration of version (?) @ com.xiaomi:dayu-service:${revision}, D:\code\xiaomi\dayu\dayu-service\pom.xml, line 118, column 16
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.xiaomi:dayu-server:jar:0.3.2-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.springframework.boot:spring-boot-starter-websocket:jar -> duplicate declaration of version (?) @ com.xiaomi:dayu-server:${revision}, D:\code\xiaomi\dayu\dayu-server\pom.xml, line 344, column 15
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] dayu                                                               [pom]
[INFO] dayu-api                                                           [jar]
[INFO] dayu-common                                                        [jar]
[INFO] dayu-dao                                                           [jar]
[INFO] dayu-rpc                                                           [jar]
[INFO] dayu-service                                                       [jar]
[INFO] dayu-server                                                        [jar]
Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/sonarsource/scanner/maven/maven-metadata.xml
Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/maven/plugins/maven-metadata.xml
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/com/xiaomi/sonarsource/scanner/maven/maven-metadata.xml
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/sonarsource/scanner/maven/maven-metadata.xml
Progress (1): 240 B
                   
Downloaded from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/com/xiaomi/sonarsource/scanner/maven/maven-metadata.xml (240 B at 330 B/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/maven/plugins/maven-metadata.xml
Progress (1): 4.1 kB
Progress (1): 8.2 kB
Progress (1): 12 kB 
Progress (1): 15 kB
                   
Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/apache/maven/plugins/maven-metadata.xml (15 kB at 80 kB/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/maven/plugins/maven-metadata.xml
Progress (1): 258 B
                   
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/apache/maven/plugins/maven-metadata.xml (258 B at 4.4 kB/s)
Downloading from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/codehaus/mojo/maven-metadata.xml
Progress (1): 1.2 kB
                    
Downloaded from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/codehaus/mojo/maven-metadata.xml (1.2 kB at 23 kB/s)
Downloading from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/codehaus/mojo/maven-metadata.xml
Progress (1): 879 B
Progress (2): 879 B | 240 B
                           
Downloaded from releases: https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual/org/apache/maven/plugins/maven-metadata.xml (879 B at 741 B/s)
Downloading from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/codehaus/mojo/maven-metadata.xml
Progress (2): 240 B | 4.1 kB
Progress (2): 240 B | 8.2 kB
Progress (2): 240 B | 12 kB 
Progress (2): 240 B | 16 kB
Progress (2): 240 B | 20 kB
Progress (2): 240 B | 21 kB
                           
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/com/xiaomi/sonarsource/scanner/maven/maven-metadata.xml (240 B at 197 B/s)
Progress (2): 21 kB | 587 B
                           
Downloaded from snapshots: https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual/org/codehaus/mojo/maven-metadata.xml (587 B at 8.5 kB/s)
Downloaded from remotes: https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual/org/codehaus/mojo/maven-metadata.xml (21 kB at 95 kB/s)
Downloading from nexus-aliyun: http://maven.aliyun.com/nexus/content/groups/public/com/xiaomi/sonarsource/scanner/maven/maven-metadata.xml
Downloading from nexus-aliyun: http://maven.aliyun.com/nexus/content/groups/public/org/apache/maven/plugins/maven-metadata.xml
Downloading from nexus-aliyun: http://maven.aliyun.com/nexus/content/groups/public/org/codehaus/mojo/maven-metadata.xml
Progress (1): 3.3/21 kB
Progress (2): 3.3/21 kB | 4.1/10 kB
Progress (2): 7.4/21 kB | 4.1/10 kB
Progress (2): 7.4/21 kB | 8.2/10 kB
Progress (2): 12/21 kB | 8.2/10 kB 
Progress (2): 16/21 kB | 8.2/10 kB
Progress (2): 20/21 kB | 8.2/10 kB
Progress (2): 21 kB | 8.2/10 kB   
Progress (2): 21 kB | 10 kB    
                           
Downloaded from nexus-aliyun: http://maven.aliyun.com/nexus/content/groups/public/org/apache/maven/plugins/maven-metadata.xml (10 kB at 59 kB/s)
Downloaded from nexus-aliyun: http://maven.aliyun.com/nexus/content/groups/public/org/codehaus/mojo/maven-metadata.xml (21 kB at 115 kB/s)
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for dayu 0.3.2-SNAPSHOT:
[INFO] 
[INFO] dayu ............................................... SKIPPED
[INFO] dayu-api ........................................... SKIPPED
[INFO] dayu-common ........................................ SKIPPED
[INFO] dayu-dao ........................................... SKIPPED
[INFO] dayu-rpc ........................................... SKIPPED
[INFO] dayu-service ....................................... SKIPPED
[INFO] dayu-server ........................................ SKIPPED
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  2.908 s
[INFO] Finished at: 2025-08-12T17:34:06+08:00
[INFO] ------------------------------------------------------------------------
[ERROR] No plugin found for prefix 'depedency' in the current project and in the plugin groups [com.xiaomi.sonarsource.scanner.maven, org.apache.maven.plugins, org.codehaus.mojo] available from the repositories [local (D:\MavenRepository), releases (https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual), remotes (https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual), snapshots (https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual), nexus-aliyun (http://maven.aliyun.com/nexus/content/groups/public)] -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/NoPluginFoundForPrefixException
