package com.xiaomi.dayu;

import com.xiaomi.dayu.common.util.InstanceUtils;
import com.xiaomi.dayu.model.domain.Consumer;
import com.xiaomi.dayu.model.domain.Provider;
import com.xiaomi.dayu.model.dto.ConsumerServiceDTO;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import org.apache.commons.lang3.StringUtils;

public class InstanceCovert {

    public static Provider namingInstanceCovertToProvider(NamingInstance instance){
        Provider p = new Provider();
//        p.setHash(id);
        p.setService(instance.getFullService());
        p.setAddress(instance.getIp()+":"+instance.getPort());
        p.setPort(instance.getPort()+"");
        p.setApplication(instance.getApplication());
        //p.setUrl(url.toIdentityString());
        p.setParameters(instance.getMetadata());
//        p.setDynamic(url.getParameter("dynamic", true));
        p.setEnabled(instance.getEnabled());
        p.setIp(instance.getIp());
        p.setHealthy(instance.getHealthy());
        p.setWeight(instance.getWeight().intValue());
        p.setMoneCluster(InstanceUtils.getValueFromJson(p.getParameters(),"mone.cluster"));
        p.setNacosServiceName(instance.getServiceName());
//        p.setUsername(url.getParameter("owner"));
        return p;
    }

    public static Consumer namingInstanceCovertToConsumer(NamingInstance instance) {
        Consumer consumer = new Consumer();
        consumer.setService(instance.getFullService());
        consumer.setAddress(instance.getIp());
        consumer.setApplication(instance.getApplication());
        consumer.setParameters(instance.getMetadata());
        consumer.setEnabled(instance.getEnabled());
        consumer.setHealthy(instance.getHealthy());
        return consumer;
    }

    public static ConsumerServiceDTO namingInstanceCovertToConsumerServiceDTO(NamingInstance instance) {
        ConsumerServiceDTO cons = new ConsumerServiceDTO();
        cons.setServiceId(instance.getFullService());
        cons.setService(instance.getDubboService());
        cons.setGroup(instance.getDubboGroup());
        cons.setVersion(instance.getDubboVersion());
        cons.setApplication(instance.getApplication());
        cons.setAddress(instance.getIp());
        if(StringUtils.isNotBlank(instance.getMetadata())){
            cons.setDubboVersion(InstanceUtils.getDubboVersion(instance.getMetadata()));
        }
        return cons;
    }
}
