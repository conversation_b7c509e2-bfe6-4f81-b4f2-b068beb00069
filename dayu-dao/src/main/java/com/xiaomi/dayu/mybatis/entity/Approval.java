package com.xiaomi.dayu.mybatis.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class Approval {
    private Integer id;

    private String appName;

    private Integer approveType;

    private Integer operateType;

    private String relateKey;

    private String relateInfo;

    private Integer status;

    private String applicant;

    private String approver;

    private String operator;

    private String applyRemark;

    private String operateRemark;

    private Date approveTime;

    private Date createTime;

    private Date updateTime;

    private Boolean del;

    private String newData;

    private String oldData;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    public void setApproveType(Integer approveType) {
        this.approveType = approveType;
    }

    public Integer getApproveType() {
        return approveType;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getRelateKey() {
        return relateKey;
    }

    public void setRelateKey(String relateKey) {
        this.relateKey = relateKey == null ? null : relateKey.trim();
    }

    public String getRelateInfo() {
        return relateInfo;
    }

    public void setRelateInfo(String relateInfo) {
        this.relateInfo = relateInfo == null ? null : relateInfo.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant == null ? null : applicant.trim();
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver == null ? null : approver.trim();
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public String getApplyRemark() {
        return applyRemark;
    }

    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark == null ? null : applyRemark.trim();
    }

    public String getOperateRemark() {
        return operateRemark;
    }

    public void setOperateRemark(String operateRemark) {
        this.operateRemark = operateRemark == null ? null : operateRemark.trim();
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDel() {
        return del;
    }

    public void setDel(Boolean del) {
        this.del = del;
    }

    public String getNewData() {
        return newData;
    }

    public void setNewData(String newData) {
        this.newData = newData == null ? null : newData.trim();
    }

    public String getOldData() {
        return oldData;
    }

    public void setOldData(String oldData) {
        this.oldData = oldData == null ? null : oldData.trim();
    }

    public enum Column {
        id("id", "id", "INTEGER", false),
        appName("app_name", "appName", "VARCHAR", false),
        approveType("approve_type", "approveType", "TINYINT", false),
        operateType("operate_type", "operateType", "TINYINT", false),
        relateKey("relate_key", "relateKey", "VARCHAR", false),
        relateInfo("relate_info", "relateInfo", "VARCHAR", false),
        status("status", "status", "TINYINT", false),
        applicant("applicant", "applicant", "VARCHAR", false),
        approver("approver", "approver", "VARCHAR", false),
        operator("operator", "operator", "VARCHAR", false),
        applyRemark("apply_remark", "applyRemark", "VARCHAR", false),
        operateRemark("operate_remark", "operateRemark", "VARCHAR", false),
        approveTime("approve_time", "approveTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        del("del", "del", "TINYINT", false),
        newData("new_data", "newData", "LONGVARCHAR", false),
        oldData("old_data", "oldData", "LONGVARCHAR", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}