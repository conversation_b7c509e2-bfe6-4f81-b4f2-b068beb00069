package com.xiaomi.dayu.mybatis.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * config_info
 * <AUTHOR>
@ApiModel(value="generate.ConfigInfo")
@Data
public class ConfigInfo implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * data_id
     */
    @ApiModelProperty(value="data_id")
    private String dataId;

    private String groupId;

    /**
     * content
     */
    @ApiModelProperty(value="content")
    private String content;

    /**
     * md5
     */
    @ApiModelProperty(value="md5")
    private String md5;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    private Date gmtModified;

    /**
     * source user
     */
    @ApiModelProperty(value="source user")
    private String srcUser;

    /**
     * source ip
     */
    @ApiModelProperty(value="source ip")
    private String srcIp;

    private String appName;

    /**
     * 租户字段
     */
    @ApiModelProperty(value="租户字段")
    private String tenantId;

    private String cDesc;

    private String cUse;

    private String effect;

    private String type;

    private String cSchema;

    private static final long serialVersionUID = 1L;
}