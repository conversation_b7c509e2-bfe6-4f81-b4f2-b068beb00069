package com.xiaomi.dayu.mybatis.entity;

import java.util.ArrayList;
import java.util.Arrays;

public class SwimLane {
    private Integer id;

    private String name;

    private String flowControlTag;

    private Boolean status;

    private Integer swimLaneGroupId;

    private String appEnvJson;

    private String conditionJson;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getFlowControlTag() {
        return flowControlTag;
    }

    public void setFlowControlTag(String flowControlTag) {
        this.flowControlTag = flowControlTag == null ? null : flowControlTag.trim();
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Integer getSwimLaneGroupId() {
        return swimLaneGroupId;
    }

    public void setSwimLaneGroupId(Integer swimLaneGroupId) {
        this.swimLaneGroupId = swimLaneGroupId;
    }

    public String getAppEnvJson() {
        return appEnvJson;
    }

    public void setAppEnvJson(String appEnvJson) {
        this.appEnvJson = appEnvJson == null ? null : appEnvJson.trim();
    }

    public String getConditionJson() {
        return conditionJson;
    }

    public void setConditionJson(String conditionJson) {
        this.conditionJson = conditionJson == null ? null : conditionJson.trim();
    }

    public enum Column {
        id("id", "id", "INTEGER", false),
        name("name", "name", "VARCHAR", false),
        flowControlTag("flow_control_tag", "flowControlTag", "VARCHAR", false),
        status("status", "status", "BIT", false),
        swimLaneGroupId("swim_lane_group_id", "swimLaneGroupId", "INTEGER", false),
        appEnvJson("app_env_json", "appEnvJson", "LONGVARCHAR", false),
        conditionJson("condition_json", "conditionJson", "LONGVARCHAR", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}