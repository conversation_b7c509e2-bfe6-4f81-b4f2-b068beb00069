package com.xiaomi.dayu.mybatis.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NacosHistoryConfigExample {
		protected String orderByClause;
	
		protected boolean distinct;
	
		protected List<Criteria> oredCriteria;
	
		public NacosHistoryConfigExample() {
			oredCriteria = new ArrayList<Criteria>();
		}
	
		public void setOrderByClause(String orderByClause) {
			this.orderByClause = orderByClause;
		}
	
		public String getOrderByClause() {
			return orderByClause;
		}
	
		public void setDistinct(boolean distinct) {
			this.distinct = distinct;
		}
	
		public boolean isDistinct() {
			return distinct;
		}
	
		public List<Criteria> getOredCriteria() {
			return oredCriteria;
		}
	
		public void or(Criteria criteria) {
			oredCriteria.add(criteria);
		}
	
		public Criteria or() {
			Criteria criteria = createCriteriaInternal();
			oredCriteria.add(criteria);
			return criteria;
		}
	
		public Criteria createCriteria() {
			Criteria criteria = createCriteriaInternal();
			if (oredCriteria.size() == 0) {
				oredCriteria.add(criteria);
			}
			return criteria;
		}
	
		protected Criteria createCriteriaInternal() {
			Criteria criteria = new Criteria();
			return criteria;
		}
	
		public void clear() {
			oredCriteria.clear();
			orderByClause = null;
			distinct = false;
		}
	
		protected abstract static class GeneratedCriteria {
			protected List<Criterion> criteria;
	
			protected GeneratedCriteria() {
				super();
				criteria = new ArrayList<Criterion>();
			}
	
			public boolean isValid() {
				return criteria.size() > 0;
			}
	
			public List<Criterion> getAllCriteria() {
				return criteria;
			}
	
			public List<Criterion> getCriteria() {
				return criteria;
			}
	
			protected void addCriterion(String condition) {
				if (condition == null) {
					throw new RuntimeException("Value for condition cannot be null");
				}
				criteria.add(new Criterion(condition));
			}
	
			protected void addCriterion(String condition, Object value, String property) {
				if (value == null) {
					throw new RuntimeException("Value for " + property + " cannot be null");
				}
				criteria.add(new Criterion(condition, value));
			}
	
			protected void addCriterion(String condition, Object value1, Object value2, String property) {
				if (value1 == null || value2 == null) {
					throw new RuntimeException("Between values for " + property + " cannot be null");
				}
				criteria.add(new Criterion(condition, value1, value2));
			}
	
			public Criteria andIdIsNull() {
				addCriterion("id is null");
				return (Criteria) this;
			}
	
			public Criteria andIdIsNotNull() {
				addCriterion("id is not null");
				return (Criteria) this;
			}
	
			public Criteria andIdEqualTo(Long value) {
				addCriterion("id =", value, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdNotEqualTo(Long value) {
				addCriterion("id <>", value, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdGreaterThan(Long value) {
				addCriterion("id >", value, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdGreaterThanOrEqualTo(Long value) {
				addCriterion("id >=", value, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdLessThan(Long value) {
				addCriterion("id <", value, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdLessThanOrEqualTo(Long value) {
				addCriterion("id <=", value, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdIn(List<Long> values) {
				addCriterion("id in", values, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdNotIn(List<Long> values) {
				addCriterion("id not in", values, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdBetween(Long value1, Long value2) {
				addCriterion("id between", value1, value2, "id");
				return (Criteria) this;
			}
	
			public Criteria andIdNotBetween(Long value1, Long value2) {
				addCriterion("id not between", value1, value2, "id");
				return (Criteria) this;
			}
	
			public Criteria andNidIsNull() {
				addCriterion("nid is null");
				return (Criteria) this;
			}
	
			public Criteria andNidIsNotNull() {
				addCriterion("nid is not null");
				return (Criteria) this;
			}
	
			public Criteria andNidEqualTo(Long value) {
				addCriterion("nid =", value, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidNotEqualTo(Long value) {
				addCriterion("nid <>", value, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidGreaterThan(Long value) {
				addCriterion("nid >", value, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidGreaterThanOrEqualTo(Long value) {
				addCriterion("nid >=", value, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidLessThan(Long value) {
				addCriterion("nid <", value, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidLessThanOrEqualTo(Long value) {
				addCriterion("nid <=", value, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidIn(List<Long> values) {
				addCriterion("nid in", values, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidNotIn(List<Long> values) {
				addCriterion("nid not in", values, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidBetween(Long value1, Long value2) {
				addCriterion("nid between", value1, value2, "nid");
				return (Criteria) this;
			}
	
			public Criteria andNidNotBetween(Long value1, Long value2) {
				addCriterion("nid not between", value1, value2, "nid");
				return (Criteria) this;
			}
	
			public Criteria andDataIdIsNull() {
				addCriterion("data_id is null");
				return (Criteria) this;
			}
	
			public Criteria andDataIdIsNotNull() {
				addCriterion("data_id is not null");
				return (Criteria) this;
			}
	
			public Criteria andDataIdEqualTo(String value) {
				addCriterion("data_id =", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdNotEqualTo(String value) {
				addCriterion("data_id <>", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdGreaterThan(String value) {
				addCriterion("data_id >", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdGreaterThanOrEqualTo(String value) {
				addCriterion("data_id >=", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdLessThan(String value) {
				addCriterion("data_id <", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdLessThanOrEqualTo(String value) {
				addCriterion("data_id <=", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdLike(String value) {
				addCriterion("data_id like", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdNotLike(String value) {
				addCriterion("data_id not like", value, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdIn(List<String> values) {
				addCriterion("data_id in", values, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdNotIn(List<String> values) {
				addCriterion("data_id not in", values, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdBetween(String value1, String value2) {
				addCriterion("data_id between", value1, value2, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andDataIdNotBetween(String value1, String value2) {
				addCriterion("data_id not between", value1, value2, "dataId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdIsNull() {
				addCriterion("group_id is null");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdIsNotNull() {
				addCriterion("group_id is not null");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdEqualTo(String value) {
				addCriterion("group_id =", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdNotEqualTo(String value) {
				addCriterion("group_id <>", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdGreaterThan(String value) {
				addCriterion("group_id >", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdGreaterThanOrEqualTo(String value) {
				addCriterion("group_id >=", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdLessThan(String value) {
				addCriterion("group_id <", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdLessThanOrEqualTo(String value) {
				addCriterion("group_id <=", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdLike(String value) {
				addCriterion("group_id like", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdNotLike(String value) {
				addCriterion("group_id not like", value, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdIn(List<String> values) {
				addCriterion("group_id in", values, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdNotIn(List<String> values) {
				addCriterion("group_id not in", values, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdBetween(String value1, String value2) {
				addCriterion("group_id between", value1, value2, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andGroupIdNotBetween(String value1, String value2) {
				addCriterion("group_id not between", value1, value2, "groupId");
				return (Criteria) this;
			}
	
			public Criteria andAppNameIsNull() {
				addCriterion("app_name is null");
				return (Criteria) this;
			}
	
			public Criteria andAppNameIsNotNull() {
				addCriterion("app_name is not null");
				return (Criteria) this;
			}
	
			public Criteria andAppNameEqualTo(String value) {
				addCriterion("app_name =", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameNotEqualTo(String value) {
				addCriterion("app_name <>", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameGreaterThan(String value) {
				addCriterion("app_name >", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameGreaterThanOrEqualTo(String value) {
				addCriterion("app_name >=", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameLessThan(String value) {
				addCriterion("app_name <", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameLessThanOrEqualTo(String value) {
				addCriterion("app_name <=", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameLike(String value) {
				addCriterion("app_name like", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameNotLike(String value) {
				addCriterion("app_name not like", value, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameIn(List<String> values) {
				addCriterion("app_name in", values, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameNotIn(List<String> values) {
				addCriterion("app_name not in", values, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameBetween(String value1, String value2) {
				addCriterion("app_name between", value1, value2, "appName");
				return (Criteria) this;
			}
	
			public Criteria andAppNameNotBetween(String value1, String value2) {
				addCriterion("app_name not between", value1, value2, "appName");
				return (Criteria) this;
			}
	
			public Criteria andMd5IsNull() {
				addCriterion("md5 is null");
				return (Criteria) this;
			}
	
			public Criteria andMd5IsNotNull() {
				addCriterion("md5 is not null");
				return (Criteria) this;
			}
	
			public Criteria andMd5EqualTo(String value) {
				addCriterion("md5 =", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5NotEqualTo(String value) {
				addCriterion("md5 <>", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5GreaterThan(String value) {
				addCriterion("md5 >", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5GreaterThanOrEqualTo(String value) {
				addCriterion("md5 >=", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5LessThan(String value) {
				addCriterion("md5 <", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5LessThanOrEqualTo(String value) {
				addCriterion("md5 <=", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5Like(String value) {
				addCriterion("md5 like", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5NotLike(String value) {
				addCriterion("md5 not like", value, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5In(List<String> values) {
				addCriterion("md5 in", values, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5NotIn(List<String> values) {
				addCriterion("md5 not in", values, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5Between(String value1, String value2) {
				addCriterion("md5 between", value1, value2, "md5");
				return (Criteria) this;
			}
	
			public Criteria andMd5NotBetween(String value1, String value2) {
				addCriterion("md5 not between", value1, value2, "md5");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateIsNull() {
				addCriterion("gmt_create is null");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateIsNotNull() {
				addCriterion("gmt_create is not null");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateEqualTo(Date value) {
				addCriterion("gmt_create =", value, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateNotEqualTo(Date value) {
				addCriterion("gmt_create <>", value, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateGreaterThan(Date value) {
				addCriterion("gmt_create >", value, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
				addCriterion("gmt_create >=", value, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateLessThan(Date value) {
				addCriterion("gmt_create <", value, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
				addCriterion("gmt_create <=", value, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateIn(List<Date> values) {
				addCriterion("gmt_create in", values, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateNotIn(List<Date> values) {
				addCriterion("gmt_create not in", values, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateBetween(Date value1, Date value2) {
				addCriterion("gmt_create between", value1, value2, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
				addCriterion("gmt_create not between", value1, value2, "gmtCreate");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedIsNull() {
				addCriterion("gmt_modified is null");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedIsNotNull() {
				addCriterion("gmt_modified is not null");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedEqualTo(Date value) {
				addCriterion("gmt_modified =", value, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedNotEqualTo(Date value) {
				addCriterion("gmt_modified <>", value, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedGreaterThan(Date value) {
				addCriterion("gmt_modified >", value, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
				addCriterion("gmt_modified >=", value, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedLessThan(Date value) {
				addCriterion("gmt_modified <", value, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
				addCriterion("gmt_modified <=", value, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedIn(List<Date> values) {
				addCriterion("gmt_modified in", values, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedNotIn(List<Date> values) {
				addCriterion("gmt_modified not in", values, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedBetween(Date value1, Date value2) {
				addCriterion("gmt_modified between", value1, value2, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
				addCriterion("gmt_modified not between", value1, value2, "gmtModified");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpIsNull() {
				addCriterion("src_ip is null");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpIsNotNull() {
				addCriterion("src_ip is not null");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpEqualTo(String value) {
				addCriterion("src_ip =", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpNotEqualTo(String value) {
				addCriterion("src_ip <>", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpGreaterThan(String value) {
				addCriterion("src_ip >", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpGreaterThanOrEqualTo(String value) {
				addCriterion("src_ip >=", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpLessThan(String value) {
				addCriterion("src_ip <", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpLessThanOrEqualTo(String value) {
				addCriterion("src_ip <=", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpLike(String value) {
				addCriterion("src_ip like", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpNotLike(String value) {
				addCriterion("src_ip not like", value, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpIn(List<String> values) {
				addCriterion("src_ip in", values, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpNotIn(List<String> values) {
				addCriterion("src_ip not in", values, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpBetween(String value1, String value2) {
				addCriterion("src_ip between", value1, value2, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andSrcIpNotBetween(String value1, String value2) {
				addCriterion("src_ip not between", value1, value2, "srcIp");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeIsNull() {
				addCriterion("op_type is null");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeIsNotNull() {
				addCriterion("op_type is not null");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeEqualTo(String value) {
				addCriterion("op_type =", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeNotEqualTo(String value) {
				addCriterion("op_type <>", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeGreaterThan(String value) {
				addCriterion("op_type >", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeGreaterThanOrEqualTo(String value) {
				addCriterion("op_type >=", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeLessThan(String value) {
				addCriterion("op_type <", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeLessThanOrEqualTo(String value) {
				addCriterion("op_type <=", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeLike(String value) {
				addCriterion("op_type like", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeNotLike(String value) {
				addCriterion("op_type not like", value, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeIn(List<String> values) {
				addCriterion("op_type in", values, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeNotIn(List<String> values) {
				addCriterion("op_type not in", values, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeBetween(String value1, String value2) {
				addCriterion("op_type between", value1, value2, "opType");
				return (Criteria) this;
			}
	
			public Criteria andOpTypeNotBetween(String value1, String value2) {
				addCriterion("op_type not between", value1, value2, "opType");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdIsNull() {
				addCriterion("tenant_id is null");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdIsNotNull() {
				addCriterion("tenant_id is not null");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdEqualTo(String value) {
				addCriterion("tenant_id =", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdNotEqualTo(String value) {
				addCriterion("tenant_id <>", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdGreaterThan(String value) {
				addCriterion("tenant_id >", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
				addCriterion("tenant_id >=", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdLessThan(String value) {
				addCriterion("tenant_id <", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdLessThanOrEqualTo(String value) {
				addCriterion("tenant_id <=", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdLike(String value) {
				addCriterion("tenant_id like", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdNotLike(String value) {
				addCriterion("tenant_id not like", value, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdIn(List<String> values) {
				addCriterion("tenant_id in", values, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdNotIn(List<String> values) {
				addCriterion("tenant_id not in", values, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdBetween(String value1, String value2) {
				addCriterion("tenant_id between", value1, value2, "tenantId");
				return (Criteria) this;
			}
	
			public Criteria andTenantIdNotBetween(String value1, String value2) {
				addCriterion("tenant_id not between", value1, value2, "tenantId");
				return (Criteria) this;
			}
		}
	
		public static class Criteria extends GeneratedCriteria {
	
			protected Criteria() {
				super();
			}
		}
	
		public static class Criterion {
			private String condition;
	
			private Object value;
	
			private Object secondValue;
	
			private boolean noValue;
	
			private boolean singleValue;
	
			private boolean betweenValue;
	
			private boolean listValue;
	
			private String typeHandler;
	
			public String getCondition() {
				return condition;
			}
	
			public Object getValue() {
				return value;
			}
	
			public Object getSecondValue() {
				return secondValue;
			}
	
			public boolean isNoValue() {
				return noValue;
			}
	
			public boolean isSingleValue() {
				return singleValue;
			}
	
			public boolean isBetweenValue() {
				return betweenValue;
			}
	
			public boolean isListValue() {
				return listValue;
			}
	
			public String getTypeHandler() {
				return typeHandler;
			}
	
			protected Criterion(String condition) {
				super();
				this.condition = condition;
				this.typeHandler = null;
				this.noValue = true;
			}
	
			protected Criterion(String condition, Object value, String typeHandler) {
				super();
				this.condition = condition;
				this.value = value;
				this.typeHandler = typeHandler;
				if (value instanceof List<?>) {
					this.listValue = true;
				} else {
					this.singleValue = true;
				}
			}
	
			protected Criterion(String condition, Object value) {
				this(condition, value, null);
			}
	
			protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
				super();
				this.condition = condition;
				this.value = value;
				this.secondValue = secondValue;
				this.typeHandler = typeHandler;
				this.betweenValue = true;
			}
	
			protected Criterion(String condition, Object value, Object secondValue) {
				this(condition, value, secondValue, null);
			}
		}
	}