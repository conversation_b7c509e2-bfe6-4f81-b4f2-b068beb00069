package com.xiaomi.dayu.mybatis.entity;

import lombok.Data;

import java.sql.Date;

@Data
public class ConfigInfoExtend {
    private Long id;
    private String dataId;
    private Long configInfoId;
    private String envId;
    private String envName;
    private Integer configType;
    private Date createTime;

    public ConfigInfoExtend() {
    }

    public ConfigInfoExtend(String dataId, long configInfoId, String envId, String envName, Integer configType) {
        this.dataId = dataId;
        this.configInfoId = configInfoId;
        this.envId = envId;
        this.envName = envName;
        this.configType = configType;
    }
}
