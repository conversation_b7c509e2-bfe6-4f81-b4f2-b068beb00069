package com.xiaomi.dayu.mybatis.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class NamingInstance {
    private Integer id;

    private String instanceId;

    private String serviceName;

    private String dubboService;

    private String dubboGroup;

    private String dubboVersion;

    private String fullService;

    private String application;

    private String side;

    private String ip;

    private Integer port;

    private Double weight;

    private Boolean healthy;

    private Boolean enabled;

    private Boolean ephemeral;

    private String clusterName;

    private String namespaceId;

    private String groupName;

    private String metadata;

    private String md5;

    private Date lastBeatTime;

    private Date createTime;

    private Date updateTime;

    private Boolean del;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId == null ? null : instanceId.trim();
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName == null ? null : serviceName.trim();
    }

    public String getDubboService() {
        return dubboService;
    }

    public void setDubboService(String dubboService) {
        this.dubboService = dubboService == null ? null : dubboService.trim();
    }

    public String getDubboGroup() {
        return dubboGroup;
    }

    public void setDubboGroup(String dubboGroup) {
        this.dubboGroup = dubboGroup == null ? null : dubboGroup.trim();
    }

    public String getDubboVersion() {
        return dubboVersion;
    }

    public void setDubboVersion(String dubboVersion) {
        this.dubboVersion = dubboVersion == null ? null : dubboVersion.trim();
    }

    public String getFullService() {
        return fullService;
    }

    public void setFullService(String fullService) {
        this.fullService = fullService == null ? null : fullService.trim();
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application == null ? null : application.trim();
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side == null ? null : side.trim();
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip == null ? null : ip.trim();
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Boolean getHealthy() {
        return healthy;
    }

    public void setHealthy(Boolean healthy) {
        this.healthy = healthy;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getEphemeral() {
        return ephemeral;
    }

    public void setEphemeral(Boolean ephemeral) {
        this.ephemeral = ephemeral;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName == null ? null : clusterName.trim();
    }

    public String getNamespaceId() {
        return namespaceId;
    }

    public void setNamespaceId(String namespaceId) {
        this.namespaceId = namespaceId == null ? null : namespaceId.trim();
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata == null ? null : metadata.trim();
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5 == null ? null : md5.trim();
    }

    public Date getLastBeatTime() {
        return lastBeatTime;
    }

    public void setLastBeatTime(Date lastBeatTime) {
        this.lastBeatTime = lastBeatTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDel() {
        return del;
    }

    public void setDel(Boolean del) {
        this.del = del;
    }

    public enum Column {
        id("id", "id", "INTEGER", false),
        instanceId("instance_id", "instanceId", "VARCHAR", false),
        serviceName("service_name", "serviceName", "VARCHAR", false),
        dubboService("dubbo_service", "dubboService", "VARCHAR", false),
        dubboGroup("dubbo_group", "dubboGroup", "VARCHAR", false),
        dubboVersion("dubbo_version", "dubboVersion", "VARCHAR", false),
        fullService("full_service", "fullService", "VARCHAR", false),
        application("application", "application", "VARCHAR", false),
        side("side", "side", "VARCHAR", false),
        ip("ip", "ip", "VARCHAR", false),
        port("port", "port", "INTEGER", false),
        weight("weight", "weight", "DOUBLE", false),
        healthy("healthy", "healthy", "BIT", false),
        enabled("enabled", "enabled", "BIT", false),
        ephemeral("ephemeral", "ephemeral", "BIT", false),
        clusterName("cluster_name", "clusterName", "VARCHAR", false),
        namespaceId("namespace_id", "namespaceId", "VARCHAR", false),
        groupName("group_name", "groupName", "VARCHAR", false),
        metadata("metadata", "metadata", "VARCHAR", false),
        md5("md5", "md5", "VARCHAR", false),
        lastBeatTime("last_beat_time", "lastBeatTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        del("del", "del", "BIT", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}