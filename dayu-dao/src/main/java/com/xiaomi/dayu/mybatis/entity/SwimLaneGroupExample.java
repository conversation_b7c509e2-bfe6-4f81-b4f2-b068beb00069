package com.xiaomi.dayu.mybatis.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SwimLaneGroupExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SwimLaneGroupExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAppListIsNull() {
            addCriterion("app_list is null");
            return (Criteria) this;
        }

        public Criteria andAppListIsNotNull() {
            addCriterion("app_list is not null");
            return (Criteria) this;
        }

        public Criteria andAppListEqualTo(String value) {
            addCriterion("app_list =", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListNotEqualTo(String value) {
            addCriterion("app_list <>", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListGreaterThan(String value) {
            addCriterion("app_list >", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListGreaterThanOrEqualTo(String value) {
            addCriterion("app_list >=", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListLessThan(String value) {
            addCriterion("app_list <", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListLessThanOrEqualTo(String value) {
            addCriterion("app_list <=", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListLike(String value) {
            addCriterion("app_list like", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListNotLike(String value) {
            addCriterion("app_list not like", value, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListIn(List<String> values) {
            addCriterion("app_list in", values, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListNotIn(List<String> values) {
            addCriterion("app_list not in", values, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListBetween(String value1, String value2) {
            addCriterion("app_list between", value1, value2, "appList");
            return (Criteria) this;
        }

        public Criteria andAppListNotBetween(String value1, String value2) {
            addCriterion("app_list not between", value1, value2, "appList");
            return (Criteria) this;
        }

        public Criteria andDescpIsNull() {
            addCriterion("descp is null");
            return (Criteria) this;
        }

        public Criteria andDescpIsNotNull() {
            addCriterion("descp is not null");
            return (Criteria) this;
        }

        public Criteria andDescpEqualTo(String value) {
            addCriterion("descp =", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpNotEqualTo(String value) {
            addCriterion("descp <>", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpGreaterThan(String value) {
            addCriterion("descp >", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpGreaterThanOrEqualTo(String value) {
            addCriterion("descp >=", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpLessThan(String value) {
            addCriterion("descp <", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpLessThanOrEqualTo(String value) {
            addCriterion("descp <=", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpLike(String value) {
            addCriterion("descp like", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpNotLike(String value) {
            addCriterion("descp not like", value, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpIn(List<String> values) {
            addCriterion("descp in", values, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpNotIn(List<String> values) {
            addCriterion("descp not in", values, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpBetween(String value1, String value2) {
            addCriterion("descp between", value1, value2, "descp");
            return (Criteria) this;
        }

        public Criteria andDescpNotBetween(String value1, String value2) {
            addCriterion("descp not between", value1, value2, "descp");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andEntranceAppIsNull() {
            addCriterion("entrance_app is null");
            return (Criteria) this;
        }

        public Criteria andEntranceAppIsNotNull() {
            addCriterion("entrance_app is not null");
            return (Criteria) this;
        }

        public Criteria andEntranceAppEqualTo(String value) {
            addCriterion("entrance_app =", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppNotEqualTo(String value) {
            addCriterion("entrance_app <>", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppGreaterThan(String value) {
            addCriterion("entrance_app >", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppGreaterThanOrEqualTo(String value) {
            addCriterion("entrance_app >=", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppLessThan(String value) {
            addCriterion("entrance_app <", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppLessThanOrEqualTo(String value) {
            addCriterion("entrance_app <=", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppLike(String value) {
            addCriterion("entrance_app like", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppNotLike(String value) {
            addCriterion("entrance_app not like", value, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppIn(List<String> values) {
            addCriterion("entrance_app in", values, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppNotIn(List<String> values) {
            addCriterion("entrance_app not in", values, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppBetween(String value1, String value2) {
            addCriterion("entrance_app between", value1, value2, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andEntranceAppNotBetween(String value1, String value2) {
            addCriterion("entrance_app not between", value1, value2, "entranceApp");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderIsNull() {
            addCriterion("prefix_header is null");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderIsNotNull() {
            addCriterion("prefix_header is not null");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderEqualTo(String value) {
            addCriterion("prefix_header =", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderNotEqualTo(String value) {
            addCriterion("prefix_header <>", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderGreaterThan(String value) {
            addCriterion("prefix_header >", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderGreaterThanOrEqualTo(String value) {
            addCriterion("prefix_header >=", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderLessThan(String value) {
            addCriterion("prefix_header <", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderLessThanOrEqualTo(String value) {
            addCriterion("prefix_header <=", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderLike(String value) {
            addCriterion("prefix_header like", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderNotLike(String value) {
            addCriterion("prefix_header not like", value, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderIn(List<String> values) {
            addCriterion("prefix_header in", values, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderNotIn(List<String> values) {
            addCriterion("prefix_header not in", values, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderBetween(String value1, String value2) {
            addCriterion("prefix_header between", value1, value2, "prefixHeader");
            return (Criteria) this;
        }

        public Criteria andPrefixHeaderNotBetween(String value1, String value2) {
            addCriterion("prefix_header not between", value1, value2, "prefixHeader");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}