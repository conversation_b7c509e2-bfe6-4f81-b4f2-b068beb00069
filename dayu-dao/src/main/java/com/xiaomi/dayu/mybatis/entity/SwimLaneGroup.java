package com.xiaomi.dayu.mybatis.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class SwimLaneGroup {
    private Integer id;

    private String name;

    private String appList;

    private String descp;

    private String creator;

    private Date createTime;

    private Integer type;

    private String entranceApp;

    private String prefixHeader;

    private String appTopicToGroups;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getAppList() {
        return appList;
    }

    public void setAppList(String appList) {
        this.appList = appList == null ? null : appList.trim();
    }

    public String getDescp() {
        return descp;
    }

    public void setDescp(String descp) {
        this.descp = descp == null ? null : descp.trim();
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getEntranceApp() {
        return entranceApp;
    }

    public void setEntranceApp(String entranceApp) {
        this.entranceApp = entranceApp == null ? null : entranceApp.trim();
    }

    public String getPrefixHeader() {
        return prefixHeader;
    }

    public void setPrefixHeader(String prefixHeader) {
        this.prefixHeader = prefixHeader == null ? null : prefixHeader.trim();
    }

    public String getAppTopicToGroups() {
        return appTopicToGroups;
    }

    public void setAppTopicToGroups(String appTopicToGroups) {
        this.appTopicToGroups = appTopicToGroups == null ? null : appTopicToGroups.trim();
    }

    public enum Column {
        id("id", "id", "INTEGER", false),
        name("name", "name", "VARCHAR", false),
        appList("app_list", "appList", "VARCHAR", false),
        descp("descp", "descp", "VARCHAR", false),
        creator("creator", "creator", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        type("type", "type", "INTEGER", false),
        entranceApp("entrance_app", "entranceApp", "VARCHAR", false),
        prefixHeader("prefix_header", "prefixHeader", "VARCHAR", false),
        appTopicToGroups("app_topic_to_groups", "appTopicToGroups", "LONGVARCHAR", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}