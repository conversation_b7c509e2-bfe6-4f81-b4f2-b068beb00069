package com.xiaomi.dayu.mybatis.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class ThreadpoolConfigHistory implements Serializable {

    private Long id;

    private Long configID;

    private String user;

    private String action;

    private String content;

    private String dataID;

    public ThreadpoolConfigHistory(Long configID, String user, String action, String content, String dataID) {
        this.configID = configID;
        this.user = user;
        this.action = action;
        this.content = content;
        this.dataID = dataID;
    }

}
