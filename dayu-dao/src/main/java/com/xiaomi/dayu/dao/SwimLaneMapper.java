package com.xiaomi.dayu.dao;

import com.xiaomi.dayu.mybatis.entity.SwimLane;
import com.xiaomi.dayu.mybatis.entity.SwimLaneExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SwimLaneMapper {
    long countByExample(SwimLaneExample example);

    int deleteByExample(SwimLaneExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SwimLane record);

    int insertSelective(SwimLane record);

    List<SwimLane> selectByExampleWithBLOBs(SwimLaneExample example);

    List<SwimLane> selectByExample(SwimLaneExample example);

    SwimLane selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SwimLane record, @Param("example") SwimLaneExample example);

    int updateByExampleWithBLOBs(@Param("record") SwimLane record, @Param("example") SwimLaneExample example);

    int updateByExample(@Param("record") SwimLane record, @Param("example") SwimLaneExample example);

    int updateByPrimaryKeySelective(SwimLane record);

    int updateByPrimaryKeyWithBLOBs(SwimLane record);

    int updateByPrimaryKey(SwimLane record);

    int batchInsert(@Param("list") List<SwimLane> list);

    int batchInsertSelective(@Param("list") List<SwimLane> list, @Param("selective") SwimLane.Column ... selective);
}