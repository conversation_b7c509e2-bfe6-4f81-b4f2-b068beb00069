package com.xiaomi.dayu.dao;

import java.util.List;

import com.xiaomi.dayu.mybatis.entity.SwimLaneGroup;
import com.xiaomi.dayu.mybatis.entity.SwimLaneGroupExample;
import org.apache.ibatis.annotations.Param;

public interface SwimLaneGroupMapper {
    long countByExample(SwimLaneGroupExample example);

    int deleteByExample(SwimLaneGroupExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SwimLaneGroup record);

    int insertSelective(SwimLaneGroup record);

    List<SwimLaneGroup> selectByExample(SwimLaneGroupExample example);

    SwimLaneGroup selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SwimLaneGroup record, @Param("example") SwimLaneGroupExample example);

    int updateByExample(@Param("record") SwimLaneGroup record, @Param("example") SwimLaneGroupExample example);

    int updateByPrimaryKeySelective(SwimLaneGroup record);

    int updateByPrimaryKey(SwimLaneGroup record);

    int batchInsert(@Param("list") List<SwimLaneGroup> list);

    int batchInsertSelective(@Param("list") List<SwimLaneGroup> list, @Param("selective") SwimLaneGroup.Column ... selective);
}