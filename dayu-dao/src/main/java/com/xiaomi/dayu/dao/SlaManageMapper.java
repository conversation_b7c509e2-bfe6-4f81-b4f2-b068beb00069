package com.xiaomi.dayu.dao;

import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.mybatis.entity.SlaManage;
import com.xiaomi.dayu.mybatis.example.SlaManageExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SlaManageMapper {
    long countByExample(SlaManageExample example);

    int deleteByExample(SlaManageExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SlaManage record);

    int insertSelective(SlaManage record);

    List<SlaManage> selectByExample(SlaManageExample example);

    SlaManage selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SlaManage record, @Param("example") SlaManageExample example);

    int updateByExample(@Param("record") SlaManage record, @Param("example") SlaManageExample example);

    int updateByPrimaryKeySelective(SlaManage record);

    int updateByPrimaryKey(SlaManage record);

    int batchInsert(@Param("list") List<SlaManage> list);

    int batchInsertSelective(@Param("list") List<SlaManage> list, @Param("selective") SlaManage.Column ... selective);

    // 自定义
    List<SlaManage> selectByExampleWithRowbounds(SlaManageExample example, PageRowBounds pageRowBounds);

}