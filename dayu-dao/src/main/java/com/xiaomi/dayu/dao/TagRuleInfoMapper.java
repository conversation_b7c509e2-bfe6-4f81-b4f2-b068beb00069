package com.xiaomi.dayu.dao;

import java.util.List;

import com.xiaomi.dayu.mybatis.entity.TagRuleInfo;
import com.xiaomi.dayu.mybatis.entity.TagRuleInfoExample;
import org.apache.ibatis.annotations.Param;

public interface TagRuleInfoMapper {
    long countByExample(TagRuleInfoExample example);

    int deleteByExample(TagRuleInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TagRuleInfo record);

    int insertSelective(TagRuleInfo record);

    List<TagRuleInfo> selectByExampleWithBLOBs(TagRuleInfoExample example);

    List<TagRuleInfo> selectByExample(TagRuleInfoExample example);

    TagRuleInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TagRuleInfo record, @Param("example") TagRuleInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") TagRuleInfo record, @Param("example") TagRuleInfoExample example);

    int updateByExample(@Param("record") TagRuleInfo record, @Param("example") TagRuleInfoExample example);

    int updateByPrimaryKeySelective(TagRuleInfo record);

    int updateByPrimaryKeyWithBLOBs(TagRuleInfo record);

    int updateByPrimaryKey(TagRuleInfo record);

    int batchInsert(@Param("list") List<TagRuleInfo> list);

    int batchInsertSelective(@Param("list") List<TagRuleInfo> list, @Param("selective") TagRuleInfo.Column ... selective);

}