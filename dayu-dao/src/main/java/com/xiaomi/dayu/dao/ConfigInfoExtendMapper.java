package com.xiaomi.dayu.dao;

import com.xiaomi.dayu.mybatis.entity.ConfigInfoExtend;
import com.xiaomi.dayu.mybatis.example.ConfigInfoExtendExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ConfigInfoExtendMapper extends BaseMapper<ConfigInfoExtend, Integer, ConfigInfoExtendExample> {
    ConfigInfoExtend queryByConfigInfoId(@Param("id") Long id);

    List<ConfigInfoExtend> queryByConfigInfoIds(@Param("configInfoIds") List<Long> configInfoIds);

    int insertOrUpdate(ConfigInfoExtend record);
}
