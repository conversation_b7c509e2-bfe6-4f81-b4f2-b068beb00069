package com.xiaomi.dayu.dao;

import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.model.dto.ConfigInfoAndExtendDTO;
import com.xiaomi.dayu.mybatis.entity.ConfigInfo;
import com.xiaomi.dayu.mybatis.example.ConfigInfoExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ConfigInfoMapper extends BaseMapper<ConfigInfo, Integer, ConfigInfoExample> {
    List<ConfigInfo> fuzzySearchConfig(@Param("appNameList") List<String> appNameList, @Param("congfigType") String congfigType);

    List<ConfigInfoAndExtendDTO> queryConfigAndExtendListByAppName(@Param("appName") String appName);

    List<ConfigInfo> searchConfig(ConfigInfoExample example);

    ConfigInfo selectByPrimaryKey(Long id);

    List<ConfigInfoAndExtendDTO> searchConfigAndExtend(ConfigInfoExample example);

    long countConfigAndExtend(ConfigInfoExample example);

    int queryCountByAppNameNotEmpty();

    Long searchConfigAndExtendTotal(ConfigInfoExample example);

    List<ConfigInfo> queryConfigPage(ConfigInfoExample example, PageRowBounds pageRowBounds);
}
