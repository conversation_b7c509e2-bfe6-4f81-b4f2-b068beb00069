package com.xiaomi.dayu.dao;

import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.mybatis.entity.Approval;
import com.xiaomi.dayu.mybatis.example.ApprovalExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface ApprovalMapper {

    long countByExample(ApprovalExample example);

    int deleteByExample(ApprovalExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(Approval record);

    int insertSelective(Approval record);

    List<Approval> selectByExampleWithBLOBs(ApprovalExample example);

    List<Approval> selectByExample(ApprovalExample example);

    Approval selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") Approval record, @Param("example") ApprovalExample example);

    int updateByExampleWithBLOBs(@Param("record") Approval record, @Param("example") ApprovalExample example);

    int updateByExample(@Param("record") Approval record, @Param("example") ApprovalExample example);

    int updateByPrimaryKeySelective(Approval record);

    int updateByPrimaryKeyWithBLOBs(Approval record);

    int updateByPrimaryKey(Approval record);

    int batchInsert(@Param("list") List<Approval> list);

    int batchInsertSelective(@Param("list") List<Approval> list, @Param("selective") Approval.Column ... selective);

    // 自定义
    List<Approval> selectByExampleWithRowbounds(ApprovalExample approvalExample, PageRowBounds pageRowBounds);
}