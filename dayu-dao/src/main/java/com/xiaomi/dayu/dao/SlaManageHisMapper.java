package com.xiaomi.dayu.dao;

import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.mybatis.entity.SlaManageHis;
import com.xiaomi.dayu.mybatis.example.SlaManageHisExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SlaManageHisMapper {
    long countByExample(SlaManageHisExample example);

    int deleteByExample(SlaManageHisExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SlaManageHis record);

    int insertSelective(SlaManageHis record);

    List<SlaManageHis> selectByExample(SlaManageHisExample example);

    SlaManageHis selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SlaManageHis record, @Param("example") SlaManageHisExample example);

    int updateByExample(@Param("record") SlaManageHis record, @Param("example") SlaManageHisExample example);

    int updateByPrimaryKeySelective(SlaManageHis record);

    int updateByPrimaryKey(SlaManageHis record);

    int batchInsert(@Param("list") List<SlaManageHis> list);

    int batchInsertSelective(@Param("list") List<SlaManageHis> list, @Param("selective") SlaManageHis.Column ... selective);

    // 自定义
    List<SlaManageHis> selectByExampleWithRowbounds(SlaManageHisExample example, PageRowBounds pageRowBounds);

}