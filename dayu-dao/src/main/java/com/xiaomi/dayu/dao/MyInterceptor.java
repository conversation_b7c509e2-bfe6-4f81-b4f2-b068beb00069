package com.xiaomi.dayu.dao;

import com.alibaba.fastjson.JSON;
import com.xiaomi.dayu.common.UserInfoThreadLocal;
import com.xiaomi.dayu.model.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.util.StopWatch;

@Intercepts(
        {
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),

        }
)
@Slf4j
public class MyInterceptor implements Interceptor {
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        log.error(invocation.getTarget().getClass().getName());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("time1");
        Object proceed = invocation.proceed();
        stopWatch.stop();
        stopWatch.start("time2");
        UserInfo userInfo = UserInfoThreadLocal.getUserInfo();
        log.error(JSON.toJSONString(userInfo));
        stopWatch.stop();
        String print = stopWatch.prettyPrint();
        log.error(JSON.toJSONString(print));
        return proceed;
    }
}
