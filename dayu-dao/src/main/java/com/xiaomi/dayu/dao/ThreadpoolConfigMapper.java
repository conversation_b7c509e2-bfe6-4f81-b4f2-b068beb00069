package com.xiaomi.dayu.dao;

import com.xiaomi.dayu.mybatis.entity.ThreadpoolConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ThreadpoolConfigMapper extends BaseMapper<ThreadpoolConfig, Integer, Object> {
    List<ThreadpoolConfig> queryConfigsOfApp(@Param("appName") String appName);

    List<ThreadpoolConfig> queryConfigs(@Param("id") Long id);

    List<ThreadpoolConfig> queryConfigsByAppAndPool(@Param("appName") String appName, @Param("poolName") String poolName);

    int insertConfig(ThreadpoolConfig config);

    int updateConfig(ThreadpoolConfig config);
}
