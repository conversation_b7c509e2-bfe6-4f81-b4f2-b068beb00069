package com.xiaomi.dayu.dao;


import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.api.bo.DubboServiceInfoRes;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.mybatis.example.NamingInstanceExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;
import java.util.Set;

public interface NamingInstanceMapper {
    long countByExample(NamingInstanceExample example);

    int deleteByExample(NamingInstanceExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(NamingInstance record);

    int insertSelective(NamingInstance record);

    List<NamingInstance> selectByExampleWithRowbounds(NamingInstanceExample example, RowBounds rowBounds);

    List<NamingInstance> selectByExampleWithDistinctRowbounds(NamingInstanceExample example, RowBounds rowBounds);
    List<NamingInstance> selectByExampleWithDistinctNORowbounds(NamingInstanceExample example);

    List<NamingInstance> selectByExampleWithDistinctRowboundsNoApp(NamingInstanceExample example, RowBounds rowBounds);

    List<NamingInstance> findAllSimpleInfoWithSideByPage(NamingInstanceExample example, RowBounds rowBounds);

    List<NamingInstance> selectByExample(NamingInstanceExample example);
    List<NamingInstance> selectByExampleNotWithBLOBs(NamingInstanceExample example);


    NamingInstance selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") NamingInstance record, @Param("example") NamingInstanceExample example);

    int updateByExample(@Param("record") NamingInstance record, @Param("example") NamingInstanceExample example);

    int updateByPrimaryKeySelective(NamingInstance record);

    int updateByPrimaryKey(NamingInstance record);

    int batchInsert(@Param("list") List<NamingInstance> list);

    int batchInsertSelective(@Param("list") List<NamingInstance> list, @Param("selective") NamingInstance.Column ... selective);

    Set<String> queryApplicationNames(@Param("side") String side);

    Set<String> queryServiceNames(@Param("side") String side);

    List<String> queryServicesByAddress(@Param("ip")String ip);


    List<DubboServiceInfoRes> selectServiceByExampleWithRowbounds(NamingInstanceExample example, PageRowBounds pageRowBounds);

    List<DubboServiceInfoRes> selectServiceByExampleWithNORowbounds(NamingInstanceExample example);

    List<String> selectFullServiceForHera(NamingInstanceExample example);

    List<DubboServiceInfoRes> selectDataForHera(NamingInstanceExample example);
}