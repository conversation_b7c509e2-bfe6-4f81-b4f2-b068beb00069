package com.xiaomi.dayu.dao;
import com.github.pagehelper.PageRowBounds;
import com.xiaomi.dayu.api.bo.DubboServiceInfoRes;
import com.xiaomi.dayu.mybatis.entity.NacosHistoryConfig;
import com.xiaomi.dayu.mybatis.entity.NamingInstance;
import com.xiaomi.dayu.mybatis.example.NacosHistoryConfigExample;
import com.xiaomi.dayu.mybatis.example.NamingInstanceExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;
import java.util.Set;
public interface NacosHistoryConfigMapper {
		long countByExample(NacosHistoryConfigExample example);

		int deleteByExample(NacosHistoryConfigExample example);

		int insert(NacosHistoryConfig record);

		int insertSelective(NacosHistoryConfig record);

		List<NacosHistoryConfig> selectByExampleWithBLOBs(NacosHistoryConfigExample example);

		List<NacosHistoryConfig> selectByExample(NacosHistoryConfigExample example);

		List<NacosHistoryConfig> selectByExample(NacosHistoryConfigExample example, PageRowBounds pageRowBounds);

		int updateByExampleSelective(@Param("record") NacosHistoryConfig record, @Param("example") NacosHistoryConfigExample example);

		int updateByExampleWithBLOBs(@Param("record") NacosHistoryConfig record, @Param("example") NacosHistoryConfigExample example);

		int updateByExample(@Param("record") NacosHistoryConfig record, @Param("example") NacosHistoryConfigExample example);

		int batchInsert(@Param("list") List<NacosHistoryConfig> list);

		int batchInsertSelective(@Param("list") List<NacosHistoryConfig> list, @Param("selective") NacosHistoryConfig.Column ... selective);
	}