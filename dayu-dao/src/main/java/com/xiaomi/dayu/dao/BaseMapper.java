package com.xiaomi.dayu.dao;

import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (yang<PERSON><PERSON><EMAIL>)
 * @version 1.0
 * @since 2022/1/5
 *
 * mybatis 通用 dao 模板。
 * 注: 在此接口列出的所有方法，对于具体的子类，不一定每个方法都可以使用，具体是否可以使用取决于 生成 的 xml 文件是否包含对应的 sql 语句
 * @param <T> mybatis generator 生成 baseRecord 对象
 * @param <PK> baseRecord 对象的主键，只支持单主键
 * @param <E> mybatis generator 生成 example 对象
 */
public interface BaseMapper <T,PK extends Serializable,E> {

    long countByExample(E example);

    int deleteByExample(E example);

    int deleteByPrimaryKey(PK id);

    int insert(T record);

    int insertSelective(T record);

    List<T> selectByExample(E example);

    List<T> selectByExampleWithBlob(E example);

    T selectByPrimaryKey(PK id);

    int updateByExampleSelective(@Param("record") T record, @Param("example") E example);

    int updateByExample(@Param("record") T record, @Param("example") E example);

    int updateByPrimaryKeySelective(T record);

    int updateByPrimaryKey(T record);
}
